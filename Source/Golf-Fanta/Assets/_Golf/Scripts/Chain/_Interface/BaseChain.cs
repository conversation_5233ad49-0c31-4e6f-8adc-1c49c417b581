using System;
using System.Collections.Generic;

namespace GolfGame
{
    public abstract class BaseChain
    {
        protected LinkedListNode<BaseChainHandler> CurrentHandler;

        protected LinkedList<BaseChainHandler> ChainHandlers;

        public Action finished;

        public BaseChain()
        {
            ChainHandlers = new LinkedList<BaseChainHandler>();
        }

        public virtual void AddCombatHandler(BaseChainHandler combatHandler)
        {
            if (ChainHandlers.Count == 0)
            {
                CurrentHandler = ChainHandlers.AddLast(combatHandler);
                CurrentHandler.Value.DependencyInjection(this);
            }
            else
            {
                LinkedListNode<BaseChainHandler> NewNode = ChainHandlers.AddLast(combatHandler);
                NewNode.Value.DependencyInjection(this);
            }
        }

        public virtual void StartChain()
        {
            CurrentHandler.Value.Start();
        }

        public virtual void IterateChain()
        {
            CurrentHandler = CurrentHandler.Next;
            if (CurrentHandler != null)
            {
                CurrentHandler.Value.Start();
            }
            else
            {
                finished?.Invoke();
            }
        }

        public virtual void Dispose<PERSON>hain()
        {
            ChainHandlers.Clear();
        }

        public virtual void SkipChain()
        {
            CurrentHandler.Value.Stop();
        }
    }
}
