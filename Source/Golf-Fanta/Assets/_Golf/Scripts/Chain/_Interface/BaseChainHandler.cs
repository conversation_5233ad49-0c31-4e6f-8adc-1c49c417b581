using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace GolfGame
{
    public abstract class BaseChainHandler
    {
        public abstract void DependencyInjection(BaseChain chain);

        public abstract BaseChain GetChain();

        public virtual void DataInjection(params object[] data)
        {

        }

        public virtual void Start()
        {

        }

        public virtual void Execute()
        {

        }

        public virtual void Finish()
        {

        }

        public virtual int OnEventFinish(object[] result)
        {
            return 0;
        }

        public virtual void AddListener()
        {

        }

        public virtual void RemoveListener()
        {

        }

        public virtual void Stop()
        {

        }
    }
}
