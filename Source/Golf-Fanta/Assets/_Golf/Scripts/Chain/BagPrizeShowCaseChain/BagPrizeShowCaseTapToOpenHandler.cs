using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Playables;
using UnityEngine.UIElements;

namespace GolfGame
{
    public class BagPrizeShowCaseTapToOpenHandler : BaseChainHandler
    {
        protected BagPrizeShowCaseChain Chain;

        public override void DependencyInjection(BaseChain chain)
        {
            Chain = (BagPrizeShowCaseChain)chain;
        }

        public override BaseChain GetChain()
        {
            return Chain;
        }

        PlayableDirector prizeAnimationDirector;
        PlayableDirector tapToOpenDirector;
        PlayableDirector tapToContinueDirector;
        UIDocument document;

        Button skipButton;
        VisualElement prize;
        VisualElement prizeNumberArea;
        Label ContinueButton;
        VisualElement totalCardsNumberArea;

        Label PrizeName;
        VisualElement PrizeIcon;
        Label PrizeProgression;
        Label PrizeNumber;

        Button clickOverlay;

        public override void DataInjection(params object[] data)
        {
            if (data.Length == 4
                && data[0] is UIDocument
                && data[1] is PlayableDirector
                && data[2] is PlayableDirector
                && data[3] is PlayableDirector
                )
            {
                document = (UIDocument)data[0];
                tapToOpenDirector = (PlayableDirector)data[1];
                prizeAnimationDirector = (PlayableDirector)data[2];
                tapToContinueDirector = (PlayableDirector)data[3];

                VisualElement root = document.rootVisualElement;

                skipButton = root.Q<Button>("SkipButton");
                prize = root.Q<VisualElement>("Prize");
                prizeNumberArea = root.Q<VisualElement>("PrizeNumberArea");
                ContinueButton = root.Q<Label>("ContinueButton");
                totalCardsNumberArea = root.Q<VisualElement>("TotalCardsNumberArea");

                clickOverlay = root.Q<Button>("ClickOverlay");

                PrizeName = root.Q<Label>("PrizeName");
                PrizeIcon = root.Q<VisualElement>("PrizeIcon");
                PrizeProgression = root.Q<Label>("PrizeProgressionLabel");
                PrizeNumber = root.Q<Label>("PrizeNumber");
            }
            else
            {
                Debug.LogError("BagPrizeShowCaseTapToOpenHandler: Data Injection Failed");
            }
        }

        public override void Start()
        {
            Execute();
        }

        public override void Execute()
        {
            tapToOpenDirector.Play();

            skipButton.style.visibility = Visibility.Hidden;
            prize.style.visibility = Visibility.Hidden;
            prizeNumberArea.style.visibility = Visibility.Hidden;
            ContinueButton.style.visibility = Visibility.Hidden;
            totalCardsNumberArea.style.visibility = Visibility.Hidden;

            clickOverlay.clicked += OnClick;
        }

        private void OnClick()
        {
            Finish();
        }

        public override void Finish()
        {
            tapToOpenDirector.Stop();
            clickOverlay.clicked -= OnClick;
            tapToContinueDirector.Play();
            Chain.IterateChain();
        }

        public override int OnEventFinish(object[] result)
        {
            return 0;
        }

        public override void AddListener()
        {

        }

        public override void RemoveListener()
        {

        }

        public override void Stop()
        {

        }
    }
}
