using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using _Golf.Scripts.Common.Helper;
using _Golf.Scripts.ScriptableObjects.Item;
using Kamgam.UIToolkitWorldImage.Examples;
using UnityEngine;
using UnityEngine.Playables;
using UnityEngine.UIElements;
using DG.Tweening;

namespace GolfGame
{
    public class BagPrizeShowCaseOpenSingleHandler : BaseChainHandler
    {
        protected BagPrizeShowCaseChain Chain;

        public override void DependencyInjection(BaseChain chain)
        {
            Chain = (BagPrizeShowCaseChain)chain;
        }

        public override BaseChain GetChain()
        {
            return Chain;
        }

        PlayableDirector prizeAnimationDirector;
        PlayableDirector tapToOpenDirector;
        UIDocument document;

        Button skipButton;
        VisualElement prize;
        VisualElement prizeNumberArea;
        Label ContinueButton;
        VisualElement totalCardsNumberArea;

        Button clickOverlay;
        Label tapToOpen;
        Label PrizeName;
        VisualElement PrizeIcon;
        Label PrizeProgression;
        Label PrizeNumber;
        Label CurrentCardsRemains;

        int i = 0;
        private int cardRemainAmount = 0;
        private string itemId;
        private int itemQuantity;

        public override void DataInjection(params object[] data)
        {
            if (data.Length == 6
                && data[0] is UIDocument
                && data[1] is PlayableDirector
                && data[2] is PlayableDirector
                && data[3] is string
                && data[4] is int
                && data[5] is int
            )
            {
                document = (UIDocument)data[0];
                tapToOpenDirector = (PlayableDirector)data[1];
                prizeAnimationDirector = (PlayableDirector)data[2];
                itemId = (string)data[3];
                itemQuantity = (int)data[4];
                cardRemainAmount = (int)data[5];
                VisualElement root = document.rootVisualElement;

                skipButton = root.Q<Button>("SkipButton");
                prize = root.Q<VisualElement>("Prize");
                prizeNumberArea = root.Q<VisualElement>("PrizeNumberArea");
                ContinueButton = root.Q<Label>("ContinueButton");
                totalCardsNumberArea = root.Q<VisualElement>("TotalCardsNumberArea");
                clickOverlay = root.Q<Button>("ClickOverlay");

                tapToOpen = root.Q<Label>("TapToOpen");

                PrizeName = root.Q<Label>("PrizeName");
                PrizeIcon = root.Q<VisualElement>("PrizeIcon");
                PrizeProgression = root.Q<Label>("PrizeProgressionLabel");
                PrizeNumber = root.Q<Label>("PrizeNumber");
                CurrentCardsRemains = root.Q<Label>("TotalCardsNumber");

                i = 0;
            }
            else
            {
                Debug.LogError("BagPrizeShowCaseOpenSingleHandler: Data Injection Failed");
            }
        }

        public override void Start()
        {
            Execute();
        }

        public override void Execute()
        {
            prize.SetBackgroundImage(ResourcesManager.Instance.GetItemRarity(itemId).Background);
            PrizeName.text = ResourcesManager.Instance.GetItemName(itemId);
            PrizeIcon.SetBackgroundImage(ResourcesManager.Instance.GetItemSprite(itemId));
            PrizeNumber.text = "+" + itemQuantity;
            CurrentCardsRemains.text = cardRemainAmount.ToString();
            skipButton.style.visibility = Visibility.Visible;
            ContinueButton.style.visibility = Visibility.Visible;
            totalCardsNumberArea.style.visibility = Visibility.Visible;
            tapToOpen.style.visibility = Visibility.Hidden;

            clickOverlay.clicked += OnClick;

            OnClick();
        }

        private void OnClick()
        {
            if (i == 0)
            {
                prize.style.visibility = Visibility.Visible;
                prizeNumberArea.style.visibility = Visibility.Visible;
                prizeAnimationDirector.Play();
                prizeAnimationDirector.stopped += OnStopAnimationPrize;
                i++;

                if (cardRemainAmount == 0)
                {
                    var delaySequence = DOTween.Sequence();
                    delaySequence.AppendInterval(0.5f).onComplete = () =>
                    {
                        Finish();
                    };
                }
            }
            else if (i == 1)
            {
                Finish();
            }
        }

        private void OnStopAnimationPrize(PlayableDirector director) { }

        public override void Finish()
        {
            prizeAnimationDirector.stopped -= OnStopAnimationPrize;
            clickOverlay.clicked -= OnClick;
            prizeNumberArea.style.visibility = Visibility.Hidden;
            prize.style.visibility = Visibility.Hidden;

            Chain.IterateChain();
        }

        public override int OnEventFinish(object[] result)
        {
            return 0;
        }

        public override void AddListener() { }

        public override void RemoveListener() { }

        public override void Stop()
        {
            prizeAnimationDirector.stopped -= OnStopAnimationPrize;
            clickOverlay.clicked -= OnClick;

            prize.style.visibility = Visibility.Hidden;
        }
    }
}