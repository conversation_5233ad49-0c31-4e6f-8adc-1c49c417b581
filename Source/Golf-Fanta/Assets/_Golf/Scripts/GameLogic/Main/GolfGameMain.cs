using _Golf.Scripts.Common;
using _Golf.Scripts.Core;
using _Golf.Scripts.GameLogic;
using _Golf.Scripts.UI;
using Cinemachine;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using _Golf.Physics.Data;
using _Golf.Scripts.CirclePoint;
using _Golf.Scripts.ScriptableObjects;
using TinyMessenger;
using Unity.VisualScripting;
using UnityEngine;
using UnityEngine.Rendering.Universal;
using _Golf.Scripts.ScriptableObjects.Item;
using Game.UI;

namespace GolfGame
{
    public class GolfGameMain : MonoBehaviour
    {
        private GameplayBus gameplayBus;
        
        #region action tokens
        private TinyMessageSubscriptionToken _ballSpawnToken;
        private TinyMessageSubscriptionToken _holeSpawnToken;
        private TinyMessageSubscriptionToken _previewCourseToken;
        private TinyMessageSubscriptionToken _previewCourseFinishedToken;
        private TinyMessageSubscriptionToken _ctpSetUpAction;
        private TinyMessageSubscriptionToken _ctpSetConfigAction;
        private TinyMessageSubscriptionToken _ctpDistanceNotifyAction;
        #endregion

        #region scene objects
        [SerializeField] private Ball ball;
        [SerializeField] private List<Ball> balls;
        [SerializeField] private GameObject hole;

        [SerializeField] private LineRenderer setPinBallPathLine;         // render set pin line, camera mediator
        [SerializeField] private LineRenderer ballPathLineRenderer;       // ball path line renderer, ball mediator
        [SerializeField] private LineRenderer ballPredictionLineRenderer; // draw ball flight path prediction, hit ui mediator

        [SerializeField] private DecalProjector pin;
        [SerializeField] private DecalProjector pinCenter;
        [SerializeField] private DecalProjector pinRadius;
        
        [SerializeField] private List<CirclePointBehaviour> circles;
        #endregion

        #region cameras
        // main
        private Camera MainCamera;
        private Canvas UICanvas;

        // virtual
        private CinemachineVirtualCamera FreeLookCamera;
        private CinemachineVirtualCamera BallRearLookCamera;
        private CinemachineVirtualCamera BallStartFlyLookCamera;
        private CinemachineVirtualCamera BallFlyPassCameraWaitingLookCamera;
        private CinemachineVirtualCamera BallFlyAfterFirstBounceFollowCamera;
        private CinemachineVirtualCamera BallFlyAfterFirstRollFollowCamera;

        // render texture
        private Camera DragBallCamera;
        #endregion

        private BallMediator _localBallMediator;
        private List<BallMediator> _ballMediators = new List<BallMediator>();
        private CameraMediator cameraMediator;
        private CirclePointMediator _circlePointMediator;

        #region data
        [SerializeField] private PlayerGadget playerGadget;
        #endregion

        private void Awake()
        {
            gameplayBus = GlobalSO.GameplayBus;
        }

        

        private void OnEnable()
        {
            _ballSpawnToken = ActionDispatcher.Bind<BallSpawnAction>(OnBallSpawn);
            _holeSpawnToken = ActionDispatcher.Bind<HoleSpawnAction>(OnHoleSpawn);
            _previewCourseToken = ActionDispatcher.Bind<PreviewCourseToken>(OnPreviewCourseStart);
            _previewCourseFinishedToken = ActionDispatcher.Bind<PreviewCourseFinishedToken>(OnPreviewCourseFinished);

            NewGameManager.OnResetGame += OnResetShot;
        }

        private void OnPreviewCourseStart(PreviewCourseToken obj)
        {
            pin.gameObject.SetActive(false);
            pinCenter.gameObject.SetActive(false);
            pinRadius.gameObject.SetActive(false);
        }

        private void OnDisable()
        {
            ActionDispatcher.Unbind(_ballSpawnToken);
            ActionDispatcher.Unbind(_holeSpawnToken);
            ActionDispatcher.Unbind(_previewCourseToken);
            ActionDispatcher.Unbind(_previewCourseFinishedToken);

            NewGameManager.OnResetGame -= OnResetShot;

            ExitScene();
        }

        private void OnResetShot()
        {
            ActionDispatcher.Dispatch(new CompleteResetShotAction());
            cameraMediator.ResetAim();
            ActionDispatcher.Dispatch(new ReadyToPlayAction());
        }

        void ExitScene()
        {
            RemoveMediators();
            ResetCameras();
        }

        private void RemoveMediators()
        {
            MasterManager.Instance.RemoveMediator(_localBallMediator);
            MasterManager.Instance.RemoveMediator(cameraMediator);
            if (gameplayBus.currentLogic.logicData is SinglePlayerClosestToPinSO)
            {
                MasterManager.Instance.RemoveMediator(_circlePointMediator);
            }
        }

        private void DestroyUI()
        {
            
        }

        private void ResetCameras()
        {
            CameraManager.Instance.ResetVirtualCameras();
        }

        /// <summary>
        /// Ball game object has spawned on the golf field (local or not)
        /// </summary>
        /// <param name="action"></param>
        private void OnBallSpawn(BallSpawnAction action)
        {
            // Ball spawnedBall = action.ball;
            // BallMediator ballMediator = new BallMediator(spawnedBall, hole.transform, pin.transform, ballPathLineRenderer);
            //
            // _ballMediators.Add(ballMediator);
            // balls.Add(action.ball);
            //
            // if (spawnedBall.IsLocalBall())
            // {
            //     ball = action.ball;
            //     ball = spawnedBall;
            //     _localBallMediator = ballMediator;
            // }
            //
            //
            // CheckIfSceneObjectsAreComplete();
        }
        
        private void OnHoleSpawn(HoleSpawnAction action)
        {
            hole = action.holeBehaviour.gameObject;

            CheckIfSceneObjectsAreComplete();
        }

        private void CheckIfSceneObjectsAreComplete()
        {
            if (hole == null)
            {
                return;
            }
            
            if (gameplayBus.currentLogic is MultiplayerNormalLogic)
            {
                if (balls.Count == 2)
                {
                    SceneObjectsAreComplete();
                }
                else
                {
                    return;
                }
            }
            else if (gameplayBus.currentLogic is SinglePlayerClosestToPin)
            {
                if (balls.Count == 1)
                {
                    SceneObjectsAreComplete();
                }
                else
                {
                    return;
                }
            }
            else
            {
                if (balls.Count == 1)
                {
                    SceneObjectsAreComplete();
                }
                else
                {
                    return;
                }
            }
        }

        private void SceneObjectsAreComplete()
        {
            InitCameras();
            InitMediators();
            PreviewCourse();
            //StartGameSetup();
        }

        public void InitCameras()
        {
            MainCamera = CameraManager.Instance.GetMainCamera();

            FreeLookCamera = CameraManager.Instance.GetFreeLookCamera();
            BallRearLookCamera = CameraManager.Instance.GetBallRearLookCamera();
            BallStartFlyLookCamera = CameraManager.Instance.GetBallStartFlyLookCamera();
            BallFlyPassCameraWaitingLookCamera = CameraManager.Instance.GetBallFlyPassCameraWaitingLookCamera();
            BallFlyAfterFirstBounceFollowCamera = CameraManager.Instance.GetBallFlyAfterFirstBounceFollowCamera();
            BallFlyAfterFirstRollFollowCamera = CameraManager.Instance.GetBallFlyAfterFirstRollFollowCamera();

            DragBallCamera = CameraManager.Instance.GetDragBallCamera();
        }

        public void InitMediators()
        {
            UICanvas = MasterManager.Instance.UIManager.GetUICanvas();

            UIComponentMediator golfBallSwingUIBaseMediator = MasterManager.Instance.InitUIComponent(UIComponentEnum.GameplayGolfBallSwingUIComponent);
            GameplayGolfBallSwingUIComponentMediator golfBallSwingUIMediator = (GameplayGolfBallSwingUIComponentMediator)golfBallSwingUIBaseMediator;

            UIComponentMediator golfBallPlayerBaseMediator =
                MasterManager.Instance.InitUIComponent(UIComponentEnum.GameplayPlayersInfoUIComponent);
            var meterDisplayMediator = MasterManager.Instance.InitUIComponent(UIComponentEnum.GameplayMeterDisplayUIComponent) as MeterDisplayUIComponentMediator;
            
            
            golfBallSwingUIMediator.SetCanvas(UICanvas);
            golfBallSwingUIMediator.SetPredictionLineRenderer(ballPredictionLineRenderer);
            golfBallSwingUIMediator.SetPin(pin);
            golfBallSwingUIMediator.SetHolePosition(hole);
            //golfBallSwingUIMediator.SetBall(ball);

            golfBallSwingUIMediator.FinalizeInitialization();

            UIComponentMediator gameplayEffectsUIComponentBaseMediator = MasterManager.Instance.InitUIComponent(UIComponentEnum.GameplayEffectsUIComponent);
            GameplayEffectsUIComponentMediator gameplayEffectsUIComponentMediator = (GameplayEffectsUIComponentMediator)gameplayEffectsUIComponentBaseMediator;

            gameplayEffectsUIComponentMediator.SetHolePosition(hole);

            cameraMediator = new CameraMediator();
            //cameraMediator.SetLineRenderer(setPinBallPathLine);
            meterDisplayMediator.SetLineRenderer(setPinBallPathLine);
            cameraMediator.SetPin(pin, pinCenter, pinRadius);
            cameraMediator.SetHolePosition(hole);
            //cameraMediator.SetBall(ball);
            cameraMediator.SetCamera(
                _MainCamera: MainCamera,
                _FreeLookCamera: FreeLookCamera,
                _BallRearLookCamera: BallRearLookCamera,
                _BallStartFlyLookCamera: BallStartFlyLookCamera,
                _BallFlyPassCameraWaitingLookCamera: BallFlyPassCameraWaitingLookCamera,
                _BallFlyAfterFirstBounceFollowCamera: BallFlyAfterFirstBounceFollowCamera,
                _BallFlyAfterFirstRollFollowCamera: BallFlyAfterFirstRollFollowCamera,
                _DragBallCamera: DragBallCamera
            );
            cameraMediator.SetPlayerGadget(playerGadget);
            
            // _localBallMediator = new BallMediator(ball, hole.transform, pin.transform, _BallPathLineRenderer);

            if (gameplayBus.currentLogic.logicData is SinglePlayerClosestToPinSO)
            {
                _circlePointMediator = new CirclePointMediator();
                _circlePointMediator.SetCircles(circles);
                _circlePointMediator.SetLineRenderer(setPinBallPathLine);
                _circlePointMediator.SetHole(hole.gameObject);
                MasterManager.Instance.AddMediator(_circlePointMediator);   
            }

            MasterManager.Instance.AddMediator(_localBallMediator);
            MasterManager.Instance.AddMediator(cameraMediator);
        }

        public void PreviewCourse()
        {
            if(gameplayBus.currentLogic.logicData is SinglePlayerClosestToPinSO) 
                ActionDispatcher.Dispatch(new ClosestToPinEnableAction(true));
            ActionDispatcher.Dispatch(new PreviewCourseToken());
        }

        private void OnPreviewCourseFinished(PreviewCourseFinishedToken token)
        {
            pin.gameObject.SetActive(true);
            pinCenter.gameObject.SetActive(true);
            if(gameplayBus.currentLogic.logicData is SinglePlayerClosestToPinSO) 
                ActionDispatcher.Dispatch(new ClosestToPinEnableAction(false));
            StartGameSetup();
        }

        public void StartGameSetup()
        {
            foreach (Ball curBall in balls)
            {
                ActionDispatcher.Dispatch(new PrepShotAction(curBall));
            }
            ActionDispatcher.Dispatch(new ReadyToPlayAction());
        }
    }
}
