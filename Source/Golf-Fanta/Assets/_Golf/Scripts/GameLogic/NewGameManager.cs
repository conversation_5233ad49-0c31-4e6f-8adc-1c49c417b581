using System;
using System.Collections.Generic;
using System.Linq;
using _Golf.Scripts.CirclePoint;
using _Golf.Scripts.Common;
using _Golf.Scripts.Core;
using _Golf.Scripts.Core.CourseController;
using _Golf.Scripts.Lobby;
using _Golf.Scripts.Networking.Photon.Lobby;
using _Golf.Scripts.SceneLoader;
using _Golf.Scripts.ScriptableObjects;
using GolfGame;
using GolfPhysics;
using TinyMessenger;
using UnityEngine;
using UnityEngine.Rendering.Universal;
using Avatar = _Golf.Scripts.Core.Avatar;
using Debug = UnityEngine.Debug;
using ResetGameAction = _Golf.Scripts.Common.ResetGameAction;
using Task = System.Threading.Tasks.Task;

namespace _Golf.Scripts.GameLogic
{
    public class NewGameManager : MonoBehaviour
    {
        [SerializeField] private bool isSandbox = false;
        [SerializeField] private SceneAssetReference _initializeScene;

        #region Scene Objects
        private Ball ball; // local ball
        private BallPlaceholder _placeHolder;
        private Dictionary<string, Ball> _ballsDict = new();
        private string currentModelId;

        private HoleBehaviour holeBehaviour;
        private FlagBehaviour flagBehaviour;

        private LineRenderer setPinBallPathLine;         // render set pin line, camera mediator
        private LineRenderer ballPathLineRenderer;       // ball path line renderer, ball mediator
        private LineRenderer ballPredictionLineRenderer; // draw ball flight path prediction, hit ui mediator

        private DecalProjector pin;
        private DecalProjector pinCenter;
        private DecalProjector pinRadius;

        private List<CirclePointBehaviour> circles;
        #endregion

        private BaseGolfLogic _currentLogic;

        #region Global SO
        private GameplayBus _gameplayBus;
        private PlayerGadget playerGadget;
        #endregion

        #region Local Lobby and Player
        private LocalLobby LocalLobby => _gameplayBus.localLobby;
        private List<LocalPlayer> Players => LocalLobby.LocalPlayers;
        private LocalPlayer SelfPlayer => Players.FirstOrDefault(player => player.GetId() == _gameplayBus.localPlayer.GetId());
        private bool IsSelfHost => SelfPlayer?.GetId() == LocalLobby.GetHostID();
        #endregion

        #region subscriptions
        private TinyMessageSubscriptionToken _ballPlaceHolderSpawnToken;
        private TinyMessageSubscriptionToken _lineRendererSpawnToken;
        private TinyMessageSubscriptionToken _pinSpawnToken;
        private TinyMessageSubscriptionToken _circlesSpawnToken;
        private TinyMessageSubscriptionToken _ballSpawnToken;
        private TinyMessageSubscriptionToken _holeSpawnToken;
        private TinyMessageSubscriptionToken _flagSpawnToken;

        private TinyMessageSubscriptionToken _previewCourseFinishedToken;

        private TinyMessageSubscriptionToken _ballLandedToken;
        private TinyMessageSubscriptionToken _gameOverToken;
        private TinyMessageSubscriptionToken _resetGameToken;
        private TinyMessageSubscriptionToken _playerDataChangedToken;
        private TinyMessageSubscriptionToken _localLobbyStateChangedToken;
        private TinyMessageSubscriptionToken _exitGameplayToken;
        private TinyMessageSubscriptionToken _hitBallToken;
        private TinyMessageSubscriptionToken _ballChangeToken;
        private TinyMessageSubscriptionToken _onBallFinishedToken;
        private TinyMessageSubscriptionToken _clubChangeToken;
        private TinyMessageSubscriptionToken _gearChangeToken;
        private TinyMessageSubscriptionToken _trailChangeToken;
        private TinyMessageSubscriptionToken _changeCameraMode;
        private TinyMessageSubscriptionToken _pinChangeToken;
        private TinyMessageSubscriptionToken _gameReadyToPlayToken;
        private TinyMessageSubscriptionToken _rearViewCameraTransistionFinishToken;
        private TinyMessageSubscriptionToken onLocalLobbyPlayerStatusChangedToInGameAction;
        private TinyMessageSubscriptionToken onLocalLobbyPlayerStatusChangedToHitBallAction;
        private TinyMessageSubscriptionToken onLocalLobbyPlayerStatusChangedToHitReadyForShotAction;
        private TinyMessageSubscriptionToken onLocalLobbyPlayerStrokeChangedAction;
        private TinyMessageSubscriptionToken onLocalLobbyEndGame;
        private TinyMessageSubscriptionToken onLocalLobbyOpponentLeft;
        private TinyMessageSubscriptionToken onLocalLobbyRematch;
        private TinyMessageSubscriptionToken onLocalLobbyForfeit;
        private TinyMessageSubscriptionToken onCameraOrbitHoleFinishedToken;

        private TinyMessageSubscriptionToken onPlayerSyncToken;

        private TinyMessageSubscriptionToken onReceiveOpponentCommand;

        private TinyMessageSubscriptionToken onRelayDisconnected;
        #endregion

        public static Action OnResetGame;

        private void Awake()
        {
            _gameplayBus = GlobalSO.GameplayBus;
            playerGadget = GlobalSO.PlayerGadget;
        }

        private void SetUpSandbox()
        {
            if (isSandbox)
            {
                _gameplayBus.currentLogic = _gameplayBus.defaultLogicSO.CreateLogic();
                _gameplayBus.localLobby = new LocalLobby(
                    "",
                    "",
                    "",
                    "123",
                    LobbyState.Lobby,
                    true,
                    true,
                    1,
                    1
                );
                _gameplayBus.localPlayer = new LocalPlayer(
                    "123",
                    0,
                    true,
                    new PlayerDataRank(),
                    new PlayerAvatarData(),
                    null,
                    PlayerStatus.None
                );
                PlayerDataRank rank = new PlayerDataRank(); rank.rankId = "RANK_ROOKIEI"; rank.currentPoint = 222;
                _gameplayBus.localPlayer.SetPlayerRankInfo(rank);

                _gameplayBus.localLobby.LocalPlayers.Clear();
                _gameplayBus.localLobby.LocalPlayers.Add(_gameplayBus.localPlayer);

                OnGameSceneLoaded();
            }
        }

        private void OnEnable()
        {
            SetUpSandbox();

            SceneLoader.SceneLoader.OnSceneLoaded += OnGameSceneLoaded;

            _ballPlaceHolderSpawnToken = ActionDispatcher.Bind<BallPlaceHolderSpawnAction>(OnPlaceHolderReady);
            _lineRendererSpawnToken = ActionDispatcher.Bind<LineRendererSpawnAction>(OnLineRenderersReady);
            _pinSpawnToken = ActionDispatcher.Bind<PinSpawnAction>(OnPinReady);
            _circlesSpawnToken = ActionDispatcher.Bind<CirclesSpawnAction>(OnCirclesReady);
            _ballSpawnToken = ActionDispatcher.Bind<BallSpawnAction>(OnBallSpawn);
            _holeSpawnToken = ActionDispatcher.Bind<HoleSpawnAction>(OnHoleSpawn);
            _flagSpawnToken = ActionDispatcher.Bind<FlagSpawnAction>(OnFlagSpawn);

            _previewCourseFinishedToken = ActionDispatcher.Bind<PreviewCourseFinishedToken>(OnPreviewCourseFinished);

            _ballLandedToken = ActionDispatcher.Bind<BallLandedAction>(BallLanded);
            _gameOverToken = ActionDispatcher.Bind<GameOverAction>(GameOver);
            _resetGameToken = ActionDispatcher.Bind<ResetGameAction>(GameReset);
            _playerDataChangedToken = ActionDispatcher.Bind<PlayerStatusChangedToLobbyAction>(PlayerDataChanged);
            _localLobbyStateChangedToken = ActionDispatcher.Bind<LocalLobbyStateChangedAction>(OnLocalLobbyStateChanged);
            _exitGameplayToken = ActionDispatcher.Bind<ExitGameplayAction>(OnExitGamePlay);
            _hitBallToken = ActionDispatcher.Bind<HitBallAction>(OnBallHit);
            _onBallFinishedToken = ActionDispatcher.Bind<BallFinishedAction>(OnBallFinished);
            _ballChangeToken = ActionDispatcher.Bind<ChangeBallAction>(OnBallChange);
            _clubChangeToken = ActionDispatcher.Bind<ChangeClubAction>(OnClubChange);
            _gearChangeToken = ActionDispatcher.Bind<ChangeGearAction>(OnGearChange);
            _trailChangeToken = ActionDispatcher.Bind<ChangeTrailAction>(OnTrailChange);
            _changeCameraMode = ActionDispatcher.Bind<ChangeCameraModeAction>(OnCameraChangeMode);
            _pinChangeToken = ActionDispatcher.Bind<PinChangeToken>(OnPinChange);
            _gameReadyToPlayToken = ActionDispatcher.Bind<ReadyToPlayAction>(OnGameReadyToPlay);

            _rearViewCameraTransistionFinishToken = ActionDispatcher.Bind<CameraRearViewTransistionFinishAction>(OnSelfShotFinish);

            onLocalLobbyPlayerStatusChangedToInGameAction = ActionDispatcher.Bind<LocalLobbyPlayerStatusChangedToInGameAction>(OnPlayerInGameStatusChange);
            onLocalLobbyPlayerStatusChangedToHitBallAction = ActionDispatcher.Bind<LocalLobbyPlayerStatusChangedToHitBallAtion>(OnPlayerHitBallStatusChange);
            onLocalLobbyPlayerStatusChangedToHitReadyForShotAction = ActionDispatcher.Bind<LocalLobbyPlayerStatusChangedToReadyForShotAtion>(OnPlayerReadyForShotStatusChange);
            onLocalLobbyPlayerStrokeChangedAction = ActionDispatcher.Bind<LocalLobbyPlayerStrokeCountChangedAction>(OnPlayerStrokeCountChange);
            onLocalLobbyEndGame = ActionDispatcher.Bind<LocalLobbyEndGame>(EndGame);
            onLocalLobbyOpponentLeft = ActionDispatcher.Bind<LocalLobbyOpponentLeft>(OpponentLeft);
            onLocalLobbyRematch = ActionDispatcher.Bind<LocalPlayerReadyForRematch>(OnRematch);
            onCameraOrbitHoleFinishedToken = ActionDispatcher.Bind<CameraOrbitHoleFinishedToken>(CameraOrbitHoleFinished);
            onLocalLobbyForfeit = ActionDispatcher.Bind<LocalLobbyForfeitAction>(OnForfeit);

            onPlayerSyncToken = ActionDispatcher.Bind<SyncPlayerAction>(OnPlayerSync);

            onReceiveOpponentCommand = ActionDispatcher.Bind<SyncOpponentAction>(OnReceiveOpponentCommand);

            onRelayDisconnected = ActionDispatcher.Bind<RelayInactiveDisconnectAction>(OnDisconnected);
        }

        private void OnDisconnected(RelayInactiveDisconnectAction action)
        {
            _currentLogic.OnReset();
            _currentLogic.RemoveMediators();
            _currentLogic.DestroyUI();
            _currentLogic.OnRelayDisconnected(action.playerWhoDisconnected);
        }

        private void OnForfeit(LocalLobbyForfeitAction obj)
        {
            _currentLogic.OnReset();
            _currentLogic.RemoveMediators();
            _currentLogic.DestroyUI();
            _currentLogic.OnForfeit(obj.Player);
        }

        private void OnRematch(LocalPlayerReadyForRematch obj)
        {
            _currentLogic.OnReset();
        }

        private void OnDisable()
        {
            SceneLoader.SceneLoader.OnSceneLoaded -= OnGameSceneLoaded;

            ActionDispatcher.Unbind(_ballPlaceHolderSpawnToken);
            ActionDispatcher.Unbind(_lineRendererSpawnToken);
            ActionDispatcher.Unbind(_pinSpawnToken);
            ActionDispatcher.Unbind(_circlesSpawnToken);
            ActionDispatcher.Unbind(_ballSpawnToken);
            ActionDispatcher.Unbind(_holeSpawnToken);
            ActionDispatcher.Unbind(_flagSpawnToken);

            ActionDispatcher.Unbind(_previewCourseFinishedToken);

            ActionDispatcher.Unbind(_ballLandedToken);
            ActionDispatcher.Unbind(_gameOverToken);
            ActionDispatcher.Unbind(_resetGameToken);
            ActionDispatcher.Unbind(_playerDataChangedToken);
            ActionDispatcher.Unbind(_localLobbyStateChangedToken);
            ActionDispatcher.Unbind(_exitGameplayToken);
            ActionDispatcher.Unbind(_hitBallToken);

            ActionDispatcher.Unbind(_onBallFinishedToken);

            ActionDispatcher.Unbind(_changeCameraMode);
            ActionDispatcher.Unbind(_ballChangeToken);
            ActionDispatcher.Unbind(_clubChangeToken);
            ActionDispatcher.Unbind(_gearChangeToken);
            ActionDispatcher.Unbind(_trailChangeToken);
            ActionDispatcher.Unbind(_pinChangeToken);
            ActionDispatcher.Unbind(_gameReadyToPlayToken);

            ActionDispatcher.Unbind(_rearViewCameraTransistionFinishToken);

            ActionDispatcher.Unbind(onLocalLobbyPlayerStatusChangedToInGameAction);
            ActionDispatcher.Unbind(onLocalLobbyPlayerStatusChangedToHitBallAction);
            ActionDispatcher.Unbind(onLocalLobbyPlayerStatusChangedToHitReadyForShotAction);
            ActionDispatcher.Unbind(onLocalLobbyPlayerStrokeChangedAction);
            ActionDispatcher.Unbind(onLocalLobbyEndGame);
            ActionDispatcher.Unbind(onLocalLobbyOpponentLeft);
            ActionDispatcher.Unbind(onLocalLobbyRematch);
            ActionDispatcher.Unbind(onCameraOrbitHoleFinishedToken);

            ActionDispatcher.Unbind(onPlayerSyncToken);

            ActionDispatcher.Unbind(onReceiveOpponentCommand);

            ActionDispatcher.Unbind(onRelayDisconnected);

            ExitScene();
        }

        #region OnSceneLoaded
        private void OnGameSceneLoaded()
        {
            ExitScene();

            OnGameInitialize();
            HostCheckIfAllPlayersIsInGame();
        }

        private void OnGameInitialize()
        {
            _currentLogic = _gameplayBus.currentLogic;
            SelfPlayer.SetUserStatus(PlayerStatus.InGame);
            foreach (var player in Players)
            {
                if (player.IsBot)
                    player.SetUserStatus(PlayerStatus.InGame);
            }
            GlobalSO.NeedleProperties.Reset();
            _currentLogic.OnInitialize(_gameplayBus, this);
        }

        private void HostCheckIfAllPlayersIsInGame()
        {
            if (_gameplayBus.localLobby.GetHostID() != _gameplayBus.localPlayer.GetId())
            {
                return;
            }

            var players = _gameplayBus.localLobby.LocalPlayers;
            bool isAllInGame = players.All(player => player.GetUserStatus() == PlayerStatus.InGame);

            if (!isAllInGame) { return; }

            _gameplayBus.localLobby.SetLocalLobbyState(LobbyState.InGame);

            if (_gameplayBus.lobbyHandler == null) { return; }

            _ = _gameplayBus.lobbyHandler.SyncLobbyData(_gameplayBus.localLobby);
        }
        #endregion

        #region On Scene Objects Spawned
        private void OnPlaceHolderReady(BallPlaceHolderSpawnAction ctx)
        {
            _placeHolder = ctx.ballPlaceholder;
            _ballsDict.Clear();

            _placeHolder.SetSandbox(isSandbox);

            foreach (var player in Players)
            {
                if (player.GetId() == SelfPlayer.GetId())
                {
                    playerGadget.ValidateBallData();
                }
                Ball ball = _placeHolder.ConfigureBall(player, playerGadget.ChosenBall.ItemId);
                currentModelId = playerGadget.ChosenBall.ItemId;
                ball.SetSandbox(isSandbox);

                _ballsDict.Add(player.GetId(), ball);
                _currentLogic.OnBallInitialized(ball);
            }
        }

        private void OnLineRenderersReady(LineRendererSpawnAction ctx)
        {
            setPinBallPathLine = ctx.aimPointLineRenderer;
            ballPathLineRenderer = ctx.ballPathLineRenderer;
            ballPredictionLineRenderer = ctx.ballPredictionLineRenderer;

            _currentLogic.OnLineRenderersInitialized(setPinBallPathLine, ballPathLineRenderer, ballPredictionLineRenderer);
        }

        private void OnPinReady(PinSpawnAction ctx)
        {
            pin = ctx.pin;
            pinCenter = ctx.pinCenter;
            pinRadius = ctx.pinRadius;

            _currentLogic.OnPinInitialized(pin, pinCenter, pinRadius);
        }

        private void OnCirclesReady(CirclesSpawnAction ctx)
        {
            circles = ctx.circles;

            _currentLogic.OnCirclesInitialized(circles);
        }

        private void OnBallSpawn(BallSpawnAction ctx)
        {
            Ball spawnedBall = ctx.ball;

            if (spawnedBall.IsLocalBall())
            {
                ball = spawnedBall;
            }
        }

        private void OnHoleSpawn(HoleSpawnAction ctx)
        {
            holeBehaviour = ctx.holeBehaviour;

            _currentLogic.OnHoleInitialized(holeBehaviour);
        }

        private void OnFlagSpawn(FlagSpawnAction ctx)
        {
            flagBehaviour = ctx.flagBehaviour;

            _currentLogic.OnFlagInitialized(flagBehaviour);
        }
        #endregion

        private void OnPreviewCourseFinished(PreviewCourseFinishedToken token)
        {
            _currentLogic.OnPreviewCourseFinished();
        }

        #region Exit scene
        void ExitScene()
        {
            if (_currentLogic == null) return;

            _currentLogic.RemoveMediators();
            CameraManager.Instance.ResetVirtualCameras();
            _currentLogic.DestroyUI();
        }
        #endregion

        private void OnGameReadyToPlay(ReadyToPlayAction token)
        {
            var bots = _gameplayBus.localLobby.LocalPlayers.Where(player => player.IsBot);
            foreach (var bot in bots)
            {
                bot.SetUserStatus(PlayerStatus.ReadyForShot);
            }
            SelfPlayer.SetUserStatus(PlayerStatus.ReadyForShot);

            _ = _gameplayBus.lobbyHandler.SyncPlayerData(_gameplayBus.localPlayer);
        }
        
        private void OnPinChange(PinChangeToken token)
        {
        }

        private void OnGearChange(ChangeGearAction action)
        {
            if (GlobalSO.GameplayBus.currentLogic != null)
            {
                GlobalSO.GameplayBus.currentLogic.AddUnchangeableUponPickGearUsage(action.gear.serverData.customData);
            }
        }

        private void OnTrailChange(ChangeTrailAction action)
        {
            _ballsDict.TryGetValue(_gameplayBus.localPlayer.GetId(), out var ball);
            _placeHolder.ConfigureTrail(action.trail, ball);
        }

        private void OnClubChange(ChangeClubAction action)
        {
        }

        private void OnCameraChangeMode(ChangeCameraModeAction action)
        {
        }

        private void OnBallChange(ChangeBallAction action)
        {
            _ballsDict.TryGetValue(_gameplayBus.localPlayer.GetId(), out var ball);

            if (!currentModelId.Equals(action.ball.ItemId))
            {
                _placeHolder.ConfigureModel(action.ball.ItemId, ball); currentModelId = action.ball.ItemId;
            }
        }
        
        private async void EndGame(LocalLobbyEndGame ctx)
        {
            _currentLogic.OnEndGame();
            var timeDelay = (int)Constant.DelayTimeToChangeStage * 1000;
            await Task.Delay(timeDelay);
            ActionDispatcher.Dispatch(new ShowEndGamePanelAction());
        }
        
        private void OpponentLeft(LocalLobbyOpponentLeft ctx)
        {
            
        }

        private void CameraOrbitHoleFinished(CameraOrbitHoleFinishedToken token)
        {
            _currentLogic.OnInHoleCinemaFinished(token.ball, token.trajectory);
        }

        private void OnPlayerSync(SyncPlayerAction token)
        {
            _currentLogic.OnPlayerSync(token.playerCommand);
        }

        private void OnReceiveOpponentCommand(SyncOpponentAction action)
        {
            if (action.playerCommand is ChangeBallCommand)
            {
                foreach (var curBall in _ballsDict)
                {
                    if (curBall.Value.localPlayer.GetId() != GlobalSO.GameplayBus.localPlayer.GetId())
                    {
                        _placeHolder.ConfigureModel(((ChangeBallCommand)action.playerCommand).ballId, curBall.Value);

                        break;
                    }
                }
            }
        }

        private void PlayerDataChanged(PlayerStatusChangedToLobbyAction obj)
        {

        }

        private void OnBallHit(HitBallAction ctx)
        {
            _currentLogic.OnSelfTakeShot(SelfPlayer, ball, ctx.Input, ctx.BallTrajectory);
        }

        private void OnPlayerInGameStatusChange(LocalLobbyPlayerStatusChangedToInGameAction ctx)
        {
            int count = ctx.InGameCount;
            Debug.Log("Player in game status changed" + count);
            HostCheckIfAllPlayersIsInGame();
        }

        private void OnPlayerHitBallStatusChange(LocalLobbyPlayerStatusChangedToHitBallAtion ctx)
        {
            LocalPlayer player = ctx.LocalPlayer;

            if (_currentLogic == null) return;

            _currentLogic.OnOtherPlayerTakeShot(player);
        }

        private void OnPlayerReadyForShotStatusChange(LocalLobbyPlayerStatusChangedToReadyForShotAtion ctx)
        {
            LocalPlayer player = ctx.LocalPlayer;
            var ball = _ballsDict[player.GetId()];
            ball.CurrentSurfaceType = SurfaceParameterManager.Instance.GetSurfaceType(ball.transform.position);
            if (_currentLogic == null) return;
    
            if (player.GetId() == SelfPlayer.GetId())
            {
                _currentLogic.OnSelfReadyForShot(player);
            }
            else
            {
                _currentLogic.OnOtherPlayerReadyForShot(player);

                if (player.IsBot && IsSelfHost)
                {
                    _currentLogic.BotTakeShot(player);
                }
            }
        }

        private void OnPlayerStrokeCountChange(LocalLobbyPlayerStrokeCountChangedAction action)
        {
            
        }

        private void GameReset(ResetGameAction ctx)
        {
            OnResetGame();
        }

        private void GameOver(GameOverAction ctx)
        {
        }
        
        private void BallLanded(BallLandedAction ctx)
        {
            Debug.Log("Gameplay: Ball landed " + ctx.ball.transform.position);

            _currentLogic.OnBallLanded(ctx.ball, ctx.ballTrajectory);
        }

        private void OnSelfShotFinish(CameraRearViewTransistionFinishAction ctx)
        {
            //SelfPlayer.SetUserStatus(PlayerStatus.ReadyForShot);
        }

        private void OnLocalLobbyStateChanged(LocalLobbyStateChangedAction action)
        {
            if (_gameplayBus.localLobby.GetLocalLobbyState() == LobbyState.InGame)
            {
                _currentLogic.OnGameStart();
            }
        }

        private void OnExitGamePlay(ExitGameplayAction exitGameplay)
        {
            _currentLogic.OnExit();
        }

        private void OnBallFinished(BallFinishedAction ctx)
        {
            _currentLogic.OnBallFinished(ctx.Ball);
        }

        public void ReturnToTitle()
        {
            SceneLoader.SceneLoader.OnRequestLoadTitleScene.Invoke(_initializeScene);
        }

        #region App Life Cycle
        public void OnApplicationQuit()
        {
            _currentLogic.OnApplicationQuit();
        }
        #endregion
    }
}