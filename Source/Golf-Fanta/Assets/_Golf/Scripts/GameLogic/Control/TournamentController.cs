using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using _Golf.Scripts.Common;
using _Golf.Scripts.Core;
using _Golf.Scripts.GlobalManagers;
using _Golf.Scripts.Networking;
using _Golf.Scripts.Networking.Courses;
using _Golf.Scripts.Networking.Photon.Lobby;
using _Golf.Scripts.PlayField;
using _Golf.Scripts.SceneLoader;
using _Golf.Scripts.ScriptableObjects;
using _Golf.Scripts.ScriptableObjects.PlayField;
using _Golf.Scripts.UI;
using Cysharp.Threading.Tasks;
using GolfFantaModule.Models;
using GolfGame;
using GolfGame.API;
using TinyMessenger;
using Unity.Services.Authentication;
using Unity.Services.Economy;
using UnityEngine;
using PlayerInfo = _Golf.Scripts.Core.PlayerInfo;

public class TournamentController : BaseControllerComponent
{
    [SerializeField] private int _showScoreCardDurationInMilisec = 3000;


    private TinyMessageSubscriptionToken _onClickPlayTournamentToken;
    private TinyMessageSubscriptionToken _finishHoleToken;

    private PlayerInfo _playerInfo;
    private GameplayBus _gameplayBus;
    private PlayerSaveScriptableObject _playerSave;
    private PlayFieldScriptableObject _playField;

    private LocalLobby _localLobby;
    private LocalPlayer _localPlayer;

    private Tournament _currentTournament;
    private TournamentParticipation _currentParticipation;
    private int _currentHoleIndex;
    private CancellationTokenSource _cancelScorecardTokenSource;
    private CancellationTokenSource _cancelHoleDownloadTokenSource;
    private const float TimeOut = 10f;
    private TournamentPlaySO _tournamentGameData;

    // public Action<float> OnProgress;

    private void Awake()
    {
        _onClickPlayTournamentToken = ActionDispatcher.Bind<ClickPlayTournament>(OnClickPlayTournament);
        _finishHoleToken = ActionDispatcher.Bind<FinishHoleAction>(OnFinishHole);
    }

    private void OnDestroy()
    {
        ActionDispatcher.Unbind(_onClickPlayTournamentToken);
        ActionDispatcher.Unbind(_finishHoleToken);
    }

    public override UniTask Init(MasterManager masterManager)
    {
        MasterManager = masterManager;

        _playerInfo = GlobalSO.PlayerInfoSO.Info;
        _gameplayBus = GlobalSO.GameplayBus;
        _playerSave = GlobalSO.PlayerSaves;
        _playField = GlobalSO.PlayFieldSO;
        _tournamentGameData = GlobalSO.TournamentPlaySO;

        var playerId = AuthenticationService.Instance.PlayerId;
        _localLobby = new LocalLobby("", "", "",
            playerId, LobbyState.Lobby, true, true, 1, 1);
        _localPlayer = new LocalPlayer(playerId, _playerInfo.DisplayName, _playerInfo.H2hRank, new PlayerAvatarData(_playerInfo.Avatar.Id));
        _localLobby.AddPlayer(0, _localPlayer);

        return UniTask.CompletedTask;
    }

    public override void Reset()
    {
    }
    public async void OnClickPlayTournament(ClickPlayTournament action)
    {
        _currentTournament = action.Tournament;
        _playerSave.AllTournamentParticipation.TryGetValue(_currentTournament.Guid, out _currentParticipation);
        var hasUnfinishedGame = _currentParticipation?.HasUnfinishedGame ?? false;
        var resumeLastGame = hasUnfinishedGame && !action.ForceRestart;
        if (!resumeLastGame && !CheckAttemptAvailable())
        {
            await IAPManager.Instance.Purcharse(IAPManager.REPLAY_TOURNAMENT_TICKET_PACK);
            return;
        }

        var playerRank = _currentParticipation?.RankAtParticipate ?? GlobalSO.PlayerInfoSO.Info.H2hRank;
        var resumeScore = resumeLastGame ? _currentParticipation.ResumeAttempt.Scores : null;
        var freeRetries = _currentParticipation?.RetryAttempt ?? 3;
        _tournamentGameData.SetTournament(_currentTournament, playerRank, resumeScore, _currentParticipation);

        //var holeGuids = action.Tournament.HoleGuids;
        var holes = action.Tournament.Holes;
        var holeIndexToStart = resumeLastGame ? (_currentParticipation?.ResumeAttempt?.Scores.Count ?? 0) : 0;

        _cancelHoleDownloadTokenSource?.Dispose();
        _cancelHoleDownloadTokenSource = new CancellationTokenSource();
        var downloadOperation = MapLoader.Instance.CheckAndDownloadMap(holes[holeIndexToStart], _cancelHoleDownloadTokenSource.Token, action.OnLoadingProgress);
        await downloadOperation.Task;

        //Have cancel action
        if (action.Tournament == null)
        {
            return;
        }
        TournamentsStageScreenMediator.DisableBackButtonEvent?.Invoke();

        await ValidateTournamentParticipation(action.ForceRestart);
        StartTournament(holeIndexToStart, action.OnLoadingProgress);
        var holeDownloadSequence = holes.Skip(holeIndexToStart + 1).Concat(holes.Take(holeIndexToStart)).ToList();
        MapLoader.Instance.CheckAndDownloadMapsSequentially(holeDownloadSequence, _cancelHoleDownloadTokenSource.Token);
    }

    private async Task ValidateTournamentParticipation(bool forceRestart = false)
    {
        APIServerResponse<SerializableTournamentParticipation> response;

        var configHash = EconomyService.Instance.Configuration.GetConfigAssignmentHash();
        response = await APIGameClient.Instance.ValidateTournamentParticipation(_currentTournament.Guid, forceRestart, configHash);
        _playerSave.UpdateParticipationData(response.data);
        _currentParticipation = _playerSave.AllTournamentParticipation[_currentTournament.Guid];
        _tournamentGameData.CurrentParticipation = _currentParticipation;
        if (response.responseCode == APIResponseCode.Success)
        {
            ActionDispatcher.Dispatch(new GetPlayerCurrenciesAction());
            return;
        }

        var data = new GeneralPopupContainer(
            title: "Data Sync Issue",
            description: "We encountered a problem with your data. Please try again",
            continueButtonText: "OK",
            continueAction: () => { ActionDispatcher.Dispatch(new ExitGameplayAction()); }
        );
        MasterManager.OpenUIComponent(UIComponentEnum.GeneralPopupUIComponent, new object[] { data });
    }

    private void StartTournament(int holeIndexToStart, Action<float,string> loadingProgress = null)
    {
        loadingProgress?.Invoke(0f, "LOADING...");
        _currentHoleIndex = holeIndexToStart;
        var holeToStart = _currentTournament.Course.Holes[holeIndexToStart];

        //MasterManager.OnChangeScreen(EScreenEnum.Empty);
        _gameplayBus.localLobby = _localLobby;
        _gameplayBus.localPlayer = _localPlayer;
        ActionDispatcher.Dispatch(new RegisterLogicAction(GameMode.Tournament));

        _playField.SetCourseInfo(_currentTournament.Course);
        _playField.SetHoleInfo(holeToStart);
        MapLoader.Instance.LoadMap(holeToStart.Guid,(progress) => loadingProgress?.Invoke(progress, string.Empty));

        TournamentsStageScreenMediator.ClickPlayTournamentEvent.ClearReference();
    }

    private bool CheckAttemptAvailable()
    {
        var hasNotParticipateYet = _currentParticipation == null;

        if (hasNotParticipateYet)
            return true;
        else
            return _currentParticipation.RetryAttempt > 0 || UGSEconomy.ReplayTicketBalance > 0;
    }

    private async void OnFinishHole(FinishHoleAction action)
    {
        var nextHoleIndex = _currentHoleIndex + 1;
        var isNextHoleValid = nextHoleIndex < _currentTournament.HoleGuids.Count;

        _cancelScorecardTokenSource?.Dispose();
        _cancelScorecardTokenSource = new();

        UpdateAndShowScoreCard(_currentHoleIndex, action.StrokesCount, action.TieBreakPoint, action.LongestShotDistance, action.GolfShotDatas);

        if (isNextHoleValid)
        {
            try
            {
                await UniTask.Delay(_showScoreCardDurationInMilisec, cancellationToken: _cancelScorecardTokenSource.Token);
                MasterManager.HideUIComponentAsync(UIComponentEnum.TournamentsScoreComponentUI).Forget();
            }
            catch (OperationCanceledException)
            {
                Debug.Log("Scorecard is closed soon");
            }


            //Wait for time finish or cancel is called
            _currentHoleIndex = nextHoleIndex;
            var nextHole = _currentTournament.Course.Holes[_currentHoleIndex];

            var loadingUIContainer = new LoadingUIContainer();
            loadingUIContainer.Mode = LoadingMode.NoBackGround;
            await MasterManager.OpenUIComponent(UIComponentEnum.LoadingComponentUI, new object[] { loadingUIContainer });

            var downloadOperation = MapLoader.Instance.CheckAndDownloadMap(nextHole, _cancelHoleDownloadTokenSource.Token, loadingUIContainer.OnProgressWithMessage);
            await downloadOperation.Task;

            //Fake loading
            
            if (downloadOperation.DisplayProgress == 0f)
            {
                var loadString = LocalizationManager.Instance.GetString("gameplay_loading");
                loadingUIContainer.OnProgressWithMessage?.Invoke(0.5f, loadString);
                await UniTask.Delay(100);
                loadingUIContainer.OnProgressWithMessage?.Invoke(1f, loadString);
                await UniTask.Delay(200);
            }
            
            MasterManager.HideUIComponentAsync(UIComponentEnum.LoadingComponentUI).Forget();

            _playField.SetHoleInfo(nextHole);
            await MapLoader.Instance.LoadMapAsync(_currentTournament.HoleGuids[nextHoleIndex]);
            //TODO:Show Downloading UI with downloadOperation.Task
        }
        else
        {
            _localPlayer.SetUserStatus(PlayerStatus.FinishGame);
        }
    }

    private async void UpdateAndShowScoreCard(int holeIndex, int strokeCount, float tieBreakPoint, float longestShotDistance, List<GolfShotDataModel> shots)
    {
        var par = _playField.HoleInfo.Par;
        var isFinished = holeIndex == (_currentTournament.Course.Holes.Count - 1);
        _tournamentGameData.UpdateScore(holeIndex, par, strokeCount, tieBreakPoint);

        var dataMap = new Dictionary<string, object>()
        {
            [TournamentsScoreUIComponent.ScoresKey] = _tournamentGameData.Scores,
            [TournamentsScoreUIComponent.HoleCountKey] = _tournamentGameData.CurrentTournament.Course.Holes.Count,
            [TournamentsScoreUIComponent.HoleParKey] = _tournamentGameData.CurrentTournament.Course.Holes.Select(hole => hole.Par).ToList(),
            [TournamentsScoreUIComponent.CancelScorecardTokenKey] = _cancelScorecardTokenSource,
            [TournamentsScoreUIComponent.ViewOnlyModeKey] = isFinished // Set view-only mode when tournament is finished
        };

        object[] data = { dataMap };
        _ = MasterManager.OpenUIComponent(UIComponentEnum.TournamentsScoreComponentUI, data);

        await UniTask.WaitUntil(() => _currentParticipation != null);

        _currentParticipation.UpdateProgress(holeIndex, _tournamentGameData.Scores[holeIndex], isFinished);
        var gameStats = new GameStats();
        gameStats.longestShotDistance = longestShotDistance;

        SyncProgressToCloud(_currentParticipation, gameStats, shots);
    }

    private async void SyncProgressToCloud(TournamentParticipation participation, GameStats gameStats, List<GolfShotDataModel> shots)
    {
        var latestAttempt = participation.LatestAttempt;

        var syncResponse = await APIGameClient.Instance.SaveTournamentProgress(
            participation.TournamentGuid,
            new SerializableAttempt(latestAttempt),
            gameStats,
            shots);

        if (syncResponse.responseCode == APIResponseCode.Success)
        {
            participation.LatestAttempt?.MarkSynced();
            GlobalSO.PlayerInfoSO.UpdateInfo(syncResponse.data);
        }
        else
        {
            Debug.LogError(syncResponse.message);
        }
    }
}

