using _Golf.Scripts.CirclePoint;
using _Golf.Scripts.Common;
using _Golf.Scripts.Core;
using _Golf.Scripts.Core.CourseController;
using _Golf.Scripts.PlayField;
using _Golf.Scripts.ScriptableObjects;
using _Golf.Scripts.ScriptableObjects.PlayField;
using System.Collections.Generic;
using System.Linq;
using GolfGame;
using GolfPhysics;
using UnityEngine;
using UnityEngine.Rendering.Universal;

namespace _Golf.Scripts.GameLogic
{
    public class GolfFieldConstant
    {
        public const string TeePosition = "TeePosition";
        public const string HoleCover = "HoleCover";
        public const string PointCamera = "PointCamera";
        public const string CTPPoint = "CTP point";
        public const string GolfSceneLight = "GolfSceneLight";
        public const string SpectateCamera = "SpectateCamera";
        public const string CameraRuntimeAnimator = "CameraAnim";
        public const string Bound = "Bound";
        public const string PreviewPoint = "Preview Point";
        public const string LocalVolume = "Local Volume";
    }

    public class GameplayBinder : MonoBehaviour
    {
        [SerializeField] private bool isSandbox = false;

        #region Prefab
        [SerializeField] private GameObject _ballPlaceholder;
        [SerializeField] private GameObject _controller;
        [SerializeField] private GameObject _hole;
        [SerializeField] private GameObject _flag;

        [SerializeField] private GameObject aimPointLineRendererPrefab;         // render set pin line, camera mediator
        [SerializeField] private GameObject ballPathLineRendererPrefab;         // ball path line renderer, ball mediator
        [SerializeField] private GameObject ballPredictionLineRendererPrefab;   // draw ball flight path prediction, hit ui mediator

        [SerializeField] private GameObject pinPrefab;
        [SerializeField] private GameObject pinRadiusPrefab;
        [SerializeField] private GameObject pinCenterPrefab;

        [SerializeField] private GameObject circlesPrefab;
        #endregion

        #region Cached Instantiated GOs
        private GameObject _targetBallPlaceHolder;
        private GameObject _targetFlag;
        private GameObject _targetHole;

        private List<GameObject> cachedGOs;

        private LineRenderer aimPointLineRenderer;
        private LineRenderer ballPathLineRenderer;
        private LineRenderer ballPredictionLineRenderer;

        private DecalProjector pin;
        private DecalProjector pinCenter;
        private DecalProjector pinRadius;

        private List<CirclePointBehaviour> circles;

        private PlayFieldScriptableObject _playField;
        #endregion

        private void Start()
        {
            if (isSandbox)
            {
                OnGameSceneReady();
            }
        }

        private void OnEnable()
        {
            SceneLoader.SceneLoader.OnSceneLoaded += OnGameSceneReady;
        }

        private void OnDisable()
        {
            SceneLoader.SceneLoader.OnSceneLoaded -= OnGameSceneReady;
        }

        private void OnGameSceneReady()
        {
            MasterManager.Instance.SetRenderInterval(2);
            Reset();

            // query golf field objects on scene
            GameObject teeContainer = GameObject.Find(GolfFieldConstant.TeePosition);
            GameObject holeCoverContainer = GameObject.Find(GolfFieldConstant.HoleCover);
            GameObject pointCameraContainer = GameObject.Find(GolfFieldConstant.PointCamera);
            GameObject ctpTeeContainer = GameObject.Find(GolfFieldConstant.CTPPoint);
            GameObject cameraAnim = GameObject.Find(GolfFieldConstant.CameraRuntimeAnimator);
            GameObject bound = GameObject.Find(GolfFieldConstant.Bound);
            GameObject previewPoint = GameObject.Find(GolfFieldConstant.PreviewPoint);
            GameObject localVolume = GameObject.Find(GolfFieldConstant.LocalVolume);

            // init playfield data
            _playField = GlobalSO.PlayFieldSO;
            _playField.Init(teeContainer, ctpTeeContainer,holeCoverContainer, pointCameraContainer, previewPoint, 
                cameraAnim != null ? cameraAnim : null, bound != null ? bound : null, localVolume != null ? localVolume : null);
            var playFieldData = _playField.Data;

            _playField.SetTreeRenderers(ScanAllTreeRenderers());
            
            // init tees
            TeeingArea teeToInit = null;

            var baseGolfLogic = GlobalSO.GameplayBus.currentLogic.logicData;

            if (baseGolfLogic is SinglePlayerClosestToPinSO)
            {
                teeToInit = playFieldData.CurrentCtpTeeingArea;
            }
            else if (baseGolfLogic is MultiplayerNormalLogicSO)
            {
                int tee = GlobalSO.GameplayBus.localLobby.GetTee() - 1;
                // tee of lobby is 1 -> 5

                if (0 <= tee && tee <= playFieldData.TeeingAreas.Count)
                {
                    teeToInit = playFieldData.TeeingAreas[tee];
                    playFieldData.CurrentTeeingArea = teeToInit;
                }
                else
                {
                    Debug.LogError("Lobby tee out of bounds of golf field tees");
                    teeToInit = playFieldData.CurrentTeeingArea;
                }
            }
            else
            {
                teeToInit = playFieldData.CurrentTeeingArea;
            }
            

            InitializeGameplayBall(teeToInit.Position);
            InitializeHoleFlag(playFieldData.CurrentPin.transform.position, playFieldData.CurrentPin.GetCenterPosition());

            playFieldData.CurrentPin.GetComponent<MeshRenderer>().enabled = false;

            InitializeLineRenderers(playFieldData.CurrentPin.transform.position, playFieldData.CurrentPin.GetCenterPosition());
            InitializePin(playFieldData.CurrentPin.transform.position, playFieldData.CurrentPin.GetCenterPosition());
            if (baseGolfLogic is SinglePlayerClosestToPinSO)
            {
                InitializeCircles(playFieldData.CurrentPin.transform.position, playFieldData.CurrentPin.GetCenterPosition());
            }

            var lights = FindObjectsByType<Light>(FindObjectsInactive.Include, FindObjectsSortMode.None);
            if (lights != null)
            {
                foreach (var item in lights)
                {
                    if(item.gameObject.name.Contains(GolfFieldConstant.GolfSceneLight))
                        item.cullingMask = LayerMask.GetMask("Default", "TransparentFX", "Ignore Raycast", "Water", "UI", "GOLFFIELD", "GROUND");
                }
            }
            
            SurfaceParameterManager.Instance.ScanColliders();
            ScanTreeColliders();
        }

        private Dictionary<Collider, Renderer> ScanAllTreeRenderers()
        {
            var objectRenderers = FindObjectsByType<Renderer>(FindObjectsInactive.Include, FindObjectsSortMode.None)
                .Where(gObject => (gObject.gameObject.layer == Constant.GolFieldLayerIndex)
                                  && (gObject.gameObject.GetComponent<Collider>() != null)).ToList();
            var treeDictionary = new Dictionary<Collider, Renderer>();
            foreach (var gObjectRenderer in objectRenderers)
            {
                var gObject = gObjectRenderer.gameObject;
                var gObjectCollider = gObject.GetComponent<Collider>();
                if (gObjectCollider.name.Contains("Leaves") || gObjectCollider.name.Contains("Tree"))
                {
                    treeDictionary.TryAdd(gObjectCollider, gObjectRenderer);
                }
            }
            return treeDictionary;
        }
        
        private void Reset()
        {
            GlobalSO.PlayFieldSO.Data.Reset();

            if (_targetBallPlaceHolder)
            {
                Destroy(_targetBallPlaceHolder);
            }
            if (_targetFlag)
            {
                Destroy(_targetFlag);
            }
            if (_targetHole)
            {
                Destroy(_targetHole);
            }

            if (cachedGOs != null)
            {
                foreach (GameObject go in cachedGOs) 
                {
                    Destroy(go);
                }
            }

            cachedGOs = new List<GameObject>();
        }

        private void InitializeGameplayBall(Vector3 teePosition)
        {
            var ballPlaceholderHandler = InstantiateAsync(_ballPlaceholder, transform, Vector3.zero ,Quaternion.identity);

            ballPlaceholderHandler.completed += (obj) =>
            {
                if (!ballPlaceholderHandler.isDone) 
                { 
                    return; 
                }

                _targetBallPlaceHolder = ballPlaceholderHandler.Result[0];

                _targetBallPlaceHolder.GetComponent<BallPlaceholder>().SetStartPosition(teePosition);
            };
        }

        private void InitializeHoleFlag(Vector3 pinPosition, Vector3 pinCenterPosition)
        {
            _targetHole = Instantiate(_hole, pinCenterPosition, Quaternion.identity, transform);
            _targetFlag = Instantiate(_flag, pinCenterPosition, Quaternion.identity, transform);
            _playField.Data.Flag = _targetFlag.GetComponent<FlagBehaviour>();
        }

        #region Line renderer
        private void InitializeLineRenderers(Vector3 pinPosition, Vector3 pinCenterPosition)
        {
            var aimPointLineRendererPrefabHandler = InstantiateAsync(aimPointLineRendererPrefab, transform, Vector3.zero, ballPathLineRendererPrefab.transform.rotation);

            aimPointLineRendererPrefabHandler.completed += (obj) =>
            {
                if (!aimPointLineRendererPrefabHandler.isDone)
                {
                    return;
                }

                cachedGOs.Add(aimPointLineRendererPrefabHandler.Result[0]);

                aimPointLineRenderer = aimPointLineRendererPrefabHandler.Result[0].GetComponent<LineRenderer>();
                DispatchLineRenderers();
            };

            var ballPathLineRendererPrefabHandler = InstantiateAsync(ballPathLineRendererPrefab, transform, Vector3.zero, ballPathLineRendererPrefab.transform.rotation);

            ballPathLineRendererPrefabHandler.completed += (obj) =>
            {
                if (!ballPathLineRendererPrefabHandler.isDone)
                {
                    return;
                }

                cachedGOs.Add(ballPathLineRendererPrefabHandler.Result[0]);

                ballPathLineRenderer = ballPathLineRendererPrefabHandler.Result[0].GetComponent<LineRenderer>();
                DispatchLineRenderers();
            };

            var ballPredictionLineRendererPrefabHandler = InstantiateAsync(ballPredictionLineRendererPrefab, transform, Vector3.zero, ballPredictionLineRendererPrefab.transform.rotation);

            ballPredictionLineRendererPrefabHandler.completed += (obj) =>
            {
                if (!ballPredictionLineRendererPrefabHandler.isDone)
                {
                    return;
                }

                cachedGOs.Add(ballPredictionLineRendererPrefabHandler.Result[0]);

                ballPredictionLineRenderer = ballPredictionLineRendererPrefabHandler.Result[0].GetComponent<LineRenderer>();
                DispatchLineRenderers();
            };
        }

        private bool CheckLineRenderersSpawned()
        {
            return (aimPointLineRenderer != null && ballPathLineRenderer != null && ballPredictionLineRendererPrefab != null);
        }

        private void DispatchLineRenderers()
        {
            if (CheckLineRenderersSpawned())
            {
                ActionDispatcher.Dispatch(new LineRendererSpawnAction(aimPointLineRenderer, ballPathLineRenderer, ballPredictionLineRenderer));
            }
        }
        #endregion

        #region Pin
        private void InitializePin(Vector3 pinPosition, Vector3 pinCenterPosition)
        {
            var pinPrefabHandler = InstantiateAsync(pinPrefab, transform, pinPosition, pinPrefab.transform.rotation);

            pinPrefabHandler.completed += (obj) =>
            {
                if (!pinPrefabHandler.isDone)
                {
                    return;
                }

                cachedGOs.Add(pinPrefabHandler.Result[0]);

                pin = pinPrefabHandler.Result[0].GetComponent<DecalProjector>();
                DispatchPin();
            };

            var pinCenterPrefabHandler = InstantiateAsync(pinCenterPrefab, transform, Vector3.zero, pinCenterPrefab.transform.rotation);

            pinCenterPrefabHandler.completed += (obj) =>
            {
                if (!pinCenterPrefabHandler.isDone)
                {
                    return;
                }

                cachedGOs.Add(pinCenterPrefabHandler.Result[0]);

                pinCenter = pinCenterPrefabHandler.Result[0].GetComponent<DecalProjector>();
                DispatchPin();
            };

            var pinRadiusPrefabHandler = InstantiateAsync(pinRadiusPrefab, transform, Vector3.zero, pinRadiusPrefab.transform.rotation);

            pinRadiusPrefabHandler.completed += (obj) =>
            {
                if (!pinRadiusPrefabHandler.isDone)
                {
                    return;
                }
                cachedGOs.Add(pinRadiusPrefabHandler.Result[0]);

                pinRadius = pinRadiusPrefabHandler.Result[0].GetComponent<DecalProjector>();
                DispatchPin();
            };
        }

        private bool CheckPinSpawned()
        {
            return (pin != null && pinCenter != null && pinRadius != null);
        }

        private void DispatchPin()
        {
            if (CheckPinSpawned())
            {
                ActionDispatcher.Dispatch(new PinSpawnAction(pin, pinCenter, pinRadius));
            }
        }
        #endregion

        #region Circle
        private void InitializeCircles(Vector3 pinPosition, Vector3 pinCenterPosition)
        {
            var circlesPrefabHandler = InstantiateAsync(circlesPrefab, transform, pinPosition, Quaternion.identity);

            circlesPrefabHandler.completed += (obj) =>
            {
                if (!circlesPrefabHandler.isDone)
                {
                    return;
                }

                cachedGOs.Add(circlesPrefabHandler.Result[0]);

                circles = new List<CirclePointBehaviour>();

                int childCount = circlesPrefabHandler.Result[0].transform.childCount;
                for (int i = 0; i < childCount; i++)
                {
                    circles.Add(circlesPrefabHandler.Result[0].transform.GetChild(i).GetComponent<CirclePointBehaviour>());
                }

                ActionDispatcher.Dispatch(new CirclesSpawnAction(circles));
            };
        }
        #endregion

        private void ScanTreeColliders()
        {
            const float distanceToSpawnCollider = 500f;
            var terrains = FindObjectsByType<Terrain>(FindObjectsInactive.Include, FindObjectsSortMode.None);
            foreach (var terrain in terrains)
            {
                var terrainData = terrain.terrainData;
                if (terrainData != null)
                {
                    var treePrefabs = new Dictionary<int, GameObject>();
                var terrainWidth = terrainData.size.x;
                var terrainHeight = terrainData.size.y;
                var terrainLength = terrainData.size.z;
                
                for (int i = 0; i < terrainData.treePrototypes.Length; i++)
                {
                    var prefab = Instantiate(terrainData.treePrototypes[i].prefab);
                    prefab.SetActive(false);
                    var components = prefab.GetComponentsInChildren<Component>().ToList();
                    for (int j = components.Count-1; j >= 0; j--)
                    {
                        var component = components[j];
                        if (component is Transform)
                        {
                            components.RemoveAt(j);
                        }
                        else if (component is not Collider)
                        {
                            components.RemoveAt(j);
                            DestroyImmediate(component);
                        }
                    }
                    if(components.Count > 0) //There is at least 1 collider
                        treePrefabs.Add(i, prefab);
                }

                for (int i = 0; i < terrainData.treeInstances.Length; i++)
                {
                    var terrainTreeInstance = terrainData.treeInstances[i];
                    
                    var treeLocalPosition = new Vector3(terrainTreeInstance.position.x * terrainWidth, terrainTreeInstance.position.y * terrainHeight, terrainTreeInstance.position.z * terrainLength);
                    if (Vector3.Distance(treeLocalPosition, _targetHole.transform.position) > distanceToSpawnCollider)
                        continue;
                    if(!treePrefabs.TryGetValue(terrainTreeInstance.prototypeIndex, out var treePrefab))
                        continue;
                    
                    var treeObject = Instantiate(treePrefab, terrain.transform).transform;
                    treeObject.localPosition = treeLocalPosition;
                    treeObject.rotation = Quaternion.Euler(0f, Mathf.Rad2Deg * terrainTreeInstance.rotation, 0f);
                    var prefabLocalScale = treePrefab.transform.localScale;
                    treeObject.localScale = new Vector3(prefabLocalScale.x * terrainTreeInstance.widthScale, prefabLocalScale.y * terrainTreeInstance.heightScale, prefabLocalScale.z * terrainTreeInstance.widthScale);
                    
                    treeObject.gameObject.SetActive(true);

                    var treeColliders = treePrefab.GetComponents<Collider>();
                    foreach (var treeCollider in treeColliders)
                    {
                        SurfaceParameterManager.Instance.RegisterCollider(treeCollider);
                    }
                }   
                }
            }
        }
    }
}