using System;
using System.Collections.Generic;
using _Golf.Physics.Data;
using _Golf.Scripts.CirclePoint;
using _Golf.Scripts.Common;
using _Golf.Scripts.Common.Helper;
using _Golf.Scripts.Core.CourseController;
using _Golf.Scripts.GameLogic;
using _Golf.Scripts.Lobby;
using _Golf.Scripts.ScriptableObjects;
using _Golf.Scripts.Tracking;
using _Golf.Scripts.UI;
using Cinemachine;
using GolfFantaModule.Models;
using GolfFantaModule.Models.Economy;
using GolfGame;
using GolfGame.API;
using Newtonsoft.Json;
using UnityEngine;
using UnityEngine.Rendering.Universal;

namespace _Golf.Scripts.Core
{
    public abstract class BaseGolfLogic : IDisposable
    {
        public GolfLogicSO logicData => _logicData;
        protected GolfLogicSO _logicData;
        public PlayerSessionInfo currentPlayingPlayer;


        #region gear usage
        protected Dictionary<GearType, int> gearUsage = new Dictionary<GearType, int>();
        protected Dictionary<GearType, GearData> localOneUseConsumedOnHitGear = new Dictionary<GearType, GearData>();
        protected Dictionary<GearType, GearData> localUnchangeableUponPickGear = new Dictionary<GearType, GearData>();

        public virtual void InitializeGear()
        {
            gearUsage = new Dictionary<GearType, int>();
            localOneUseConsumedOnHitGear = new Dictionary<GearType, GearData>();
            localUnchangeableUponPickGear = new Dictionary<GearType, GearData>();
        }

        public virtual void AddGearUsage(GearData gearData)
        {
            if (gearData == null) return;

            if(gearData.Id != null)
                TrackingManager.Instance.TrackGearConsume(gearData.Id, GlobalSO.PlayerInfoSO.Info.H2hRank.Id);
            if (gearUsage.ContainsKey(gearData.type))
            {
                gearUsage[gearData.type] = gearUsage[gearData.type] + 1;
            }
            else
            {
                gearUsage.Add(gearData.type, 1);
            }
        }

        public virtual bool CheckIfGameModeAllowThisGear(GearData gearData)
        {
            return true;
        }

        public virtual void AddOneUseConsumedOnHitGearUsage(GearData gearData, bool isLocal)
        {
            if (gearData == null) return;

            if (gearData.IsOneUseConsumedOnHitGear())
            {
                if (isLocal)
                {
                    if (!localOneUseConsumedOnHitGear.ContainsKey(gearData.type))
                    {
                        localOneUseConsumedOnHitGear.Add(gearData.type, gearData);
                    }
                }
            }
        }

        public virtual bool IsOneUseConsumedOnHitGearUsed(GearData gearData, bool isLocal)
        {
            if (isLocal)
            {
                return localOneUseConsumedOnHitGear.ContainsKey(gearData.type);
            }
            else
            {
                return false;
            }
        }

        public virtual void AddUnchangeableUponPickGearUsage(GearData gearData)
        {
            if (gearData == null) return;

            if (localUnchangeableUponPickGear.Count >= 1) return;

            if (gearData.IsUnchangeableUponPickGear())
            {
                if (!localUnchangeableUponPickGear.ContainsKey(gearData.type))
                {
                    localUnchangeableUponPickGear.Add(gearData.type, gearData);
                }
            }
        }

        public virtual bool IsAnyUnchangeableUponPickGearUsed()
        {
            return localUnchangeableUponPickGear.Count >= 1;
        }

        public virtual bool IsUnchangeableUponPickGearUsed(GearData gearData)
        {
            if (localUnchangeableUponPickGear.Count >= 1)
            {
                if (localUnchangeableUponPickGear.ContainsKey(gearData.type))
                {
                    return true;
                }
                else
                {
                    return false;
                }
            }
            return false;
        }
        #endregion

        public BaseGolfLogic() { }
        public BaseGolfLogic(GolfLogicSO logicData)
        {
            _logicData = logicData;
        }

        #region Activity registration and datas alike

        /// <summary>
        /// Flag to check if the activity is registered
        /// </summary>
        public bool activityRegistered = false;

        /// <summary>
        /// Initialize the activity, every game mode does this differently
        /// </summary>
        public virtual void InitializeActivity() 
        {

        }

        /// <summary>
        /// Register the activity, set activity flag to true, this game mode cannot register an activity anymore
        /// </summary>
        public virtual void RegisterActivity() 
        {
            
        }

        /// <summary>
        /// When the activity is finished, set the activity flag to false
        /// Every game mode ends its activity differently
        /// </summary>
        public virtual void UploadShotData() 
        {
            if (golfShotDatas.Count > 0)
            {
                _ = APIGameClient.Instance.AddShotDataToActivity(golfShotDatas);
            }

            golfShotDatas.Clear(); golfShotDatas = new List<GolfShotDataModel>();
        }

        public virtual void ResetActivity()
        {
            activityRegistered = false;
        }

        public List<GolfShotDataModel> golfShotDatas = new List<GolfShotDataModel>();

        public virtual void RegisterShotData(LocalPlayer player, Ball ball, GolfShotData input, BallTrajectory trajectory)
        {
            var inputParam = InputParamHelper.Get(input);

            GolfShotDataModel golfShotDataModel = new GolfShotDataModel();

            golfShotDataModel.ActivityUuid = GlobalSO.SessionSO.GetCurrentActivityUuid();
            golfShotDataModel.HoleUuid = GlobalSO.PlayFieldSO.HoleInfo.Guid;

            golfShotDataModel.Club = inputParam.CurrentClub;
            golfShotDataModel.Ball = inputParam.CurrentBall;
            golfShotDataModel.Gear = inputParam.CurrentGear;

            golfShotDataModel.BallSpeed = (float)inputParam.Velocity;
            golfShotDataModel.LaunchAngle = (float)inputParam.LaunchAngle;
            golfShotDataModel.SideAngle = (float)inputParam.SideAngle;
            golfShotDataModel.BackSpin = (float)inputParam.BackSpin;
            golfShotDataModel.SideSpin = (float)inputParam.SideSpin;

            golfShotDataModel.CarryDistance = (float)trajectory.GetFinalCarryDistance();
            golfShotDataModel.RollDistance = (float)trajectory.GetFinalRollDistance();
            golfShotDataModel.TotalDistance = (float)trajectory.GetTotalDistance();
            golfShotDataModel.OfflineDistance = (float)trajectory.GetFinalOffline();
            golfShotDataModel.MaxHeight = (float)trajectory.GetRelativeMaxHeight();

            golfShotDataModel.WeatherCondition = JsonConvert.SerializeObject(GlobalSO.PlayFieldSO.Data.Weather);
            golfShotDataModel.AdditionalData = null;

            golfShotDataModel.ShotRating = GolfShotRatingHelper.RateGolfShot((float)inputParam.SideAngle,
                GolfShotRatingHelper.FetchPerfectDegreeConfig(),GolfShotRatingHelper.FetchGreatDegreeConfig()).ToString();

            golfShotDatas.Add(golfShotDataModel);
        }

        #endregion

        public abstract void OnInitialize(GameplayBus bus, NewGameManager manager);
        
        public void SetPlayer(PlayerSessionInfo playerSessionInfo)
        {
            currentPlayingPlayer = playerSessionInfo;
        }
        public abstract void OnGameStart();
        public abstract void OnEndGame();
        public void SetData(GolfLogicSO logicSo)
        {
            _logicData = logicSo;
        }
        public abstract void OnBallLanded(Ball ball, BallTrajectory trajectory);
        public abstract void OnInHoleCinemaFinished(Ball ball, BallTrajectory trajectory);
        public abstract bool IsWon(Vector3 ballPos, Vector3 holePos);
        public abstract void OnAllTaskCompleted(List<Ball> balls, HoleBehaviour hole);
        public abstract void SetGamePlayLogicParam(GameLogicParam param);
        public abstract void OnSelfReadyForShot(LocalPlayer player);
        public abstract void OnOtherPlayerReadyForShot(LocalPlayer player);
        public abstract void OnSelfTakeShot(LocalPlayer player, Ball ball, GolfShotData input, BallTrajectory trajectory);
        public abstract void OnOtherPlayerTakeShot(LocalPlayer player);
        public abstract void BotTakeShot(LocalPlayer bot);
        public virtual void OnExit()
        {
            GlobalSO.PlayerInfoSO.UpdateAvailability(UserAvailability.Online);
        }
        public abstract void OnApplicationQuit();
        public virtual void OnReset(){ }
        public virtual void OnForfeit(LocalPlayer whoseForfeit) { }
        public abstract void OnRelayDisconnected(LocalPlayer disconnectedPlayer);
        public BaseGolfLogic(PlayerSessionInfo playerSessionInfo) { }
        public abstract void OnBallFinished(Ball ball);

        #region Scene Objects
        protected List<Ball> _balls = new();
        protected int LocalPlayerIndex => _balls.IndexOf(_balls.Find(ball => ball.IsLocalBall()));
        protected Ball localBall;
        
        protected HoleBehaviour holeBehaviour;
        protected FlagBehaviour flagBehaviour;
        
        protected LineRenderer setPinBallPathLine;         
        protected LineRenderer ballPathLineRenderer;       
        protected LineRenderer ballPredictionLineRenderer;
        
        protected DecalProjector pin;
        protected DecalProjector pinCenter;
        protected DecalProjector pinRadius;
        
        protected List<CirclePointBehaviour> circles;

        public virtual void OnBallInitialized(Ball ball)
        {
            _balls.Add(ball);

            if (ball.IsLocalBall())
            {
                localBall = ball;
            }

            OnSceneObjectsComplete();
        }

        public virtual void OnLineRenderersInitialized(LineRenderer setPinBallPathLine, LineRenderer ballPathLineRenderer, LineRenderer ballPredictionLineRenderer)
        {
            this.setPinBallPathLine = setPinBallPathLine;
            this.ballPathLineRenderer = ballPathLineRenderer;
            this.ballPredictionLineRenderer = ballPredictionLineRenderer;

            OnSceneObjectsComplete();
        }

        public virtual void OnPinInitialized(DecalProjector pin, DecalProjector pinCenter, DecalProjector pinRadius)
        {
            this.pin = pin;
            this.pinCenter = pinCenter;
            this.pinRadius = pinRadius;

            OnSceneObjectsComplete();
        }

        public virtual void OnCirclesInitialized(List<CirclePointBehaviour> circles)
        {
            this.circles = circles;

            OnSceneObjectsComplete();
        }

        public virtual void OnHoleInitialized(HoleBehaviour holeBehaviour)
        {
            this.holeBehaviour = holeBehaviour;

            OnSceneObjectsComplete();
        }

        public virtual void OnFlagInitialized(FlagBehaviour flagBehaviour)
        {
            this.flagBehaviour = flagBehaviour;

            OnSceneObjectsComplete();
        }

        public virtual bool CheckIfSceneObjectsAreComplete()
        {
            return (
                holeBehaviour != null &&
                flagBehaviour != null &&
                _balls != null && _balls.Count == 1 &&
                setPinBallPathLine != null &&
                ballPathLineRenderer != null &&
                ballPredictionLineRenderer != null &&
                pin != null &&
                pinCenter != null &&
                pinRadius != null
            );
        }

        protected virtual void OnSceneObjectsComplete()
        {
            if (CheckIfSceneObjectsAreComplete())
            {
                MasterManager.Instance.CurrentBallInCamera = LocalPlayerIndex;

                InitCameras();
                InitMediators();
                PreviewCourse();

                GlobalSO.PlayerInfoSO.UpdateAvailability(UserAvailability.InMatch);
            }
        }
        #endregion

        #region Initialize mediator
        // main
        private Camera MainCamera;
        private Canvas UICanvas;
        // virtual
        private CinemachineVirtualCamera FreeLookCamera;
        private CinemachineVirtualCamera BallRearLookCamera;
        private CinemachineVirtualCamera BallStartFlyLookCamera;
        private CinemachineVirtualCamera BallFlyPassCameraWaitingLookCamera;
        private CinemachineVirtualCamera BallFlyAfterFirstBounceFollowCamera;
        private CinemachineVirtualCamera BallFlyAfterFirstRollFollowCamera;
        // render texture
        private Camera DragBallCamera;

        private CameraBirdEyeViewDrawTrajectoryLine TrajectoryLine;

        CameraMediator cameraMediator;
        BallMediator _ballMediator;

        protected void InitCameras()
        {
            MainCamera = CameraManager.Instance.GetMainCamera();

            FreeLookCamera = CameraManager.Instance.GetFreeLookCamera();
            BallRearLookCamera = CameraManager.Instance.GetBallRearLookCamera();
            BallStartFlyLookCamera = CameraManager.Instance.GetBallStartFlyLookCamera();
            BallFlyPassCameraWaitingLookCamera = CameraManager.Instance.GetBallFlyPassCameraWaitingLookCamera();
            BallFlyAfterFirstBounceFollowCamera = CameraManager.Instance.GetBallFlyAfterFirstBounceFollowCamera();
            BallFlyAfterFirstRollFollowCamera = CameraManager.Instance.GetBallFlyAfterFirstRollFollowCamera();

            DragBallCamera = CameraManager.Instance.GetDragBallCamera();

            TrajectoryLine = CameraManager.Instance.GetTrajectoryLine();
        }

        public virtual void InitMediators()
        {
            UICanvas = MasterManager.Instance.UIManager.GetUICanvas();

            UIComponentMediator golfBallSwingUIBaseMediator = MasterManager.Instance.InitUIComponent(UIComponentEnum.GameplayGolfBallSwingUIComponent);
            GameplayGolfBallSwingUIComponentMediator golfBallSwingUIMediator = (GameplayGolfBallSwingUIComponentMediator)golfBallSwingUIBaseMediator;

            UIComponentMediator golfBallPlayerBaseMediator = MasterManager.Instance.InitUIComponent(UIComponentEnum.GameplayPlayersInfoUIComponent);
            GameplayPlayersInfoUIComponentMediator gameplayPlayersInfoUIComponentMediator = (GameplayPlayersInfoUIComponentMediator) golfBallPlayerBaseMediator;

            UIComponentMediator golfBallEffectMediator = MasterManager.Instance.InitUIComponent(UIComponentEnum.GameplayEffectsUIComponent);
            
            UIComponentMediator golfBallDebugBaseMediator = MasterManager.Instance.InitUIComponent(UIComponentEnum.GameplayDebugUIComponent);
            var meterDisplayUIComponentMediator = MasterManager.Instance.InitUIComponent(UIComponentEnum.GameplayMeterDisplayUIComponent) as MeterDisplayUIComponentMediator;
            var gadgetPickerUIComponentMediator = MasterManager.Instance.InitUIComponent(UIComponentEnum.GadgetPickerComponentUI) as GadgetPickerUIComponentMediator;

            gadgetPickerUIComponentMediator.SetHole(holeBehaviour);
            gadgetPickerUIComponentMediator.SetBallRefernceInScene(_balls);

            gameplayPlayersInfoUIComponentMediator.SetBall(_balls);

            golfBallSwingUIMediator.SetCanvas(UICanvas);
            golfBallSwingUIMediator.SetPredictionLineRenderer(ballPredictionLineRenderer);
            golfBallSwingUIMediator.SetPin(pin);
            golfBallSwingUIMediator.SetHolePosition(holeBehaviour.gameObject);
            golfBallSwingUIMediator.SetBall(_balls);

            golfBallSwingUIMediator.FinalizeInitialization();

            UIComponentMediator gameplayEffectsUIComponentBaseMediator = MasterManager.Instance.InitUIComponent(UIComponentEnum.GameplayEffectsUIComponent);
            GameplayEffectsUIComponentMediator gameplayEffectsUIComponentMediator = (GameplayEffectsUIComponentMediator)gameplayEffectsUIComponentBaseMediator;

            gameplayEffectsUIComponentMediator.SetHolePosition(holeBehaviour.gameObject);
            gameplayEffectsUIComponentMediator.SetBall(_balls);
            
            UIComponentMediator gameplayWorldUIComponentBaseMediator = MasterManager.Instance.InitUIComponent(UIComponentEnum.GameplayWorldUIComponent);
            GameplayWorldUIComponentMediator gameplayWorldUIComponentMediator = (GameplayWorldUIComponentMediator)gameplayWorldUIComponentBaseMediator;
            
            gameplayWorldUIComponentMediator.SetHolePosition(holeBehaviour.gameObject);
            
            cameraMediator = new CameraMediator();
            cameraMediator.SetLineRenderer(setPinBallPathLine, TrajectoryLine);
            cameraMediator.SetPin(pin, pinCenter, pinRadius);
            cameraMediator.SetHolePosition(holeBehaviour.gameObject);
            cameraMediator.SetBall(_balls);
            cameraMediator.SetCamera(
                _MainCamera: MainCamera,
                _FreeLookCamera: FreeLookCamera,
                _BallRearLookCamera: BallRearLookCamera,
                _BallStartFlyLookCamera: BallStartFlyLookCamera,
                _BallFlyPassCameraWaitingLookCamera: BallFlyPassCameraWaitingLookCamera,
                _BallFlyAfterFirstBounceFollowCamera: BallFlyAfterFirstBounceFollowCamera,
                _BallFlyAfterFirstRollFollowCamera: BallFlyAfterFirstRollFollowCamera,
                _DragBallCamera: DragBallCamera
            );
            cameraMediator.SetPlayerGadget(GlobalSO.PlayerGadget);

            meterDisplayUIComponentMediator.SetBall(_balls);
            meterDisplayUIComponentMediator.SetHole(holeBehaviour);
            meterDisplayUIComponentMediator.SetTrajectoryLine(TrajectoryLine);

            _ballMediator = new BallMediator(_balls, holeBehaviour.gameObject.transform, pin.transform, ballPathLineRenderer);

            MasterManager.Instance.AddMediator(_ballMediator);
            MasterManager.Instance.AddMediator(cameraMediator);
        }
        #endregion

        #region Ball Usage

        protected Dictionary<string, int> BallUsage = new();

        protected void InitializeBallUsage()
        {
            BallUsage = new Dictionary<string, int>();
            GlobalSO.PlayerGadget.ResetBallPenalties();
        }
        
        protected void AddBallToConsumeList(string ballId, bool isPenalty = false)
        {
            if(isPenalty) GlobalSO.PlayerGadget.AddToBallPenalties(ballId);
            if (BallUsage.ContainsKey(ballId))
            {
                if (isPenalty)
                {
                    TrackingManager.Instance.TrackBallConsume(ballId, GlobalSO.PlayerInfoSO.Info.H2hRank.Id);
                    BallUsage[ballId] += 1;
                }
            }
            else
            {
                TrackingManager.Instance.TrackBallConsume(ballId, GlobalSO.PlayerInfoSO.Info.H2hRank.Id);
                BallUsage.Add(ballId, 1);
            }
        }

        #endregion
        
        public virtual void PreviewCourse()
        {
            ActionDispatcher.Dispatch(new PreviewCourseToken());
        }

        public virtual void OnPreviewCourseFinished()
        {
            StartGameSetup();
        }

        private void StartGameSetup()
        {
            foreach (Ball curBall in _balls)
            {
                ActionDispatcher.Dispatch(new PrepShotAction(curBall));
            }
            ActionDispatcher.Dispatch(new ReadyToPlayAction());
        }

        public CameraMediator GetCameraMediator()
        {
            return cameraMediator;
        }

        public virtual void RemoveMediators()
        {
            MasterManager.Instance.RemoveMediator(_ballMediator);
            MasterManager.Instance.RemoveMediator(cameraMediator);
        }

        public virtual void DestroyUI()
        {
            MasterManager.Instance.DestroyUIComponent(UIComponentEnum.GameplayGolfBallSwingUIComponent);
            MasterManager.Instance.DestroyUIComponent(UIComponentEnum.GameplayDebugUIComponent);
            MasterManager.Instance.DestroyUIComponent(UIComponentEnum.GameplayPlayersInfoUIComponent);
            MasterManager.Instance.DestroyUIComponent(UIComponentEnum.GameplayEffectsUIComponent);
            MasterManager.Instance.DestroyUIComponent(UIComponentEnum.GameplayWorldUIComponent);
            MasterManager.Instance.DestroyUIComponent(UIComponentEnum.GadgetPickerComponentUI);
            MasterManager.Instance.DestroyUIComponent(UIComponentEnum.GameplayMeterDisplayUIComponent);
        }

        internal virtual void RegisterStageScreen()
        {
            MasterManager.Instance.RegisterCacheScreen(GolfUtility.GetReturnScreen(_logicData.gameMode));
            
        }

        public abstract void OnPlayerSync(PlayerCommand playerCommand);

        public virtual void Dispose()
        {
            
        }

    }

    public class GameLogicParam : IResettable
    {
        public int maximumStrokeCount { get; set; }

        public void Reset() { }
    }
}