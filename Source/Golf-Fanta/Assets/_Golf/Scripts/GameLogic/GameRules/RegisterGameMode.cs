using _Golf.Scripts.Common;
using _Golf.Scripts.Core;
using _Golf.Scripts.ScriptableObjects;
using AYellowpaper.SerializedCollections;
using Cysharp.Threading.Tasks;
using GolfGame;
using TinyMessenger;
using UnityEngine;
namespace _Golf.Scripts.SceneLoader
{
    public class RegisterGameMode : BaseControllerComponent
    {
        [SerializeField] private SerializedDictionary<GameMode,GolfLogicSO> _gameModeDictionary = new();
        private TinyMessageSubscriptionToken _messageSubscription;

        private void OnEnable()
        {
            _messageSubscription = ActionDispatcher.Bind<RegisterLogicAction>(RegisterGameLogic);
        }

        private void OnDisable()
        {
            ActionDispatcher.Unbind(_messageSubscription);
        }
        
        public override UniTask Init(MasterManager masterManager)
        {
            MasterManager = masterManager;
            
            return UniTask.CompletedTask;
        }

        public override void Reset()
        {
        }

        private void RegisterGameLogic(RegisterLogicAction ctx)
        {
            var gameMode = ctx.GameMode;
            if (_gameModeDictionary.TryGetValue(gameMode, out var logic))
            {
                GlobalSO.GameplayBus.currentLogic?.Dispose();
                GlobalSO.GameplayBus.currentLogic = logic.CreateLogic();
            }
           
        }
    }

    public class RegisterLogicAction : ActionBase
    {
        public GameMode GameMode { get; }
        public RegisterLogicAction(GameMode gameMode)
        {
            GameMode = gameMode;
        }
    }
}