using System.Collections.Generic;
using _Golf.Scripts.Lobby;
using _Golf.Scripts.Networking.Photon.Lobby;
using UnityEngine;

namespace _Golf.Scripts.Core
{
    public class GameplayBus : ScriptableObject, IResettable
    {
        [field: SerializeField] public GolfLogicSO defaultLogicSO { get; private set; }
        public BaseGolfLogic currentLogic;
        public Dictionary<string, bool> currentInRoomPlayers = new();
        [field: SerializeField] public LocalLobby localLobby;
        public LobbyHandler lobbyHandler;
        public RelayHandler relayHandler;
        [field: SerializeField] public LocalPlayer localPlayer;
        public List<Vector3> teeToPinPath;
        public LobbyProperty currentLobbyProperty;
        public void Reset()
        {
            currentLogic = null;
            localLobby = null;
            lobbyHandler = null;
            localPlayer = null;
        }
    }
}