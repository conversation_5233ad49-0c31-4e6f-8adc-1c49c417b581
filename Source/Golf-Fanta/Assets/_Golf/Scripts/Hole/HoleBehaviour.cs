using System.Collections.Generic;
using _Golf.Scripts.Common;
using _Golf.Scripts.ScriptableObjects;
using GolfGame;
using UnityEngine;

namespace _Golf.Scripts.Core.CourseController
{
    public class HoleBehaviour : MonoBehaviour
    {
        [SerializeField] private ParticleSystem hitHolePuttingEffectFar;
        [SerializeField] private ParticleSystem hitHolePuttingEffectNear;

        [SerializeField] private ParticleSystem hitHoleImpactEffect;

        [SerializeField] private ParticleGroupBehaviour smokeEffect;

        [SerializeField] private ParticleGroupBehaviour hitHoleImpactLoopEffect;

        [SerializeField] private ParticleGroupBehaviour birdieEfx;
        [SerializeField] private ParticleGroupBehaviour eagleEfx;
        [SerializeField] private ParticleGroupBehaviour parEfx;

        [SerializeField] private ParticleGroupBehaviour holeInOneEfx;

        [SerializeField] private ParticleGroupBehaviour eagleDescendEfx;
        [SerializeField] private ParticleGroupBehaviour eagleImplosionEfx;

        [SerializeField] private ParticleGroupBehaviour hitHoleEfx;

        private BoxCollider _boxCollider;

        private void Start()
        {
            ActionDispatcher.Dispatch(new HoleSpawnAction(this));

            _boxCollider = GetComponent<BoxCollider>();

            InitiateVfxDict();
        }

        private void OnTriggerEnter(Collider other)
        {
	       var ball = other.GetComponent<Ball>();
	       if(ball == null) return;

	       ActionDispatcher.Dispatch(new BallFinishedAction(ball));
        }

        public void PlayHitHolePuttingEffect(bool isPlayed, bool isFar = true)
        {
            if (isPlayed)
            {
                ParticleSystem hitHolePuttingEffect = isFar ? hitHolePuttingEffectFar : hitHolePuttingEffectNear;
                hitHolePuttingEffect.gameObject.SetActive(isPlayed);
            }
            else
            {
                hitHolePuttingEffectFar.gameObject.SetActive(false);
                hitHolePuttingEffectNear.gameObject.SetActive(false);
            }
        }

        public void PlayEffectFinisher(int par, int stroke)
        {
            if (GlobalSO.LocalGameSetting.FinisherDebugging)
            {
                Finisher(GlobalSO.LocalGameSetting.Finisher);
                return;
            }

            if (GlobalSO.GameplayBus.currentLogic.logicData is MultiplayerNormalLogicSO or TournamentLogicSO)
            {
                GolfShotRanking point = GolfUtility.GetPoint(par, stroke);
                Finisher(point);
            }
            else
            {
                // sandbox
                Finisher(GolfShotRanking.Eagle);
            }
        }

        private void Finisher(GolfShotRanking point)
        {
            switch (point)
            {
                case GolfShotRanking.Par:
                    {
                        parEfx.Play();
                        break;
                    }
                case GolfShotRanking.Birdie:
                    {
                        birdieEfx.Play();
                        break;
                    }
                case GolfShotRanking.Eagle:
                    {
                        eagleEfx.Play();
                        break;
                    }
                case GolfShotRanking.HoleInOne:
                case GolfShotRanking.Albatross:
                case GolfShotRanking.Condor:
                case GolfShotRanking.Ostrich:
                    {
                        holeInOneEfx.Play();
                        break;
                    }
                default:
                    {
                        smokeEffect.Play();
                        break;
                    }
            }
        }

        public void PlayHitHoleImpactEffect()
        {
            hitHoleImpactEffect.Play();
        }

        public void PlayHitHoleImpactEffectLoop()
        {
            hitHoleImpactLoopEffect.Play();
        }

        public void StopHitHoleImpactEffectLoop()
        {
            hitHoleImpactLoopEffect.Stop();
        }

        public void PlaySmokeEffect()
        {
            smokeEffect.Play();
        }

        public void PlayEagleImplosion()
        {
            hitHoleEfx.Stop();
            eagleImplosionEfx.Play();
        }

        public ParticleGroupBehaviour PlayEagleDescend()
        {
            eagleDescendEfx.Play();
            return eagleDescendEfx;
        }

        public void PlayHitHoleEffect()
        {
            hitHoleEfx.Stop();
            hitHoleEfx.Play();
        }

        // debug
        public void DebugEffect(int i)
        {
            switch (i)
            {
                case 0:
                    parEfx.Play();
                    break;
                case 1:
                    birdieEfx.Play();
                    break;
                case 2:
                    eagleEfx.Play();
                    break;
                case 3:
                    holeInOneEfx.Play();
                    break;
                default:
                    parEfx.Play();
                    break;
            }
        }

        //

        [SerializeField] private ParticleGroupBehaviour bogeyVfx;
        [SerializeField] private ParticleGroupBehaviour parVfx;
        [SerializeField] private ParticleGroupBehaviour birdieVfx;
        [SerializeField] private ParticleGroupBehaviour eagleVfx;
        [SerializeField] private ParticleGroupBehaviour ace1Vfx;
        [SerializeField] private ParticleGroupBehaviour ace2Vfx;
        [SerializeField] private ParticleGroupBehaviour ace3Vfx;
        [SerializeField] private ParticleGroupBehaviour ace4Vfx;

        private Dictionary<HoleVfx, ParticleGroupBehaviour> vfxDict;

        public void InitiateVfxDict()
        {
            vfxDict = new Dictionary<HoleVfx, ParticleGroupBehaviour>
            {
                { HoleVfx.Bogey, bogeyVfx },
                { HoleVfx.Par, parVfx },
                { HoleVfx.Birdie, birdieVfx },
                { HoleVfx.Eagle, eagleVfx },
                { HoleVfx.Ace1, ace1Vfx },
                { HoleVfx.Ace2, ace2Vfx },
                { HoleVfx.Ace3, ace3Vfx },
                { HoleVfx.Ace4, ace4Vfx },
            };
        }

        public void PlayVfxOneShot(HoleVfx vfx)
        {
            if (vfxDict.TryGetValue(vfx, out ParticleGroupBehaviour particleGroupBehaviour))
            {
                particleGroupBehaviour.Play();
            }
            else
            {
                Debug.LogError($"VFX not found for {vfx}");
            }
        }
    }

    public enum HoleVfx
    {
        Bogey,
        Par,
        Birdie,
        Eagle,
        Ace1,
        Ace2,
        Ace3,
        Ace4
    }
}