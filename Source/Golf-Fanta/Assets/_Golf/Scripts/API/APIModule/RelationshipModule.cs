using System;
using _Golf.Scripts.Common;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using _Golf.Scripts.API.Models.Common;
using _Golf.Scripts.Networking;
using _Golf.Scripts.ScriptableObjects.Tours;
using Newtonsoft.Json;
using Unity.Services.CloudCode;
using Unity.Services.Economy;

namespace GolfGame.API
{
    public class RelationshipModule : APIModule
    {
        public RelationshipModule(ICloudCodeService service) : base(service)
        {

        }

        public async Task<Dictionary<string, RelatedPlayer>> GetRelationships()
        {
            var response =  await ClientToServer<string, GetRelationshipsResponseData>(APIRequestMethod.GetRelationships, string.Empty);
            return response.data.RelationshipDTOs.ToDictionary(modelDto => modelDto.PlayerId, x => new RelatedPlayer(modelDto));
        }
    }
}