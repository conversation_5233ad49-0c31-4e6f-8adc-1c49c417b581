using System;
using System.Collections.Generic;
using _Golf.Scripts.ScriptableObjects.Tours;
using Newtonsoft.Json;
using UnityEngine.Serialization;

namespace _Golf.Scripts.API.Models.Tournament
{
    [Serializable]
    public class TournamentConfig
    {
        public int tbpScoreThreshold; //When player score is lower or equal this threshold, TBP will be applied. Higher than this, no TBP.
        public int receiveRewardDurationSecond;
        
        public List<TournamentTourInfo> tournamentTours;
    }
    
    [Serializable]
    public class TournamentEntryModel
    {
        public string guid;
        public string title;
        public string startTime;
        public string endTime;
        public string minimumRankId;
        [JsonProperty("course_id")] public string courseId;
        public bool isEnabled;
        public string bannerAddress;
        public string leaderboardId;
        public string currencyId;
        public List<SerializableEntryFee> entryFees;
        public List<SerializableTournamentDifficulty> difficulties;
    }

    [Serializable]
    public class SerializableEntryFee
    {
        public string rankId;
        public int amount;
    }
    
    [Serializable]
    public class SerializableTournamentDifficulty
    {
        public string rankId;
        public float windSpeedMph;
        public float needleSpeedMultiplier;
    }

    [Serializable]
    public class TournamentRewardModel
    {
        public string rank;
        public List<TournamentRewardItem> rewards;
    }

    [Serializable]
    public class TournamentRewardItem
    {
        public int top;
        public int coin;
        public int fragment_common;
        public int fragment_rare;
        public int fragment_epic;
    }
}
