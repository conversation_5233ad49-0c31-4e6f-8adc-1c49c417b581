using _Golf.Scripts.Common;
using System.Collections.Generic;
using System.Threading.Tasks;
using _Golf.Scripts.API.Models.Economy;
using _Golf.Scripts.Core;
using _Golf.Scripts.Models.DTOs;
using _Golf.Scripts.Networking;
using Cysharp.Threading.Tasks;
using Unity.Services.CloudCode;
using Newtonsoft.Json;
using GolfFantaModule.Models.Economy;
using PlayerInventory = GolfFantaModule.Models.Economy.PlayerInventory.PlayerInventory;
using GolfFantaModule.Models.Economy.PlayerInventory;
using Unity.Services.Economy;
using GolfFantaModule.Models.Common;
using GolfFantaModule.Models;
using GolfGame.API.Models.Challenges;
using GolfGame.API.Models.PlayerMessage;
using GolfGame.API.Models.Hole;
using _Golf.Scripts.ScriptableObjects.Tours;

namespace GolfGame.API
{
    public class APIGameClient : MonoSingleton<APIGameClient>
    {
        private BagModule bagModule;
        private SpecialOfferModule specialOfferModule;
        private CommonModule commonModule;
        private MatchModule matchModule;
        private TestModule testModule;
        private TournamentModule _tournamentModule;
        private ChallengeModule _challengeModule;
        private MessageModule _messageModule;
        private TourModule _tourModule;
        private RelationshipModule _relationshipModule;

        private ClosestToPinModule closestToPinModule;

        public override void Awake()
        {
            base.Awake();
        }

        public void Initialize()
        {
            bagModule = new BagModule(CloudCodeService.Instance);
            specialOfferModule = new SpecialOfferModule(CloudCodeService.Instance);
            commonModule = new CommonModule(CloudCodeService.Instance);
            matchModule = new MatchModule(CloudCodeService.Instance);
            testModule = new TestModule(CloudCodeService.Instance);
            closestToPinModule = new ClosestToPinModule(CloudCodeService.Instance);
            _tournamentModule = new TournamentModule(CloudCodeService.Instance);
            _challengeModule = new ChallengeModule(CloudCodeService.Instance);
            _messageModule = new MessageModule(CloudCodeService.Instance);
            _tourModule = new TourModule(CloudCodeService.Instance);
        }
        
        // Challenge
        public async Task<APIServerResponse<List<ChallengeResponse>>> GetAllPlayerChallenges()
        {
            return await _challengeModule.GetAllPlayerChallenges();
        }

        public async Task<APIServerResponse<ChallengeClaimResponse>> ClaimChallengeTask(string challengeUuid, List<string> tasks)
        {
            var result = await _challengeModule.ClaimChallengeTask(challengeUuid, tasks);
            if (result.data != null)
            {
                ActionDispatcher.Dispatch(new FetchInventoryAction());
                // ActionDispatcher.Dispatch(new GetPlayerCurrenciesAction()); //delay to excute animation
            }
            return result;
        }
        
        public async Task<APIServerResponse<ChallengeClaimResponse>> ClaimChallengeMilestone(string challengeUuid, List<string> milestoneUuids)
        {
            var result = await _challengeModule.ClaimChallengeMilestone(challengeUuid, milestoneUuids);
            if (result.data != null)
            {
                ActionDispatcher.Dispatch(new FetchInventoryAction());
                // ActionDispatcher.Dispatch(new GetPlayerCurrenciesAction()); //delay to excute animation
            }
            return result;
        }

        // Messages
        public async Task<APIServerResponse<GetActivePlayerMessagesResponse>> GetActivePlayerMessages()
        {
            return await _messageModule.GetActivePlayerMessages();
        }

        public async Task<APIServerResponse<MarkReadPlayerMessagesResponse>> MarkReadPlayerMessages(List<string> messageIds)
        {
            return await _messageModule.MarkReadPlayerMessages(messageIds);
        }

        public async Task<APIServerResponse<DeletePlayerMessagesResponse>> DeletePlayerMessages(List<string> messageIds)
        {
            return await _messageModule.DeletePlayerMessages(messageIds);
        }

        public async Task<APIServerResponse<ClaimPlayerMessagesResponse>> ClaimPlayerMessages(List<string> messageIds)
        {
            var result = await _messageModule.ClaimPlayerMessages(messageIds);
            if (result.responseCode == APIResponseCode.Success)
            {
                ActionDispatcher.Dispatch(new FetchInventoryAction());
                ActionDispatcher.Dispatch(new GetPlayerCurrenciesAction());
            }
            return result;
        }

        public async Task<APIServerResponse<bool>> GetDailyBagStatus()
        {
            return await bagModule.GetDailyBagStatus();
        }

        public async Task<APIServerResponse<bool>> GetNPassStatus(string configAssignmentHash)
        {
            return await closestToPinModule.GetNPassStatus(configAssignmentHash);
        }

        public async Task<APIServerResponse<SimpleRespone>> CloseToPinPlayDefault(string configAssignmentHash)
        {
            var result = await closestToPinModule.PlayDefault(configAssignmentHash);
            if (result.data != null)
            {
                ActionDispatcher.Dispatch(new FetchInventoryAction());
                ActionDispatcher.Dispatch(new GetPlayerCurrenciesAction());
            }
            return result;
        }

        public async Task<APIServerResponse<SimpleRespone>> CloseToPinPlayLucky(string configAssignmentHash)
        {
            var result = await closestToPinModule.PlayLucky(configAssignmentHash);
            if (result.data != null)
            {
                ActionDispatcher.Dispatch(new FetchInventoryAction());
                ActionDispatcher.Dispatch(new GetPlayerCurrenciesAction());
            }
            return result;
        }

        public async Task<APIServerResponse<ClosestToPinRewardRespone>> ClosestToPinReward(ClosestToPinReciveRequestModel model)
        {
            var result = await closestToPinModule.ClosestToPinReward(model);
            if (result.data != null)
            {
                ActionDispatcher.Dispatch(new FetchInventoryAction());
                ActionDispatcher.Dispatch(new GetPlayerCurrenciesAction());
            }
            return result;
        }

        public async Task<APIServerResponse<BagRewardResponse>> GetDailyBagReward(string configHash)
        {
            var result = await bagModule.GetDailyBagReward(configHash);
            if (result.data != null)
            {
                ActionDispatcher.Dispatch(new ReceiveDailyBagAction(result.data));
                ActionDispatcher.Dispatch(new FetchInventoryAction());
                ActionDispatcher.Dispatch(new GetPlayerCurrenciesAction());
            }

            return result;
        }

        public async Task<APIServerResponse<PlayerDataBagList>> GetPlayerBagInfo()
        {
            return await bagModule.GetPlayerBagInfo(EconomyService.Instance.Configuration.GetConfigAssignmentHash());
        }

        public async Task<APIServerResponse<ReceiveBagResponse>> GetPlayerBag(string bagUuid, bool isSkipped = false)
        {
            var result = await bagModule.GetPlayerBag(bagUuid, isSkipped);
            if (result.data != null)
            {
                ActionDispatcher.Dispatch(new ReceiveInventoryBagAction(result.data));
                ActionDispatcher.Dispatch(new FetchInventoryAction());
                ActionDispatcher.Dispatch(new GetPlayerCurrenciesAction());
            }

            return result;
        }

        public async Task<APIServerResponse<List<EconomyConfigurationVirtualPurchaseModel>>> GetEconomyConfigurationVirtualPurchases(string configAssignmentHash)
        {
            return await specialOfferModule.GetEconomyConfigurationVirtualPurchases(configAssignmentHash);
        }

        public async Task<APIServerResponse<List<EconomyConfigurationRealMoneyPurchaseModel>>> GetEconomyConfigurationRealMoneyPurchases(string configAssignmentHash)
        {
            return await specialOfferModule.GetEconomyConfigurationRealMoneyPurchases(configAssignmentHash);
        }

        public async Task<APIServerResponse<List<string>>> GetSpecialOfferStatus()
        {
            return await specialOfferModule.GetSpecialOfferStatus();
        }

        public async Task<APIServerResponse<BagRewardResponse>> BuySpecialOffer(string configAssignmentHash, string offerID)
        {
            var result = await specialOfferModule.BuySpecialOffer(configAssignmentHash, offerID);
            ActionDispatcher.Dispatch(new FetchInventoryAction());
            return result;
        }

        public async Task<APIServerResponse<SimpleRespone>> BuyVirtualPurchase(string configAssignmentHash, string offerID)
        {
            var result = await specialOfferModule.BuyVirtualPurchase(configAssignmentHash, offerID);
            ActionDispatcher.Dispatch(new FetchInventoryAction());
            ActionDispatcher.Dispatch(new GetPlayerCurrenciesAction());
            return result;
        }
        
        public async Task<APIServerResponse<SimpleRespone>> BuyRealMoneyPurchase(string configAssignmentHash, string offerID)
        {
            var result = await specialOfferModule.BuyRealMoneyPurchase(configAssignmentHash, offerID);
            ActionDispatcher.Dispatch(new FetchInventoryAction());
            ActionDispatcher.Dispatch(new GetPlayerCurrenciesAction());
            return result;
        }
        
        public async Task<APIServerResponse<Dictionary<string, object>>> GetPlayerCloudSave(List<string> keysToFetch)
        {
            return await commonModule.GetPlayerCloudSaves(keysToFetch);
        }
        
        public async Task<APIServerResponse<BucketsInfo>> GetBucketsInfo(string environmentName)
        {
            return await commonModule.GetBucketsInfo(environmentName);
        }

        public async Task<APIServerResponse<SerializableTournamentParticipation>> ValidateTournamentParticipation(string tournamentGuid, bool forceRestart, string configHash)
        {
            return await _tournamentModule.ValidateParticipation(tournamentGuid, forceRestart, configHash);
        }
        
        public async Task<APIServerResponse<PlayerInfoModel>> SaveTournamentProgress(string tournamentGuid, SerializableAttempt serializableAttempt, GameStats gameStats = null, List<GolfShotDataModel> shots = null)
        {
            return await _tournamentModule.SaveTournamentProgress(tournamentGuid, serializableAttempt, gameStats, shots);
        }
        
        public async Task<APIServerResponse<TournamentLeaderboard>> GetTournamentLeaderboard(string rankId, string leaderboardId, int? offset = null, int? limit = null)
        {
            return await _tournamentModule.GetLeaderboard(rankId, leaderboardId, offset, limit);
        }
        
        /// <summary>
        /// Get archived leaderboard
        /// <param name="rankId">RankId to get the correct tier</param>
        /// <param name="leaderboardId">Id of the leaderboard to get</param>
        /// <param name="versionId">Version id of the archived leaderboard, if null or empty take the most recent</param>
        /// <param name="offset">Get from this index</param>
        /// <param name="limit">Limit how many entries to get</param>
        /// <returns></returns>
        /// </summary>
        public async Task<APIServerResponse<TournamentLeaderboard>> GetTournamentArchivedLeaderboard(string rankId, string leaderboardId, string versionId = null, int? offset = null, int? limit = null)
        {
            return await _tournamentModule.GetArchivedLeaderboard(rankId, leaderboardId, versionId, offset, limit);
        }
        
        public async Task<APIServerResponse<HeadToHeadLeaderboard>> GetH2hLeaderboard(string leaderboardId = "HeadToHead", int? offset = null, int? limit = 100)
        {
            return await matchModule.GetH2hLeaderboard(leaderboardId, offset, limit);
        }

        public async Task<APIServerResponse<HeadToHeadLeaderboard>> GetH2hLeaderboardByPlayerIds(List<string> playerIds, string leaderboardId = "HeadToHead")
        {
            return await matchModule.GetH2hLeaderboardByPlayerIds(leaderboardId, playerIds);
        }
        
        public async Task<APIServerResponse<string>> ConsumeCoinFee(string matchRankId)
        {
            return await matchModule.ConsumeCoinFee(matchRankId, EconomyService.Instance.Configuration.GetConfigAssignmentHash());
        }
        
        public async Task<APIServerResponse<SimpleRespone>> ConsumeInventoryResult(ConsumeInventoryItemModel model)
        {
            var result = await matchModule.ConsumeInventoryResult(model, EconomyService.Instance.Configuration.GetConfigAssignmentHash());
            if (result.data != null)
            {
                ActionDispatcher.Dispatch(new FetchInventoryAction());
            }
            return result;
        }
        
        public async Task<APIServerResponse<TournamentRewardResponse>> GetTournamentReward(string tournamentGuid)
        {
            return await _tournamentModule.GetReward(tournamentGuid);
        }
        
        public async Task<APIServerResponse<string>> RequestServerProcessParticipationData()
        {
            return await _tournamentModule.RequestServerProcessParticipationData();
        }

        public async Task<APIServerResponse<PlayerDataRank>> GetPlayerRank()
        {
            return await commonModule.GetPlayerRank();
        }
        
        public async Task<APIServerResponse<PlayerInfoModel>> SetPlayerAvatar(string avatarId)
        {
            return await commonModule.SetPlayerAvatar(avatarId);
        }
        
        public async Task<APIServerResponse<PlayerInfoModel>> SetPlayerName(string newName)
        {
            return await commonModule.SetPlayerName(newName);
        }

        public async Task<APIServerResponse<SimpleRespone>> SetPlayerGadget(PlayerGadgetSaveData saveData)
        {
            var result = await commonModule.SetPlayerGadget(saveData);
            return result;
        }
        
        public async Task<APIServerResponse<bool>> ValidateCurrency(string currencyId, int amount)
        {
            return await commonModule.ValidateCurrency(currencyId, amount, EconomyService.Instance.Configuration.GetConfigAssignmentHash());
        }
        
        public async Task<APIServerResponse<Dictionary<string, PlayerInfoModel>>> GetPlayersInfo(List<string> playerIds)
        {
            return await commonModule.GetPlayersInfo(playerIds);
        }
        
        public async Task<APIServerResponse<Dictionary<string, string>>> GetPlayerNames(List<string> playerIds)
        {
            return await commonModule.GetPlayerNames(playerIds);
        }
        
        public async Task<APIServerResponse<Dictionary<string, string>>> GetRelationships()
        {
            return await _relationshipModule.GetRelationships();
        }
        
        public async Task<APIServerResponse<string>> CheckDbVersionAndMigrate()
        {
            return await commonModule.CheckDbVersionAndMigrate();
        }
        
        public async Task<APIServerResponse<string>> CheckAndInitializeNewPlayer()
        {
            return await commonModule.CheckAndInitializeNewPlayer();
        }

        public async Task<APIServerResponse<HeadToHeadMatchResultResponse>> GetHeadToHeadMatchResult(HeadToHeadResultRequestModel requestData)
        {
            var result =  await matchModule.GetHeadToHeadMatchResult(requestData);
            if (result.data != null)
            {
                ActionDispatcher.Dispatch(new FetchInventoryAction());
                ActionDispatcher.Dispatch(new GetPlayerCurrenciesAction());
            }
            return result;
        }
 
        public async Task<APIServerResponse<List<BallInventoryItem>>> GetEconomyBallInventoryItems(string configAssignmentHash)
        {
            return await commonModule.GetEconomyBallInventoryItems(configAssignmentHash);
        }

        public async Task<APIServerResponse<List<ClubInventoryItem>>> GetEconomyClubInventoryItems(string configAssignmentHash)
        {
            return await commonModule.GetEconomyClubInventoryItems(configAssignmentHash);
        }

        public async Task<APIServerResponse<List<GearInventoryItem>>> GetEconomyGearInventoryItems(string configAssignmentHash)
        {
            return await commonModule.GetEconomyGearInventoryItems(configAssignmentHash);
        }
        
        public async Task<APIServerResponse<List<AvatarInventoryItem>>> GetEconomyAvatarInventoryItems(string configAssignmentHash)
        {
            return await commonModule.GetEconomyAvatarInventoryItems(configAssignmentHash);
        }

        public async Task<APIServerResponse<PlayerInventory>> GetPlayerInventory(string configAssignmentHash)
        {
            return await commonModule.GetPlayerInventory(configAssignmentHash);
        }

        public async Task<APIServerResponse<PlayerClubInventoryItem>> UpgradePlayerClubInventoryItem(string configAssignmentHash, string itemId)
        {
            var result = await commonModule.UpgradePlayerClubInventoryItem(configAssignmentHash, itemId);
            if (result.data != null)
            {
                ActionDispatcher.Dispatch(new UpdateInventoryAction(result.data));
                ActionDispatcher.Dispatch(new GetPlayerCurrenciesAction());
            }
            
            return result;
        }

        public async Task<APIServerResponse<PlayerClubInventoryItem>> RefinePlayerClubInventoryItem(string configAssignmentHash, string itemId, int refineAmount)
        {
            var result = await commonModule.RefinePlayerClubInventoryItem(configAssignmentHash, itemId, refineAmount);
            if (result.data != null)
            {
                ActionDispatcher.Dispatch(new UpdateInventoryAction(result.data));
                ActionDispatcher.Dispatch(new GetPlayerCurrenciesAction());
            }
            
            return result;
        }

        public async Task<APIServerResponse<List<PlayerBallInventoryItem>>> FailBallActionResult(string configAssignmentHash, List<string> ballIds)
        {
            var result = await matchModule.FailBallActionResult(configAssignmentHash, ballIds);
            if (result.data != null)
            {
                ActionDispatcher.Dispatch(new FetchInventoryAction());
            }
            
            return result;
        }

        public async Task<APIServerResponse<PlayerGearInventoryItem>> ConsumeGearResult(string configAssignmentHash, List<string> gearIds, List<int> gearNumbers)
        {
            var result = await matchModule.ConsumeGearResult(configAssignmentHash, gearIds, gearNumbers);
            
            return result;
        }

        public async Task<APIServerResponse<List<PlayerClubInventoryItem>>> FinishMatchClubResult(string configAssignmentHash, List<string> clubIds)
        {
            var result = await matchModule.FinishMatchClubResult(configAssignmentHash, clubIds);
            if (result.data != null)
            {
                ActionDispatcher.Dispatch(new FetchInventoryAction());
            }
            
            return result;
        }

        // old apis
        public async Task<string> GetInventory(string configAssignmentHash)
        {
            var result = await GetInventoryAPI(configAssignmentHash);

            InventoryRootResponse deserializeData = JsonConvert.DeserializeObject<InventoryRootResponse>(result);
            ActionDispatcher.Dispatch(new UpdateInventoryAction(deserializeData));
            return result;
        }

        public async Task<string> GetInventoryAPI(string configAssignmentHash)
        {
            return await CloudCodeService.Instance.CallModuleEndpointAsync<string>(
                "GolfFantaModule",
                "GetInventory",
                new Dictionary<string, object>()
                {
                    {
                        "configAssignmentHash", configAssignmentHash
                    },
                });
        }

        public async Task<RemoteContentPathInfo> GetRemoteContentPathInfo()
        {
            var result = await GetRemoteContentPathInfoAPI();
            return result;
        }

        public async Task<RemoteContentPathInfo> GetRemoteContentPathInfoAPI()
        {
            return await CloudCodeService.Instance.CallModuleEndpointAsync<RemoteContentPathInfo>(
                "BucketModule",
                "GetRemoteContentPathInfo",
                new Dictionary<string, object>()
                    { });
        }

        public async Task<APIServerResponse<PlayerInfoModel>> SetPlayerElo(string configAssignmentHash, int elo)
        {
            return await testModule.SetPlayerElo(configAssignmentHash, elo);
        }

        public async Task<APIServerResponse<bool>> SetPlayerCoins(string configAssignmentHash, int coins)
        {
            var result = await testModule.SetPlayerCoins(configAssignmentHash, coins);
            ActionDispatcher.Dispatch(new GetPlayerCurrenciesAction());
            return result;
        }

        public async Task<APIServerResponse<bool>> SetPlayerGems(string configAssignmentHash, int gems)
        {
            var result = await testModule.SetPlayerGems(configAssignmentHash, gems);
            ActionDispatcher.Dispatch(new GetPlayerCurrenciesAction());
            return result;
        }

        public async Task<APIServerResponse<bool>> SetPlayerClub(string configAssignmentHash, string clubId, int level, int fragment, int capacity)
        {
            var result = await testModule.SetPlayerClub(configAssignmentHash, clubId, level, fragment, capacity);
            ActionDispatcher.Dispatch(new FetchInventoryAction());
            return result;
        }

        public async Task<APIServerResponse<bool>> SetPlayerBall(string configAssignmentHash, string ballId, int amount)
        {
            var result = await testModule.SetPlayerBall(configAssignmentHash, ballId, amount);
            ActionDispatcher.Dispatch(new FetchInventoryAction());
            return result;
        }

        public async Task<APIServerResponse<bool>> SetPlayerGear(string configAssignmentHash, string gearId, int amount)
        {
            var result = await testModule.SetPlayerGear(configAssignmentHash, gearId, amount);
            ActionDispatcher.Dispatch(new FetchInventoryAction());
            return result;
        }

        public async Task<APIServerResponse<string>> RegisterSession()
        {
            var result = await commonModule.RegisterSession();
            return result;
        }
        
        public async Task<APIServerResponse<string>> GetHoleDownloadUrl(HoleDownloadRequestData request)
        {
            var result = await commonModule.GetHoleDownloadUrl(request);
            return result;
        }
        
        public async Task<APIServerResponse<string>> GetHoleVersion(HoleVersionRequestData request)
        {
            var result = await commonModule.GetHoleVersion(request);
            return result;
        }

        public async Task<APIServerResponse<string>> AddActivityToSession(ClientActivity activity)
        {
            var result = await commonModule.AddActivityToSession(activity);
            return result;
        }

        public async Task<APIServerResponse<string>> AddShotDataToActivity(List<GolfShotDataModel> golfShotDatas)
        {
            var result = await commonModule.AddShotDataToActivity(golfShotDatas);
            return result;
        }

        public async Task<APIServerResponse<SupabaseConfigList>> FetchSupabaseConfigs(List<string> configList)
        {
            var result = await commonModule.FetchSupabaseConfigs(configList);
            return result;
        }

        public async Task<APIServerResponse<EconomyAllConfiguration>> FetchAllEconomyConfigs(string configAssignmentHash)
        {
            var result = await commonModule.GetEconomyAllConfiguration(configAssignmentHash);
            return result;
        }
        
        public async Task<APIServerResponse<List<PlayerDTO>>> SearchPlayerByName(string playerName)
        {
            return await commonModule.SearchPlayerByName(playerName);
        }

        public async UniTask<APIServerResponse<string>> RegisterPlayerDevice(RegisterPlayerDeviceRequestData requestData)
        {
            return await commonModule.RegisterPlayerDevice(requestData);
        }
        public async Task<AssetBundleVersionResponseData> GetAssetBundleVersions(AssetBundleVersionRequestData data)
        {
            var response = await commonModule.GetAssetBundleVersions(data);
            return response.data;
        }
        
        public async Task<AssetBundleUrlsResponseData> GetAssetBundleUrls(AssetBundleUrlsRequestData data)
        {
            var response = await commonModule.GetAssetBundleUrls(data);
            return response.data;
        }

        public async Task<APIServerResponse<SendP2PGiftResponse>> SendP2PGift(string receiverUnityPlayerId)
        {
            var result = await _messageModule.SendP2PGift(receiverUnityPlayerId);
            if (result.responseCode == APIResponseCode.Success)
            {
                ActionDispatcher.Dispatch(new FetchInventoryAction());
                ActionDispatcher.Dispatch(new GetPlayerCurrenciesAction());
            }
            return result;
        }

        // Tour methods
        public async Task<APIServerResponse<List<ConfigTour>>> GetAllTours()
        {
            var result = await _tourModule.GetAllTours();
            return result;
        }
    }
}
