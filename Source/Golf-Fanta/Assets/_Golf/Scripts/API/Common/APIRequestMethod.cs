using System.Collections.Generic;

namespace GolfGame.API
{
    public enum APIRequestMethod
    {
        // bag module
        GetDailyBagStatus = 0,
        GetDailyBagReward = 1,
        GetPlayerBagInfo = 2,
        GetPlayerBag = 3,

        // special offer module
        GetSpecialOfferInformation = 100,
        BuySpecialOffer = 101,
        GetSpecialOfferStatus = 102,
        GetEconomyConfigurationVirtualPurchases = 103,
        BuyVirtualPurchase = 104,
        GetShopConfig = 105,
        BuyRealMoneyPurchase = 106,

        // common module
        GetPlayerRank = 200,
        GetEconomyBallInventoryItems = 201,
        GetEconomyClubInventoryItems = 202,
        GetEconomyConfigurationRealMoneyPurchases = 203,
        GetEconomyGearInventoryItems = 204,
        GetPlayerInventory = 205,
        UpgradePlayerClubInventoryItem = 206,
        RefinePlayerClubInventoryItem = 207,
        GetMultipleConfigs = 208,
        GetBucketsInfo = 209,
        GetMultiplePlayerCloudSaves = 210,
        GetEconomyAvatarInventoryItems = 211,
        SetPlayerAvatar = 212,
        ValidateCurrency = 213,
        SetPlayerGadget = 214,
        GetPlayersInfo = 215,
        GetPlayerNames = 216,
        CheckDbVersionAndMigrate = 217,
        SetPlayerName = 218,
        CheckAndInitializeNewPlayer = 219,
        RegisterSession = 220,
        AddActivityToSession = 221,
        AddShotDataToActivity = 222,
        SearchPlayerByName = 223,
        GetAssetBundleVersions = 224,
        GetAssetBundleUrls = 225,
        GetHoleDownloadUrl = 230,
        GetHoleVersion = 231,
        RegisterDevice = 232,
        GetAllEconomyConfig = 297,
        FetchSupabaseConfig = 298,
        GetCourseConfigByRank = 299,

        // match module
        GetHeadToHeadMatchResult = 300,
        FailBallActionResult = 301,
        FinishMatchClubResult = 302,
        ConsumeGearResult = 303,
        GetH2hLeaderboard = 304,
        GetH2hLeaderboardByPlayerIds = 305,
        ConsumeCoinFee = 306,
        ConsumeInventoryResult = 307,

        //test module
        SetPlayerElo = 400,
        SetPlayerCoins = 401,
        SetPlayerGems = 402,
        SetPlayerClub = 403,
        SetPlayerBall = 404,
        SetPlayerGear = 405,

        // closest to pin module
        GetNPassStatus = 600,
        CtpPlayLucky = 601,
        UseNpass = 602,
        GetClosesToPinConfig = 603,
        ClosestToPinReward = 604,
        GetClosesToPinConditionConfig = 605,

        //Tournament module
        ValidateParticipation = 700,
        SaveProgress = 701,
        ProcessParticipationData = 702,
        GetLeaderboard = 703,
        GetReward = 704,

        //Challenge Module
        GetAllPlayerChallenge = 901,
        ClaimChallengeTaskReward = 902,
        ClaimChallengeMilestoneReward = 903,

        //Player Message Module
        GetActivePlayerMessages = 1004,
        MarkReadPlayerMessages = 1005,
        DeletePlayerMessages = 1006,
        ClaimPlayerMessages = 1007,
        // SendP2PMessage = 1008,
        SendP2PGift = 1009,

        GetAllTours = 1200,
        
        //Relationships
        GetRelationships = 1300,
    }

    public enum APIResponseCode
    {
        Success = 0,
        Fail_SDK = 1,
        Fail_CodeIneligible = 2,
        Fail = 3,
        Fail_SessionInvalid = 4,
        Fail_SessionExpired = 5
    }
}

