using _Golf.Scripts.Core;
using _Golf.Scripts.Lobby;
using _Golf.Scripts.ScriptableObjects;
using Cinemachine;
using GolfPhysics.UnitySurface;
using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Rendering.Universal;

namespace GolfGame
{
    /// <summary>
    /// A state machine to control the state of the cinemachine brain
    /// </summary>
    public class CameraStateController : PlayerSyncable, ModuleComponent
    {
        [SerializeField] 
        private GameplayBus _gameplayBus;
        private PlayerGadget playerGadget;
        private bool active;

        #region state controller
        /// <summary>
        /// The current camera state of the state machine
        /// </summary>
        ICameraState currentState;

        /// <summary>
        /// A dictionary containing states of the state machine
        /// </summary>
        Dictionary<CameraStateEnum, ICameraState> states;

        /// <summary>
        /// The current camera state of the state machine
        /// </summary>
        ICameraState prevState;
        #endregion

        #region scene objects
        private List<Ball> ballList;
        private int LocalPlayerIndex => ballList.IndexOf(ballList.Find(ball => ball.IsLocalBall()));
        private Ball LocalBall => ballList[LocalPlayerIndex];

        private Camera mainCamera;
        private GameObject holePosition;
        private LineRenderer lineRenderer; private CameraBirdEyeViewDrawTrajectoryLine trajectoryLine;
        private DecalProjector decalProjector;
        private DecalProjector pinCenter;
        private DecalProjector pinRadius;
        #endregion

        #region Cameras
        CinemachineVirtualCamera[] virtualCameraList;
        private Dictionary<CameraStateEnum, CinemachineVirtualCamera> virtualCameras;
        #endregion

        #region mediator manager
        CameraMediator mediator;
        CameraMediatorManager manager;

        public void Init(ModuleMediator _mediator)
        {
            mediator = (CameraMediator)_mediator;
            manager = (CameraMediatorManager)_mediator.GetManager();

            manager.UpdatelineRendererEvent += OnUpdateLineRenderer;
        }

        public void Dispose()
        {
            currentState?.OnExit();
            active = false;

            manager.UpdatelineRendererEvent -= OnUpdateLineRenderer;
        }

        public CameraMediatorManager GetManager()
        {
            return manager;
        }
        #endregion

        /// <summary>
        /// Initializing camera state controller
        /// </summary>
        /// <param name="_mainCam">Main camera in the scene which also contains the cinemachine brain</param>
        /// <param name="_ball">Golf ball</param>
        /// <param name="_hole">Hole game object of the golf field</param>
        /// <param name="_lineRenderer">A line renderer used to draw prediction trajectory</param>
        /// <param name="_decalProjector">A decal projector used to put a pin on the golf field</param>
        public void Init(Camera _mainCam, 

            CinemachineVirtualCamera freeLookCamera,
            CinemachineVirtualCamera ballRearLookCam,
            CinemachineVirtualCamera ballStartFlyLookCamera,
            CinemachineVirtualCamera ballFlyPassCameraWaitingLookCamera,
            CinemachineVirtualCamera ballFlyAfterFirstBounceFollowCamera,
            CinemachineVirtualCamera ballFlyAfterFirstRollFollowCamera,

            List<Ball> balls, GameObject _hole, LineRenderer _lineRenderer, CameraBirdEyeViewDrawTrajectoryLine _trajectoryLine, DecalProjector _decalProjector, DecalProjector _pinCenter, DecalProjector _pinRadius, Camera _DragBallCamera)
        {
            mainCamera = _mainCam;
            ballList = balls;
            holePosition = _hole;
            lineRenderer = _lineRenderer;
            trajectoryLine = _trajectoryLine;
            decalProjector = _decalProjector;
            pinCenter = _pinCenter;
            pinRadius = _pinRadius;

            virtualCameraList = new CinemachineVirtualCamera[] {
                freeLookCamera,
                ballRearLookCam,
                ballStartFlyLookCamera,
                ballFlyPassCameraWaitingLookCamera,
                ballFlyAfterFirstBounceFollowCamera,
                ballFlyAfterFirstRollFollowCamera
            };

            virtualCameras = new Dictionary<CameraStateEnum, CinemachineVirtualCamera>()
            {
                {
                    CameraStateEnum.PREVIEW_COURSE, freeLookCamera
                },
                {
                    CameraStateEnum.BALL_REAR, ballRearLookCam
                },
                {
                    CameraStateEnum.BALL_BIRD_EYE, freeLookCamera
                },
                {
                    CameraStateEnum.BALL_START_FLY_LOOKAT, ballStartFlyLookCamera
                },
                {
                    CameraStateEnum.BALL_LOOK_AT_AFTER_WAITING, ballFlyPassCameraWaitingLookCamera
                },
                {
                    CameraStateEnum.BALL_FOLLOW_AFTER_WAITING, ballFlyAfterFirstBounceFollowCamera
                },
                {
                    CameraStateEnum.BALL_FOLLOW_AFTER_FIRST_ROLL, freeLookCamera
                },
                {
                    CameraStateEnum.BALL_FOLLOW_LOOK_AT_AFTER_WAITING, ballFlyPassCameraWaitingLookCamera
                },
                {
                    CameraStateEnum.BALL_FOLLOW_LOOK_AT_AFTER_WAITING_WIDE, ballFlyPassCameraWaitingLookCamera
                },
                {
                    CameraStateEnum.FOLLOW_BALL_PUTTING, freeLookCamera
                },
                {
                    CameraStateEnum.LOOKAT_BALL_PUTTING, ballFlyPassCameraWaitingLookCamera
                },
                {
                    CameraStateEnum.LOOKDOWN_BALL_PUTTING, freeLookCamera
                },
                {
                    CameraStateEnum.ORBIT_HOLE, ballRearLookCam
                },
                {
                    CameraStateEnum.ORBIT_HOLE_LOOKUP, ballRearLookCam
                },
                {
                    CameraStateEnum.ORBIT_HOLE_STATIC, ballRearLookCam
                },
                {
                    CameraStateEnum.ORBIT_HOLE_DESCEND, ballRearLookCam
                },

                { 
                    CameraStateEnum.HOLE_FINISHER_TEMPLATE, ballRearLookCam         
                },

                {
                    CameraStateEnum.DEBUG_BIRD_EYE, freeLookCamera
                },
            };

            states = new Dictionary<CameraStateEnum, ICameraState>
            {
                {
                    CameraStateEnum.PREVIEW_COURSE, new CameraPreviewCourseView(mainCamera, virtualCameras, CameraStateEnum.PREVIEW_COURSE, this)
                },
                {
                    CameraStateEnum.BALL_REAR, new CameraBallRearView(mainCamera, virtualCameras, CameraStateEnum.BALL_REAR, this)
                },
                {
                    CameraStateEnum.BALL_BIRD_EYE, new CameraBirdEyeView(mainCamera, virtualCameras, CameraStateEnum.BALL_BIRD_EYE, this, _gameplayBus)
                },
                {
                    CameraStateEnum.BALL_START_FLY_LOOKAT, new CameraBallStartFlyLookAt(mainCamera, virtualCameras, CameraStateEnum.BALL_START_FLY_LOOKAT, this)
                },
                {
                    CameraStateEnum.BALL_LOOK_AT_AFTER_WAITING, new CameraBallFlyLookAt(mainCamera, virtualCameras, CameraStateEnum.BALL_LOOK_AT_AFTER_WAITING, this)
                },
                {
                    CameraStateEnum.BALL_FOLLOW_LOOK_AT_AFTER_WAITING, new CameraBallFlyFollowLookAt(mainCamera, virtualCameras, CameraStateEnum.BALL_FOLLOW_LOOK_AT_AFTER_WAITING, this)
                },
                {
                    CameraStateEnum.BALL_FOLLOW_LOOK_AT_AFTER_WAITING_WIDE, new CameraBallFlyFollowLookAtWide(mainCamera, virtualCameras, CameraStateEnum.BALL_FOLLOW_LOOK_AT_AFTER_WAITING, this)
                },
                {
                    CameraStateEnum.BALL_FOLLOW_AFTER_WAITING, new CameraBallFollow(mainCamera, virtualCameras, CameraStateEnum.BALL_FOLLOW_AFTER_WAITING, this)
                },
                {
                    CameraStateEnum.BALL_FOLLOW_AFTER_FIRST_ROLL, new CameraFollowAfterFirstRoll(mainCamera, virtualCameras, CameraStateEnum.BALL_FOLLOW_AFTER_FIRST_ROLL, this)
                },
                {
                    CameraStateEnum.FOLLOW_BALL_PUTTING, new CameraFollowBallPutting(mainCamera, virtualCameras, CameraStateEnum.FOLLOW_BALL_PUTTING, this)
                },
                {
                    CameraStateEnum.LOOKAT_BALL_PUTTING, new CameraLookAtBallPutting(mainCamera, virtualCameras, CameraStateEnum.LOOKAT_BALL_PUTTING, this)
                },
                {
                    CameraStateEnum.LOOKDOWN_BALL_PUTTING, new CameraLookDownBallPutting(mainCamera, virtualCameras, CameraStateEnum.LOOKDOWN_BALL_PUTTING, this)
                },
                {
                    CameraStateEnum.ORBIT_HOLE, new CameraOrbitHole(mainCamera, virtualCameras, CameraStateEnum.ORBIT_HOLE, this)
                },
                {
                    CameraStateEnum.ORBIT_HOLE_LOOKUP, new CameraOrbitHoleLookUp(mainCamera, virtualCameras, CameraStateEnum.ORBIT_HOLE, this)
                },
                {
                    CameraStateEnum.ORBIT_HOLE_STATIC, new CameraOrbitHoleStatic(mainCamera, virtualCameras, CameraStateEnum.ORBIT_HOLE_STATIC, this)
                },
                {
                    CameraStateEnum.ORBIT_HOLE_DESCEND, new CameraOrbitHoleDescend(mainCamera, virtualCameras, CameraStateEnum.ORBIT_HOLE_DESCEND, this)
                },
                {
                    CameraStateEnum.HOLE_FINISHER_TEMPLATE, new CameraHoleFinisherTemplate(mainCamera, virtualCameras, CameraStateEnum.HOLE_FINISHER_TEMPLATE, this)
                },

                {
                    CameraStateEnum.DEBUG_BIRD_EYE, new CameraDebugBirdEyeView(mainCamera, virtualCameras, CameraStateEnum.DEBUG_BIRD_EYE, this)
                },

            };

            foreach (KeyValuePair<CameraStateEnum, ICameraState> state in states)
            {
                state.Value.SetBall(ballList);
                state.Value.SetHolePosition(holePosition);
                state.Value.SetLineRenderer(lineRenderer, trajectoryLine);
                state.Value.SetDecalProjector(decalProjector, pinCenter, pinRadius);

                if (state.Value is CameraBallRearView)
                {
                    var rearState = (CameraBallRearView)state.Value;
                    rearState.SetDragBallCamera(_DragBallCamera);
                }
            }

            active = true;
            manager.InitBus(_gameplayBus);
        }

        /// <summary>
        /// Broadcast an event to the camera state machine, making it change state accordingly
        /// </summary>
        /// <param name="e">Event enum of the camera state machine</param>
        /// <param name="data">An object array as passing data</param>
        public void ReceiveEvent(CameraControllerEvent e, object[] data)
        {
            CameraStateEnum index = currentState.OnEvent(e, data);

            if (index == CameraStateEnum.NONE)
            {
                return;
            }
            else
            {
                ChangeState(index, data);
            }
        }

        /// <summary>
        /// Change the camera state manually
        /// </summary>
        /// <param name="index">State enum of a camera state</param>
        /// <param name="data">An object array as passing data</param>
        public void ChangeState(CameraStateEnum index, object[] data)
        {
            ChangeState(states[index], data, index);
        }

        /// <summary>
        /// Change the camera state from one to another, used internally only
        /// </summary>
        /// <param name="newState">A camera state to switch to</param>
        /// <param name="data">An object array as passing data</param>
        private void ChangeState(ICameraState newState, object[] data, CameraStateEnum stateEnum)
        {
            // cache prev state
            prevState = currentState;
            // exit old state
            currentState.OnExit();
            // switch state
            currentState = newState;
            // enter new state
            currentState.OnEnter(data);
        }

        /// <summary>
        /// Update current camera state
        /// </summary>
        string stateDebugCache = "None";

        public void Update()
        {
            if (currentState != null && active)
            {
                currentState.Update();
#if UNITY_EDITOR
                if (!stateDebugCache.Equals(currentState.GetType().ToString()))
                {
                    stateDebugCache = currentState.GetType().ToString();
                    Debug.Log(String.Format("Ball {0} cam state: {1}", MasterManager.Instance.CurrentBallInCamera, currentState.GetType().ToString()));
                }
#endif
            }
        }

        /// <summary>
        /// Fixed update current camera state
        /// </summary>
        private void FixedUpdate()
        {
            if (currentState != null && active)
                currentState.FixedUpdate();
        }

        /// <summary>
        /// Late update camera state
        /// </summary>
        private void LateUpdate()
        {
            if (currentState != null && active)
                currentState.LateUpdate();
        }
        
        /// <summary>
        /// Get current camera state of the camera state machine
        /// </summary>
        /// <returns>Camera state</returns>
        public ICameraState GetState()
        {
            return currentState;
        }

        // GADGET EVENTS
        public void SetPlayerGadget(PlayerGadget gadget)
        {
            playerGadget = gadget;
        }

        //

        public void StartGameSetUp()
        {
            currentState = states[CameraStateEnum.BALL_BIRD_EYE];
            currentState.OnEnter(null);
            gameObject.SetActive(true);
        }

        public void Preview()
        {
            currentState = states[CameraStateEnum.PREVIEW_COURSE];
            currentState.OnEnter(null);
            gameObject.SetActive(true);
        }

        public void ShutOff()
        {
            currentState.OnExit();
            currentState = null;
            for (int i = 0; i < virtualCameraList.Length; i++)
            {
                virtualCameraList[i].gameObject.SetActive(false);
            }
        }

        public void SkipPreview()
        {
            if (currentState is CameraPreviewCourseView)
            {
                currentState.OnExit();
            }
        }

        #region Scene Object
        public void HideWhicheverBallInHole()
        {
            foreach (var currentBall in ballList)
            {
                if (GolfUtility.IsPositionOnSurface(currentBall.gameObject.transform.position, new SurfaceType[1] { SurfaceType.HoleCup }))
                {
                    currentBall.Hide();
                }
            }
        }

        public void ShowAllBall()
        {
            foreach (var currentBall in ballList)
            {
                currentBall.Show();
            }
        }

        public void SetActiveFlag(bool active)
        {
            GlobalSO.PlayFieldSO.Data.Flag.SetActive(active);
        }
        #endregion

        #region Post processing
        public const float ColorAdjustmentOffetToDark = 1.3f;
        private static IEnumerator LerpPostExposure(float targetValue, float duration)
        {
            float startValue = GlobalSO.PlayFieldSO.GetLocalVolumeColorAdjustment().postExposure.value;
            float elapsedTime = 0f;

            while (elapsedTime < duration)
            {
                elapsedTime += Time.deltaTime;
                float t = Mathf.Clamp01(elapsedTime / duration);
                GlobalSO.PlayFieldSO.AdjustLocalVolumePostExposure(Mathf.Lerp(startValue, targetValue, t));
                yield return null;
            }

            GlobalSO.PlayFieldSO.AdjustLocalVolumePostExposure(targetValue);
        }

        private Coroutine _currentCoroutine;

        public void AdjustPostExposure(float targetValue, float duration)
        {
            if (_currentCoroutine != null)
            {
                StopCoroutine(_currentCoroutine);
            }

            _currentCoroutine = StartCoroutine(LerpPostExposure(targetValue, duration));
        }
        #endregion

        bool isMovingOpponentAimPoint = false;
        IEnumerator moveOpponentAimPoint;
        Vector3 cachedOpponentAimPointPosition = Vector3.zero;

        public void MoveOpponentAimPoint(Vector3 position)
        {
            if (isMovingOpponentAimPoint)
            {
                StopCoroutine(moveOpponentAimPoint);
                cachedOpponentAimPointPosition = position;
            }

            moveOpponentAimPoint = SmoothLerp(position);
            StartCoroutine(moveOpponentAimPoint);
        }

        private IEnumerator SmoothLerp(Vector3 position)
        {
            isMovingOpponentAimPoint = true;

            decalProjector.gameObject.SetActive(true);
            pinCenter.gameObject.SetActive(true);

            float time = 1f;

            Vector3 startingPos = position + (position - cachedOpponentAimPointPosition).normalized * 5f;
            Vector3 finalPos = position;

            float elapsedTime = 0;

            while (elapsedTime < time)
            {
                Vector3 lerpPosition = Vector3.Lerp(startingPos, finalPos, (elapsedTime / time));
                decalProjector.gameObject.transform.position = lerpPosition;
                pinCenter.gameObject.transform.position = lerpPosition;

                elapsedTime += Time.deltaTime;
                yield return null;
            }

            isMovingOpponentAimPoint = false;
            cachedOpponentAimPointPosition = position;
        }

        private void OnUpdateLineRenderer()
        {
            if (currentState is CameraBirdEyeView)
            {
                ((CameraBirdEyeView)states[CameraStateEnum.BALL_BIRD_EYE]).NeedUpdateLineView = true;
            }
        }
    }
}