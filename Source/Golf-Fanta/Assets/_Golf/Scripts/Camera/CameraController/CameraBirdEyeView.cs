using System;
using _Golf.Scripts.Core;
using Cinemachine;
using GolfPhysics;
using System.Collections.Generic;
using System.Linq;
using _Golf.Physics.Data;
using _Golf.Scripts.Common;
using _Golf.Scripts.Common.Helper;
using _Golf.Scripts.GlobalManagers;
using _Golf.Scripts.ScriptableObjects;
using DG.Tweening;
using GolfFantaModule.Models.Economy;
using UnityEngine;
using UnityEngine.InputSystem;
using UnityEngine.Rendering.Universal;
using _Golf.Scripts.Lobby;
using UnityEngine.Splines;
using _Golf.Scripts.UI;
using UnityEngine.EventSystems;
using Object = UnityEngine.Object;

namespace GolfGame
{
    /// <summary>
    /// A state of the cinemachine brain in which the camera is placed on a high position that nicely looks down at both ball and hole.
    /// To be more specific, this state becomes active when player switch to SET PIN mode while in hit ball UI. 
    /// </summary>
    public class CameraBirdEyeView : ICameraState
    {
        #region Events
        public static event Action<float, BallGuideStatus> DistanceChanged;
        #endregion 

        #region camera
        private CameraStateController controller;
        private Camera mainCamera;
        private CinemachineBrain cinemachineBrain;
        private Dictionary<CameraStateEnum, CinemachineVirtualCamera> virtualCameras;
        private CameraStateEnum key;
        private CinemachineVirtualCamera cameraInCharge;
        private CameraMediatorManager cameraMediatorManager;
        private CameraTouchInput _touchInput;
        #endregion

        #region scene objects
        private List<Ball> ballList;
        private int LocalPlayerIndex => ballList.IndexOf(ballList.Find(ball => ball.IsLocalBall()));
        private Ball LocalBall => ballList[LocalPlayerIndex];

        private Ball currentBall;

        private Transform hole;
        private LineRenderer aimLineRenderer; private CameraBirdEyeViewDrawTrajectoryLine trajectoryLineRenderer;
        private DecalProjector aimPointProjector;
        private DecalProjector aimPointCenter;
        private DecalProjector aimPointDirection;
        private DecalProjector aimPointDirectionArrow;
        private DecalProjector aimPointRadius;
        
        private Dictionary<Collider, Renderer> _treeRenderers;
        #endregion

        #region input action
        private InputAction scrollAction;

        private InputAction leftMousePress;
        private InputAction screenPosPositionMouse;

        private InputAction press0TouchScreen;
        private InputAction press1TouchScreen;

        private InputAction screenPos0PositionTouchScreen;
        private InputAction screenPos1PositionTouchScreen;

        private InputTouch inputTouch0;
        private InputTouch inputTouch1;
        #endregion

        #region Constant
        // Touch Constant
        private const float ScrollSpeed = 1f;
        private const float EdgeThreshold = 100f;
        private const float MinCameraHeight = 1f;
        private const float MaxCameraHeight = 200f;
        private const float MinDistanceFromCameraToHole = 2f;
        private const float DragSlerpSpeed = 1f;
        private const float DragMaxDistance = 150f;
        private const float DecalMoveBackSpeed = 5f;
        // Zoom Constant
        private const float SlerpSpeed = 0.2f;
        private const float ZoomStep = 2f;
        private const float ZoomHorizontalRange = 60f;
        private const float ZoomVerticalRange = 60f;
        private const float MinZoomInDistance = 5f;
        private const float MinZoomSpeed = 0.1f;
        private const float XZAxisMoveSpeed = 0.8f;
        private const float YAxisMoveSpeed = 1.75f;
        // Scale Object Constant
        private const float PinScaleRatio = 60f;
        private const float LineScaleHighRatio = 180;
        private const float MinPinScale = 0.07f;
        // Hide Object Handler Constant
        private const float RaycastBoxD = 1f;
        #endregion

        #region internal calculation
        private Vector3 curScreenPos;
        private bool isDraggingPin;
        private bool isDragging;
        
        private bool isSpinningPin = false;
        private bool isDragOnEditor = false;

        private Vector3 focusZoomPos = Vector3.zero;
        private float prevMagnitude = 0;
        private Vector2 prevScreenPos0;
        private Vector2 prevScreenPos1;
        private float finalDistance;

        private float initZAxis;
        private Vector3 cachedPos;

        private BallData currentBallData;
        private float gearBonusDistance = 0f;
        private BallTrajectory _ballTrajectory;
        public bool NeedUpdateLineView;
        #endregion

        public CameraBirdEyeView(Camera _mainCamera,
            Dictionary<CameraStateEnum, CinemachineVirtualCamera> _virtualCameras,
            CameraStateEnum _key,
            CameraStateController _controller,
            GameplayBus gameplayBus)
        {
            mainCamera = _mainCamera; 
            cinemachineBrain = mainCamera.GetComponent<CinemachineBrain>();

            controller = _controller;

            virtualCameras = _virtualCameras;
            key = _key;
            cameraInCharge = virtualCameras[key];
            _touchInput = cameraInCharge.GetComponent<CameraTouchInput>();
            _touchInput.StepDragCallBack = StepDragCallBack;

            cameraMediatorManager = controller.GetManager();
        }
        
        #region camera state implement
        public void OnEnter(object[] data)
        {
            _treeRenderers = GlobalSO.PlayFieldSO.GetTreeRenderers();
            
            cinemachineBrain.m_DefaultBlend = new CinemachineBlendDefinition(CinemachineBlendDefinition.Style.Cut, 0f);
            cameraInCharge.gameObject.SetActive(true);

            ActionDispatcher.Dispatch(new FlagIconEnableAction(true));
            _ = MasterManager.Instance.OpenUIComponent(UIComponentEnum.GameplayMeterDisplayUIComponent);
            
            if (MasterManager.Instance.CurrentBallInCamera == LocalPlayerIndex)
            {
                InitInputActionOnDevice(); 
                InitInputActionOnEditor();

                cameraMediatorManager.SetActiveForLineRenderer(true);

                aimPointProjector.gameObject.SetActive(true);
                aimPointCenter.gameObject.SetActive(true);

                var autoAimPosition = cameraMediatorManager.GetCachedAimPosition();
                aimPointProjector.gameObject.transform.position = autoAimPosition;
                
                finalDistance = cameraMediatorManager.GetCachedFinalDistance();

                var trajectoryInput = cameraMediatorManager.HashSpeedAndSideAngle(false, autoAimPosition);
                cameraMediatorManager.Draw(out var drawTrajectory, trajectoryInput);
                _ballTrajectory = drawTrajectory;
                
                aimPointCenter.gameObject.transform.position = drawTrajectory[drawTrajectory.FirstBounceFrame].Position.ToVector3();
                
                // Set Camera Position Done then Calculate Zoom Object
                SetUpCamera(data);
            }
            else
            {
                // Set Camera Position Done then Calculate Zoom Object
                SetUpCamera(data);

                cameraMediatorManager.SetActiveForLineRenderer(false);
                trajectoryLineRenderer.StopDrawingTrajectory();

                aimPointProjector.gameObject.SetActive(false);
                aimPointCenter.gameObject.SetActive(false);
            }
            
            isSpinningPin = true;

            focusZoomPos = aimPointCenter.transform.position;
            
            ScaleSceneObject();
            
            aimLineRenderer.material.SetColor("_Color", Constant.DefaultLineColor);
            trajectoryLineRenderer.SetConfigToExact();
        }

        public void OnExit()
        {
            cinemachineBrain.m_DefaultBlend = new CinemachineBlendDefinition(CinemachineBlendDefinition.Style.EaseInOut, 2f);
            
            cameraInCharge.gameObject.SetActive(false);
            
            ActionDispatcher.Dispatch(new FlagIconEnableAction(false));
            
            if(aimLineRenderer != null)
                cameraMediatorManager.SetActiveForLineRenderer(false);
            trajectoryLineRenderer.StopDrawingTrajectory();
            if (aimPointProjector != null) 
                aimPointProjector.gameObject.SetActive(false);
            if(aimPointCenter != null)
                aimPointCenter.gameObject.SetActive(false);
            if(aimPointRadius != null)
                aimPointRadius.gameObject.SetActive(false);

            DisposeInputActionOnDevice();
            DisposeInputActionOnEditor();

            isSpinningPin = false;

            ReEnableRenderers();
        }

        private bool IsClickedOnUI(Vector2 position)
        {
            PointerEventData pointer = new PointerEventData(EventSystem.current);
            
            pointer.position = position;
            
            List<RaycastResult> raycastResults = new List<RaycastResult>();

            EventSystem.current.RaycastAll(pointer, raycastResults);
            
            return raycastResults.Count > 0;
        }
        
        public void FixedUpdate()
        {
 
        }

        public void LateUpdate()
        {

        }
        
        public void Update()
        {
            if (isSpinningPin && aimPointProjector != null)
            {
                aimPointProjector.transform.Rotate(new Vector3(0, 0, 1.75f));
            }

            var localAimProjector = aimPointCenter.transform; //aimPointProjector.transform;
            var localAimCenter = aimPointProjector.transform; //aimPointCenter.transform;
            var localBallTransform = currentBall.transform;
            
            if (MasterManager.Instance.CurrentBallInCamera == LocalPlayerIndex)
            {
                SetPinDirection();

                if (NeedUpdateLineView)
                {
                    NeedUpdateLineView = false;
                    DrawProjection(localAimCenter.position, out _);
                    return;
                }
                
                if (isDraggingPin)
                {
                    MasterManager.Instance.SetRenderInterval(1);

                    Ray ray = Camera.main.ScreenPointToRay(curScreenPos + _offetOnScreen);
                    
                    CheckEdgeTouch(curScreenPos);
                    
                    RaycastHit hit;
                    
                    if (Physics.Raycast(ray, out hit, 2000, Constant.AimLayerMask))
                    {
                        float currentDistance = (hit.point - localBallTransform.position).magnitude;

                        var maxDistance = finalDistance;
                        
                        localAimProjector.position = hit.point;
                        
                        if (currentDistance > maxDistance)
                        {
                            Vector3 maxRangePoint = localBallTransform.position + 
                                                    (hit.point - localBallTransform.position).normalized * maxDistance;
                            
                            if (IsOutOfBound(maxRangePoint))
                            {
                                if (Physics.Raycast(maxRangePoint.OffsetY(500f), Vector3.down, out var checkHit, Mathf.Infinity, Constant.GroundLayerMask))
                                {
                                    var nearestPoint = GetNearestPointFromBound(checkHit.point);
                                    
                                    localAimCenter.position = nearestPoint;
                                    
                                    DrawProjection(nearestPoint, out _);
                                }
                                SetLineStatus(BallGuideStatus.OVER, maxDistance);
                            }
                            else
                            {
                                if (Physics.Raycast(maxRangePoint.OffsetY(500f), Vector3.down, out var checkHit, Mathf.Infinity, Constant.GroundLayerMask))
                                {
                                    DrawProjection(checkHit.point, out _);
                                }
                                else
                                {
                                    DrawProjection(maxRangePoint, out _);
                                }
                                
                                localAimCenter.position = maxRangePoint;
                                
                                SetLineStatus(BallGuideStatus.OVER, maxDistance);
                            }
                        }
                        else
                        {
                            if (IsOutOfBound(hit.point))
                            {
                                var nearestPoint = GetNearestPointFromBound(hit.point);
                                
                                var newDistance = (nearestPoint - localBallTransform.position).magnitude;
                                
                                controller.SyncInterval(PlayerCommandSignature.DragAimCommand, new object[] { hit.point });
                                
                                localAimCenter.position = nearestPoint;
                                
                                DrawProjection(nearestPoint, out _);

                                SetLineStatus(BallGuideStatus.DEFAULT, newDistance);
                            }
                            else
                            {
                                DrawProjection(hit.point, out _);

                                controller.SyncInterval(PlayerCommandSignature.DragAimCommand, new object[] { hit.point });

                                localAimCenter.position = hit.point;
                                
                                SetLineStatus(BallGuideStatus.DEFAULT, currentDistance);
                            }
                        }
                        ScaleSceneObject();
                    }
                }
                else
                {
                    MasterManager.Instance.SetRenderInterval(2);

                    localAimProjector.position = Vector3.Slerp(
                        localAimProjector.position,
                        localAimCenter.position,
                         DecalMoveBackSpeed * Time.deltaTime
                    );

                    float currentDistance = (localAimProjector.position -localAimCenter.position).magnitude;
                    
                    float distanceFromBallToAim = (localAimProjector.position - localBallTransform.position).magnitude;

                    if (currentDistance < 0.1f)
                    {
                        SetLineStatus(BallGuideStatus.DEFAULT, currentDistance);
                    }

                    DistanceChanged?.Invoke(Mathf.Min(distanceFromBallToAim, finalDistance), BallGuideStatus.NONE);
                }
            }
            CheckHitTrees();
        }
        
        public CameraStateEnum OnEvent(CameraControllerEvent e, object[] data)
        {
            if (e == CameraControllerEvent.SET_INPUT)
            {
                return CameraStateEnum.BALL_REAR;
            }
            else
            {
                return CameraStateEnum.NONE;
            }
        }
        #endregion

        #region Data Injection
        public void SetBall(List<Ball> balls)
        {
            ballList = balls;
            currentBall = LocalBall;
        }

        public void SetHolePosition(GameObject holePosition)
        {
            hole = holePosition.transform;
        }

        public void SetLineRenderer(LineRenderer _lineRenderer, CameraBirdEyeViewDrawTrajectoryLine trajectoryLine)
        {
            aimLineRenderer = _lineRenderer;
            cameraMediatorManager.SetActiveForLineRenderer(false);

            trajectoryLineRenderer = trajectoryLine;
            trajectoryLineRenderer.StopDrawingTrajectory();
        }

        public void SetDecalProjector(DecalProjector _decalProjector, DecalProjector PinCenter, DecalProjector PinRadius)
        {
            aimPointProjector = _decalProjector;
            aimPointCenter = PinCenter;
            aimPointRadius = PinRadius;
            aimPointDirection = aimPointCenter.transform.GetChild(1).GetComponent<DecalProjector>();
            aimPointDirectionArrow = aimPointCenter.transform.GetChild(2).GetComponent<DecalProjector>();
        }
        #endregion

        #region Utilities
        public void SetUpCamera(object[] data)
        {
            currentBall = ballList[MasterManager.Instance.CurrentBallInCamera];

            if (MasterManager.Instance.CurrentBallInCamera == LocalPlayerIndex)
            {
                cameraInCharge.m_Lens.FieldOfView = 60f;
                const float rangeMoveBelongToDistance = 0.25f;
                const float rangeMoveBack = 0.15f;
                const float boostEulerAngle = 7.5f;
                const float angleCalculateInRad = 60 * Mathf.Deg2Rad;

                var ballPosition = currentBall.transform.position;
                var holePosition = hole.position;
                var aimPosition = aimPointCenter.transform.position;
                
                var directionFromBallToAim = (ballPosition - aimPosition).normalized;
                var directionFromBallToHole = (holePosition - ballPosition).normalized;

                var distanceFromBallToAim = (ballPosition - aimPosition).magnitude;
                var distanceFromBallToHole = (ballPosition - holePosition).magnitude;
                var moveBackRange = Mathf.Max(distanceFromBallToHole * rangeMoveBack, 1f);
                var cameraHeight = Mathf.Clamp(ballPosition.y
                                               + distanceFromBallToHole * rangeMoveBelongToDistance
                    , MinCameraHeight + ballPosition.y, MaxCameraHeight + ballPosition.y);
                
                // For Drag Over Decal
                var angle = Vector3.Angle(Vector3.forward, directionFromBallToHole);
                Vector3 cross = Vector3.Cross(Vector3.forward, directionFromBallToHole);
                var isRerverse = cross.y < 0;
                angle = isRerverse ? - angle : angle;
                initZAxis = angle < 0 ? Mathf.Abs(angle) : 180f + (180f - angle);
                
                // Calculate CameraPos
                cachedPos.x = ballPosition.x + 2f * directionFromBallToAim.x * moveBackRange - (isRerverse ? Mathf.Cos(angleCalculateInRad) * moveBackRange : 0);
                cachedPos.y = cameraHeight;
                cachedPos.z = ballPosition.z + 2f * directionFromBallToAim.z * moveBackRange - (isRerverse == false ? Mathf.Cos(angleCalculateInRad) * moveBackRange : 0);
                
                cameraInCharge.transform.position = cachedPos;
                
                // Boost Euler Angles For Better View
                cameraInCharge.transform.LookAt(aimPointCenter.transform.position);
                cameraInCharge.transform.eulerAngles += new Vector3(boostEulerAngle, 0, 0);
            }
            else
            {
                cameraInCharge.m_Lens.FieldOfView = 50f;

                Vector3 ballToHole = (hole.transform.position - currentBall.transform.position).normalized;

                var direction = (currentBall.transform.position - hole.transform.position).normalized;

                var range = (currentBall.transform.position - hole.transform.position).magnitude;

                Vector3 cameraPos = direction.normalized * (MinDistanceFromCameraToHole + range * 0.4f) + currentBall.transform.position;

                var cameraHeight = Mathf.Clamp((MinCameraHeight + currentBall.transform.position.y) + range * 0.65f, 45f, MaxCameraHeight);

                cameraInCharge.transform.position = new Vector3(cameraPos.x, cameraHeight, cameraPos.z);

                cameraInCharge.transform.LookAt(hole.transform.position);
                
                focusZoomPos = aimPointCenter.transform.position;
            }
        }

        private void DrawProjection(Vector3 endPoint, out BallTrajectory drawTrajectory)
        {
            var trajectoryInput = cameraMediatorManager.HashSpeedAndSideAngle(true, endPoint);
            cameraMediatorManager.Draw(out drawTrajectory, trajectoryInput);
            _ballTrajectory = drawTrajectory;
        }
        
        #endregion

        #region INPUT SYSTEM SETUP

        private Vector3 _offetOnScreen;
        private Vector3 GetPinScreenPos()
        {
            return Camera.main.WorldToScreenPoint(aimPointProjector.transform.position);
        }
        
        /// <summary>
        /// Initialize Input Action On Device (Input System)
        /// </summary>
        ///
        private void InitInputActionOnDevice()
        {
            // ok so this is a very important note
            // the order in which input actions are instantiated matters!
            // for example, in this function
            // [Touch Screen Get Touch Position] is instantiated first then [Touch Screen Get Press] second
            // => when you touch the screen, you get the input from [Touch Screen Get Touch Position] before [Touch Screen Get Press]
            // you will have the screen position input to perform a raycast when you touch screen
            // if the order is reversed, you won't have the screen position ready as it is registered after screen touch

            inputTouch0 = new InputTouch();
            inputTouch1 = new InputTouch();

            // SCREEN POSISION TOUCH SCREEN 0
            screenPos0PositionTouchScreen = new InputAction
            (
                type: InputActionType.Value,
                binding: "<Touchscreen>/touch0/position"
            );
            screenPos0PositionTouchScreen.Enable();

            screenPos0PositionTouchScreen.started += context =>
            {
                SetPinUsingScreenPos(context);
                inputTouch0.Update(context);
            };
            
            screenPos0PositionTouchScreen.performed += context =>
            {
                SetPinUsingScreenPos(context);
                inputTouch0.Update(context);
                DragMapOnDevice();
            };
            screenPos0PositionTouchScreen.canceled += context =>
            {
                inputTouch0.Reset();
            };

            // SCREEN POSISION TOUCH SCREEN 1
            screenPos1PositionTouchScreen = new InputAction
            (
                type: InputActionType.Value,
                binding: "<Touchscreen>/touch1/position"
            );
            screenPos1PositionTouchScreen.Enable();
            screenPos1PositionTouchScreen.performed += context =>
            {
                inputTouch1.Update(context);
                RotateAndZoomActionOnDevice();
            };
            screenPos1PositionTouchScreen.canceled += context =>
            {
                inputTouch1.Reset();
            };

            // PRESS TOUCH SCREEN 0
            press0TouchScreen = new InputAction
            (
                type: InputActionType.Button,
                binding: "<Touchscreen>/touch0/press"
            );
            press0TouchScreen.Enable();
            press0TouchScreen.performed += _ =>
            {
                isDragging = true;
                CheckIfPressingOnPin();
                _offetOnScreen = GetPinScreenPos() - curScreenPos;
            };
            press0TouchScreen.canceled += _ =>
            {
                CancelPinDrag();
                inputTouch0.Reset();
                prevScreenPos0 = Vector2.zero;
                isDragging = false;
            };

            // PRESS TOUCH SCREEN 1
            press1TouchScreen = new InputAction
            (
                type: InputActionType.Button,
                binding: "<Touchscreen>/touch1/press"
            );
            press1TouchScreen.Enable();
            press1TouchScreen.performed += _ =>
            {
            };
            press1TouchScreen.canceled += _ =>
            {
                inputTouch1.Reset();
                prevMagnitude = 0;
                prevScreenPos1 = Vector2.zero;
                isDragging = false;
            };
        }

        /// <summary>
        /// Dispose Input Action On Device (Input System)
        /// </summary>
        private void DisposeInputActionOnDevice()
        {
            press0TouchScreen.Disable();
            press1TouchScreen.Disable();
            screenPos0PositionTouchScreen.Disable();
            screenPos1PositionTouchScreen.Disable();
            inputTouch0 = null;
            inputTouch1 = null;
        }

        private int GetTouchCount()
        {
            return Input.touchCount;
        }

        /// <summary>
        /// Handle Zoom Action On Device (Input System)
        /// </summary>
        private void ZoomActionOnDevice(float distance)
        {
            if (isDraggingPin) return;

            if (GetTouchCount() < 2) return;
            
            ZoomCamera(- distance / 2f);
        }
        
        private void StepDragCallBack(Vector3 target)
        {
            return;
            if(isDraggingPin) return;
            
            if(inputTouch0 is not { Moved: true }) return;
            
            if(!isDragging) return;
            
            if(GetTouchCount() > 1) return;
            
            if(IsClickedOnUI(inputTouch0.position)) return;
            
            var distanceFromTargetToBound = GlobalSO.PlayFieldSO.GetNearestDistance(target.SetY(GlobalSO.PlayFieldSO.Data.CurrentPin.Position.y));
            
            var distanceFromCurrentToBound = GlobalSO.PlayFieldSO.GetNearestDistance(cameraInCharge.transform.position.SetY(GlobalSO.PlayFieldSO.Data.CurrentPin.Position.y));
            
            if (distanceFromTargetToBound > 100f)
            {
                if (distanceFromTargetToBound > distanceFromCurrentToBound)
                {
                    return;
                }
            }
            
            cameraInCharge.transform.position = target;
            
            ScaleSceneObject();
            
            focusZoomPos = GetFocusZoomPos();
        }
        
        private void RotateAndZoomActionOnDevice()
        {
            if (isDraggingPin) return;

            if (GetTouchCount() < 2) return;
            
            if(IsClickedOnUI(inputTouch0.position) || IsClickedOnUI(inputTouch1.position)) return;

            Vector2 screenPos0 = screenPos0PositionTouchScreen.ReadValue<Vector2>();
            Vector2 screenPos1 = screenPos1PositionTouchScreen.ReadValue<Vector2>();

            if (prevScreenPos0 == Vector2.zero || prevScreenPos1 == Vector2.zero)
            {
                prevScreenPos0 = screenPos0;
                prevScreenPos1 = screenPos1;
                return;
            }

            Vector2 currentVector = screenPos1 - screenPos0;
            
            Vector2 previousVector = prevScreenPos1 - prevScreenPos0;
            
            prevScreenPos0 = screenPos0;
            prevScreenPos1 = screenPos1;

            var prevMagnitude = previousVector.magnitude;
            var currentMagnitude = currentVector.magnitude;
            
            var magnitude = (currentMagnitude - prevMagnitude);

            ZoomActionOnDevice(magnitude);
            
            float angle = Vector2.SignedAngle(previousVector, currentVector);
            
            // Rotate
            if (Mathf.Abs(angle) > 0.5f)
            {
                cameraInCharge.transform.RotateAround(focusZoomPos, Vector3.up, angle);
                ScaleSceneObject();
            }
        }

        private bool IsPinching()
        {
            return inputTouch0.Moved || inputTouch1.Moved;
        }
        
        /// <summary>
        /// Initialize Input Action On Editor (Input System)
        /// </summary>
        private void InitInputActionOnEditor()
        {
            leftMousePress = new InputAction(
                type: InputActionType.Button,
                binding: "<Mouse>/press"
            );
            leftMousePress.Enable();

            leftMousePress.performed += _ =>
            {
                isDragOnEditor = true;
                CheckIfPressingOnPin();
            };
            leftMousePress.canceled += _ =>
            {
                isDragOnEditor = false;
                CancelPinDrag();
            };

            screenPosPositionMouse = new InputAction(
                type: InputActionType.Value,
                binding: "<Mouse>/position"
            );
            screenPosPositionMouse.Enable();

            screenPosPositionMouse.started += context =>
            {
                SetPinUsingScreenPos(context);
                inputTouch0.Update(context);
            };
            
            screenPosPositionMouse.performed += context =>
            {
                SetPinUsingScreenPos(context);
                inputTouch0.Update(context);
                DragMapOnEditor();
            };

            screenPosPositionMouse.canceled += context =>
            {
                if(inputTouch0 != null)
                    inputTouch0.Reset();
            };

            SetupScrollAction();
        }

        /// <summary>
        /// Dispose Input Action On Editor (Input System)
        /// </summary>
        private void DisposeInputActionOnEditor()
        {
            leftMousePress.Disable();
            screenPosPositionMouse.Disable();

            TurnOffScrollAction();
        }

        /// <summary>
        /// Raycast using current screen position input to check if player is touching the pin
        /// </summary>
        /// <returns></returns>
        private bool isClickedOnPin()
        {
            if (Camera.main != null)
            {
                var ray = Camera.main.ScreenPointToRay(curScreenPos);
                if (Physics.Raycast(ray, out var hit, float.MaxValue, Constant.ControllerLayerMask))
                {
                    return hit.collider.name.Contains("Pin");
                }
            }

            return false;
        }

        /// <summary>
        /// Check if Pin is pressed, then turn on the Pin Dragging Flag for FixedUpdate
        /// </summary>
        /// <returns></returns>
        private void CheckIfPressingOnPin()
        {
            if (IsClickedOnUI(curScreenPos))
            {
                isDraggingPin = false;
                return;
            }
            if (isClickedOnPin())
            {
                isDraggingPin = isClickedOnPin();
            }
        }

        /// <summary>
        /// Turn on the Pin Dragging Flag for FixedUpdate
        /// </summary>
        private void CancelPinDrag()
        {
            isDraggingPin = false;
        }

        /// <summary>
        /// Current Screen Position, either from mouse or touch screen (Input system)
        /// </summary>
        /// <param name="context"></param>
        private void SetPinUsingScreenPos(InputAction.CallbackContext context)
        {
            curScreenPos = context.ReadValue<Vector2>();
        }

        float cameraDistance;
        /// <summary>
        /// Turn on the scroll action (PC) to zoom the camera while it is in bird eye view.
        /// </summary>
        private void SetupScrollAction()
        {
            scrollAction = new InputAction(binding: "<Mouse>/scroll");
            scrollAction.Enable();

            scrollAction.performed += ctx => ZoomCamera(ctx.ReadValue<Vector2>().y * ScrollSpeed);
        }

        /// <summary>
        /// Turn off the scroll action (PC) to zoom the camera when it leaves bird eye view.
        /// </summary>
        private void TurnOffScrollAction()
        {
            scrollAction.Disable();
        }
        
        /// <summary>
        /// Zoom camera
        /// </summary>
        /// <param name="increment"></param>
        private void ZoomCamera(float increment)
        {
            // Calculate the current horizontal distance between camera and focusZoomPos (ignoring Y component)
            Vector3 cameraPosition2D = new Vector3(cameraInCharge.transform.position.x, 0f, cameraInCharge.transform.position.z);
            
            Vector3 focusPosition2D = new Vector3(focusZoomPos.x, 0f, focusZoomPos.z);
            
            float horizontalDistance = Vector3.Distance(cameraPosition2D, focusPosition2D);
            
            var verticalDistance = cameraInCharge.transform.position.y - focusZoomPos.y;
            
            var distance = Vector3.Distance(cameraInCharge.transform.position, focusZoomPos);
            
            Vector3 translateVector = Vector3.zero;
            
            bool isFar = horizontalDistance > ZoomHorizontalRange;
            
            // Zoom In
            if (increment < 0)
            {
                if (distance >= MinZoomInDistance)
                {
                    Vector3 direction = (focusZoomPos - cameraInCharge.transform.position).normalized;
                    var moveStep = direction * Mathf.Abs(increment) * (verticalDistance / ZoomVerticalRange);
                    moveStep.x *= XZAxisMoveSpeed;
                    moveStep.z *= XZAxisMoveSpeed;
                    moveStep.y *= YAxisMoveSpeed;
                    translateVector += moveStep;
                }
            }
            else if(increment > 0)
            {
                Vector3 direction = (cameraInCharge.transform.position - focusZoomPos).normalized;
                var moveStep = direction * Mathf.Abs(increment) / MinZoomInDistance;
                moveStep.x = !isFar ? moveStep.x * XZAxisMoveSpeed : moveStep.x * MinZoomSpeed;
                moveStep.z = !isFar ? moveStep.z * XZAxisMoveSpeed : moveStep.z * MinZoomSpeed;
                moveStep.y *= YAxisMoveSpeed;
                translateVector += moveStep;
            }
            
            if(increment == 0) return;
            
            var targetPosition = cameraInCharge.transform.position + translateVector;

            var newPosition = Vector3.MoveTowards(cameraInCharge.transform.position, targetPosition, ZoomStep);
            
            // Clamp Camera height
            newPosition.y = Mathf.Clamp(newPosition.y, focusZoomPos.y + MinCameraHeight, focusZoomPos.y + MaxCameraHeight);
            
            ScaleSceneObject();

            if (IsPinching())
            {
                cameraInCharge.transform.position = newPosition;
            
                Quaternion lookRotation = Quaternion.LookRotation(focusZoomPos - cameraInCharge.transform.position);
                cameraInCharge.transform.rotation = Quaternion.Slerp(cameraInCharge.transform.rotation, lookRotation, SlerpSpeed);
            }
        }

                
        #endregion

        #region  Camera Function
        
        private void DragMapOnDevice()
        {
            if (isDraggingPin) return;
            
            if (GetTouchCount() > 1) return;
            
            if(!isDragging) return;
            
            if(IsClickedOnUI(inputTouch0.position)) return;
            
            var deltaMovement = PlanePositionDelta(inputTouch0);

            if(deltaMovement == Vector3.zero) return;
            
            deltaMovement.y = 0;
            
            var targetPosition = cameraInCharge.transform.position + deltaMovement;

            var pinY = GlobalSO.PlayFieldSO.Data.CurrentPin.Position.y;
            
            var distanceFromTargetToBound = GlobalSO.PlayFieldSO.GetNearestDistance(targetPosition.SetY(pinY));
            
            //var distanceFromCurrentToBound = GlobalSO.PlayFieldSO.GetNearestDistance(cameraInCharge.transform.position.SetY(pinY));

            if (!GlobalSO.PlayFieldSO.IsInBound(targetPosition.SetY(pinY)))
            {
                if (distanceFromTargetToBound > DragMaxDistance)
                {
                    return;
                }
            }
            
            cameraInCharge.transform.position = Vector3.Slerp(cameraInCharge.transform.position, targetPosition, DragSlerpSpeed);
                
            ScaleSceneObject();
            
            focusZoomPos = GetFocusZoomPos();
        }
        
        private Vector3 PlanePositionDelta(InputTouch touch)
        {
            // not moved
            if (!touch.Moved)
                return Vector3.zero;

            // delta
            Ray rayBefore = mainCamera.ScreenPointToRay(touch.lastPosition);
            Ray rayNow = mainCamera.ScreenPointToRay(touch.position);

            int layerMask = Constant.AimLayerMask;

            RaycastHit hitBefore;
            RaycastHit hitNow;

            if (Physics.Raycast(rayBefore, out hitBefore, 2000, layerMask) &&
                Physics.Raycast(rayNow, out hitNow, 2000, layerMask))
            {
                return rayBefore.GetPoint(hitBefore.distance) - rayNow.GetPoint(hitNow.distance);
            }

            //not on plane
            return Vector3.zero;
        }
        
        private void DragMapOnEditor()
        {
            if (isDraggingPin) return;

            if (!isDragOnEditor) return;

            if(IsClickedOnUI(inputTouch0.position)) return;
            
            var deltaMovement = PlanePositionDelta(inputTouch0) / 1f;

            deltaMovement.y = 0;

            if (!inputTouch0.Moved) return;
            var targetPosition = cameraInCharge.transform.position + deltaMovement;
            
            var distanceFromTargetToBound = GlobalSO.PlayFieldSO.GetNearestDistance(targetPosition.SetY(GlobalSO.PlayFieldSO.Data.CurrentPin.Position.y));
            
            var distanceFromCurrentToBound = GlobalSO.PlayFieldSO.GetNearestDistance(cameraInCharge.transform.position.SetY(GlobalSO.PlayFieldSO.Data.CurrentPin.Position.y));
            
            if (distanceFromTargetToBound > 100f)
            {
                if (distanceFromTargetToBound > distanceFromCurrentToBound)
                {
                    return;
                }
            }
            cameraInCharge.transform.position = Vector3.Lerp(cameraInCharge.transform.position, targetPosition, 0.5f);
                
            ScaleSceneObject();
            
            focusZoomPos = GetFocusZoomPos();
        }
        
        void DrawBoxCast(Vector3 center, Vector3 halfExtents, Quaternion orientation, Vector3 direction, float distance, Color color, float duration = 0.1f)
        {
            #if UNITY_EDITOR
            Vector3[] corners = new Vector3[8];
            Vector3 right = orientation * Vector3.right * halfExtents.x;
            Vector3 up = orientation * Vector3.up * halfExtents.y;
            Vector3 forward = orientation * Vector3.forward * halfExtents.z;

            corners[0] = center + right + up + forward;
            corners[1] = center + right + up - forward;
            corners[2] = center + right - up + forward;
            corners[3] = center + right - up - forward;
            corners[4] = center - right + up + forward;
            corners[5] = center - right + up - forward;
            corners[6] = center - right - up + forward;
            corners[7] = center - right - up - forward;

            void DrawEdges(Vector3[] c)
            {
                Debug.DrawLine(c[0], c[1], color, duration);
                Debug.DrawLine(c[0], c[2], color, duration);
                Debug.DrawLine(c[0], c[4], color, duration);
                Debug.DrawLine(c[1], c[3], color, duration);
                Debug.DrawLine(c[1], c[5], color, duration);
                Debug.DrawLine(c[2], c[3], color, duration);
                Debug.DrawLine(c[2], c[6], color, duration);
                Debug.DrawLine(c[3], c[7], color, duration);
                Debug.DrawLine(c[4], c[5], color, duration);
                Debug.DrawLine(c[4], c[6], color, duration);
                Debug.DrawLine(c[5], c[7], color, duration);
                Debug.DrawLine(c[6], c[7], color, duration);
            }

            DrawEdges(corners);

            Vector3 castCenter = center + direction.normalized * distance;
            for (int i = 0; i < 8; i++)
                corners[i] += direction.normalized * distance;

            DrawEdges(corners);

            for (int i = 0; i < 8; i++)
                Debug.DrawLine(corners[i] - direction.normalized * distance, corners[i], color, duration);
            #endif
        }
        
        private void CheckHitTrees()
        {
            ReEnableRenderers();
            var cameraPos = cameraInCharge.transform.position;
            var height = 2 * RaycastBoxD * Mathf.Tan(cameraInCharge.m_Lens.FieldOfView * Mathf.Deg2Rad / 2f);
            var width = height * mainCamera.aspect;
            Vector3 halfExtents = new Vector3(width, height, height);
            Vector3 direction = cameraInCharge.transform.forward;
            var center = cameraPos + cameraInCharge.transform.forward * height;
            Quaternion orientation = cameraInCharge.transform.rotation;
            
            // Cast box
            RaycastHit[] hits = Physics.BoxCastAll(center, halfExtents, direction, orientation, height, Constant.GolfFieldLayerMask);
            
            foreach (var hit in hits)
            {
                if (_treeRenderers.TryGetValue(hit.collider, out Renderer renderer))
                {
                    renderer.enabled = false;
                }
            }
            
            DrawBoxCast(center, halfExtents, orientation, direction, RaycastBoxD, Color.red);
        }

        private void ReEnableRenderers()
        {
            foreach (var item in _treeRenderers)
            {
                if(item.Value != null)
                    item.Value.enabled = true;
            }
        }
        
        private void SetPinDirection()
        {
            aimPointDirectionArrow.transform.eulerAngles =
                aimPointCenter.transform.eulerAngles.SetZ(- GlobalSO.PlayFieldSO.Data.Weather.WindDirection);
        }
        
        private void SetLineStatus(BallGuideStatus status, float distance)
        {
            switch (status)
            {
                case BallGuideStatus.DEFAULT:
                    SetPinRadiusPos(false);
                    
                    aimLineRenderer.material.SetColor("_Color", Constant.DefaultLineColor);
                                
                    trajectoryLineRenderer.SetConfigToExact();
                    break;
                case BallGuideStatus.OVER:
                    SetPinRadiusPos(true);
                    
                    aimLineRenderer.material.SetColor("_Color", Constant.OverLineColor);
                    
                    trajectoryLineRenderer.SetConfigToOver();
                    
                    break;
            }
            DistanceChanged?.Invoke(distance, status);
        }
        
        private void SetPinRadiusPos(bool isEnable)
        {
            aimPointRadius.gameObject.SetActive(isEnable);
            
            if(!isEnable) return;
            
            // Rotate pin Radius
            var angle = Vector3.Angle((aimPointCenter.transform.position - currentBall.gameObject.transform.position).normalized, (hole.transform.position - currentBall.gameObject.transform.position).normalized);

            Vector3 cross = Vector3.Cross((aimPointCenter.transform.position - currentBall.gameObject.transform.position).normalized, (hole.transform.position - currentBall.gameObject.transform.position).normalized);

            if (cross.y < 0)
            {
                angle = -angle;
            }

            aimPointRadius.gameObject.transform.eulerAngles = new Vector3(aimPointCenter.gameObject.transform.eulerAngles.x, aimPointCenter.gameObject.transform.eulerAngles.y, initZAxis + angle);
                                
            var radiusPos = aimPointProjector.transform.position;
                                
            aimPointRadius.gameObject.transform.position = radiusPos;
        }
        private bool IsOutOfBound(Vector3 point)
        {
            return !GlobalSO.PlayFieldSO.IsInBound(point);
        }

        private Vector3 GetNearestPointFromBound(Vector3 aimPoint)
        {
            List<Vector3> intersections = new List<Vector3>();
            intersections.Clear();

            var splineContainer = GlobalSO.PlayFieldSO.Bound;
            var boundTransform = GlobalSO.PlayFieldSO.Bound.transform;
            
            Vector3 localStart = boundTransform.InverseTransformPoint(currentBall.transform.position);
            Vector3 localEnd = boundTransform.InverseTransformPoint(aimPoint);
            Vector3 localDir = (localEnd - localStart).normalized;

            Bounds splineBounds = splineContainer.Spline.GetBounds();
            
            if (!RayIntersectsBounds(localStart, localDir, splineBounds))
            {
                Debug.Log("No intersection with spline bounds");
                return aimPoint;
            }

            
            var spline = splineContainer.Spline;
            float step = 0.01f;
            for (float t = 0; t <= 1; t += step)
            {
                Vector3 pointOnSpline = spline.EvaluatePosition(t);

                if (PointOnRay(localStart, localDir, pointOnSpline))
                {
                    Vector3 worldIntersection = boundTransform.TransformPoint(pointOnSpline);
                    intersections.Add(worldIntersection);
                }
            }

            if (intersections.Count == 0)
            {
                return aimPoint;    
            }

            var nearestPoint = intersections.First();
            foreach (var point in intersections)
            {
                var currentDistance = Vector3.Distance(aimPoint.SetY(nearestPoint.y), nearestPoint);
                var newDistance = Vector3.Distance(aimPoint.SetY(point.y), point);
                if (newDistance < currentDistance)
                    nearestPoint = point;
            }
            
            return nearestPoint;
        }
        
        bool RayIntersectsBounds(Vector3 rayOrigin, Vector3 rayDir, Bounds bounds)
        {
            float t;
            return bounds.IntersectRay(new Ray(rayOrigin, rayDir), out t);
        }
        
        bool PointOnRay(Vector3 rayOrigin, Vector3 rayDir, Vector3 point)
        {
            Vector3 toPoint = point - rayOrigin;
            float projection = Vector3.Dot(toPoint, rayDir);
            return projection >= 0 && projection <= toPoint.magnitude;
        }
        
        private void ScaleSceneObject()
        {
            float decalScale = 1;
            
            float distance = Vector3.Distance(cameraInCharge.transform.position, aimPointProjector.transform.position);
            
            var ratio = (float)(distance / PinScaleRatio);
            
            decalScale = Mathf.Max(ratio, MinPinScale);
            
            aimPointProjector.transform.DOScale(Vector3.one * decalScale, Time.deltaTime);
            aimPointDirection.transform.DOScale(Vector3.one * decalScale, Time.deltaTime);
            aimPointDirectionArrow.transform.DOScale(Vector3.one * decalScale, Time.deltaTime);
            aimPointRadius.transform.DOScale(Vector3.one * decalScale, Time.deltaTime);

            aimPointDirectionArrow.enabled = !(distance < GlobalSO.LocalGameSetting.AimPointWindDirectionHideRadius);

            var steps = new List<Vector3>();
            
            var initPoint = (int)(_ballTrajectory.Count / 10);
            
            for (var i = 0; i < 10; i++)
            {
                steps.Add(_ballTrajectory[initPoint * i].Position.ToVector3());
            }

            var widthCurve = new AnimationCurve();
            
            widthCurve.ClearKeys();

            var initTimeKey = 1f / steps.Count;
            
            // Scale 
            for (int i = 0; i < steps.Count; i++)
            {
                var distanceFromPointToCam = Vector3.Distance(cameraInCharge.transform.position, steps[i]);
                var scale = distanceFromPointToCam / LineScaleHighRatio;
                widthCurve.AddKey(initTimeKey * i, scale);
            }

            aimLineRenderer.widthCurve = widthCurve;
        }
        
        private Vector3 GetFocusZoomPos()
        {
            float screenWidth = Screen.width;
            float screenHeight = Screen.height;
            var targetPosition2D = new Vector2(screenWidth / 2, screenHeight / 2);
            int layerMask = GolfPhysics.Constant.TerrainLayerMask;
            
            Ray rayNow = mainCamera.ScreenPointToRay(targetPosition2D);
            
            RaycastHit hitNow;
            
            if (Physics.Raycast(rayNow, out hitNow, 2000, layerMask))
            {
                return rayNow.GetPoint(hitNow.distance);
            }
            return aimPointCenter.gameObject.transform.position;
        }

        private void CheckEdgeTouch(Vector3 position)
        {
            float screenWidth = Screen.width;

            if (position.x <= EdgeThreshold)
            {
                cameraInCharge.transform.RotateAround(cameraInCharge.transform.position, Vector3.up, -5f * Time.deltaTime);
                return;
            }
            else if (position.x >= screenWidth - EdgeThreshold)
            {
                cameraInCharge.transform.RotateAround(cameraInCharge.transform.position, Vector3.up, 5f * Time.deltaTime);
                return;
            }
            return;
        }

        #endregion

        public void ChangeGadgetRedrawTrajectory()
        {
            if (MasterManager.Instance.CurrentBallInCamera == LocalPlayerIndex)
            {
                cameraMediatorManager.SetActiveForLineRenderer(true);

                aimPointProjector.gameObject.SetActive(true);
                aimPointCenter.gameObject.SetActive(true);

                var autoAimPosition = cameraMediatorManager.GetCachedAimPosition();
                aimPointProjector.gameObject.transform.position = autoAimPosition;

                finalDistance = cameraMediatorManager.GetCachedFinalDistance();

                var trajectoryInput = cameraMediatorManager.HashSpeedAndSideAngle(false, autoAimPosition);
                cameraMediatorManager.Draw(out var drawTrajectory, trajectoryInput);
                _ballTrajectory = drawTrajectory;

                aimPointCenter.gameObject.transform.position = drawTrajectory[drawTrajectory.FirstBounceFrame].Position.ToVector3();

                // Set Camera Position Done then Calculate Zoom Object
                SetUpCamera(null);
            }
            else
            {
                // Set Camera Position Done then Calculate Zoom Object
                SetUpCamera(null);

                cameraMediatorManager.SetActiveForLineRenderer(false);
                trajectoryLineRenderer.StopDrawingTrajectory();

                aimPointProjector.gameObject.SetActive(false);
                aimPointCenter.gameObject.SetActive(false);
            }

            isSpinningPin = true;

            focusZoomPos = aimPointCenter.transform.position;

            ScaleSceneObject();

            aimLineRenderer.material.SetColor("_Color", Constant.DefaultLineColor);
            trajectoryLineRenderer.SetConfigToExact();
        }

    }

    public enum BallGuideStatus
    {
        NONE,
        DEFAULT,
        OVER,
    }
    
    public class InputTouch
    {
        public Vector3 position;
        public Vector3 lastPosition;

        public bool Moved;

        public InputTouch()
        {
            Moved = false;
        }

        public void Update(InputAction.CallbackContext context)
        {
            if (!Moved)
            {
                lastPosition = context.ReadValue<Vector2>();
            }
            else
            {
                lastPosition = new Vector3(position.x, position.y, position.z);
            }
            position = context.ReadValue<Vector2>();

            Moved = true;
        }

        public void Reset()
        {
            Moved = false;
        }
    }

    public class HashHelp
    {
        public float angle;
        public float distance;
        public List<float> milestoneList;
    }

    public class HashResult
    {
        public float velocity;
        public float delta;
    }

    public class HashAllResult
    {
        public float angle;
        public float distance;
        public List<float> milestoneList;
        public float velocity;
    }
}
