using _Golf.Physics.Data;
using _Golf.Scripts.Common;
using _Golf.Scripts.Core.CourseController;
using Cinemachine;
using System.Collections.Generic;
using _Golf.Scripts.ScriptableObjects;
using UnityEngine;
using UnityEngine.Rendering.Universal;

namespace GolfGame
{
    /// <summary>
    /// A state of the cinemachine brain in which the camera orbits hole after ball goes into it 
    /// </summary>
    public class CameraOrbitHoleLookUp : ICameraState
    {
        private CameraStateController controller;

        #region Cinemachine camera
        private Camera mainCamera;
        private CinemachineBrain cinemachineBrain;
        private Dictionary<CameraStateEnum, CinemachineVirtualCamera> virtualCameras;
        private CameraStateEnum key;
        private CinemachineVirtualCamera cameraInCharge1;
        private CinemachineVirtualCamera cameraInCharge2;
        private CinemachineVirtualCamera currentCamera;
        #endregion

        #region Scene objects
        private List<Ball> ballList;
        private int LocalPlayerIndex => ballList.IndexOf(ballList.Find(ball => ball.IsLocalBall()));
        private Ball LocalBall => ballList[LocalPlayerIndex];

        private Ball currentBall;

        private GameObject holePosition;
        private LineRenderer lineRenderer;
        #endregion

        #region Magic numbers
        float phase1Duration = 1.0f;
        float phase2Duration = 2.0f;
        float phase3Duration = 5.0f;

        float phase1Distance = 1.0f;
        float phase2Distance = 2.0f;

        float cameraDesignatedHeightFromHole1 = 0.16f;
        float cameraDesignatedHeightFromHole2 = 0.32f;
        #endregion

        float timeSpent = 0;
        bool phase2 = false; 
        bool phase3 = false;
        bool ended = false;

        bool effectPhase1Reached = false;
        bool effectPhase2Reached = false;

        private Ball ball;
        private BallTrajectory trajectory;

        #region ICameraState Implementation
        public CameraOrbitHoleLookUp(Camera _mainCamera,
            Dictionary<CameraStateEnum, CinemachineVirtualCamera> _virtualCameras,
            CameraStateEnum _key,
            CameraStateController _controller)
        {
            mainCamera = _mainCamera; cinemachineBrain = mainCamera.GetComponent<CinemachineBrain>();
            controller = _controller;

            virtualCameras = _virtualCameras;
            key = _key;

            cameraInCharge1 = virtualCameras[key];
            cameraInCharge2 = virtualCameras[CameraStateEnum.BALL_FOLLOW_AFTER_FIRST_ROLL];
        }

        public void OnEnter(object[] data)
        {
            if (data != null)
            {
                trajectory = (BallTrajectory)data[0];
                ball = (Ball)data[1];
            }

            cinemachineBrain.m_DefaultBlend = new CinemachineBlendDefinition(CinemachineBlendDefinition.Style.EaseInOut, 0.1f);
            cameraInCharge2.m_Lens.FieldOfView = 75;

            timeSpent = 0; phase2 = false; phase3 = false; ended = false;

            effectPhase1Reached = false;
            effectPhase2Reached = false;

            CameraToggle(2); MasterManager.Instance.SetRenderInterval(1);

            SetInitPos(phase1: true);

            controller.HideWhicheverBallInHole();
        }

        public void SetUpCamera(object[] data)
        {

        }

        public void OnExit()
        {
            cinemachineBrain.m_DefaultBlend = new CinemachineBlendDefinition(CinemachineBlendDefinition.Style.EaseInOut, 2f);
            cameraInCharge2.m_Lens.FieldOfView = 22;

            CameraToggle(0); MasterManager.Instance.SetRenderInterval(2);

            holePosition.GetComponent<HoleBehaviour>().StopHitHoleImpactEffectLoop();

            if (ended == false)
            {
                ended = true;

                ActionDispatcher.Dispatch(new CameraOrbitHoleFinishedToken(ball, trajectory));
            }

        }

        public void Update()
        {
            if (0.2f <= timeSpent && timeSpent < 2f)
            {
                if (!effectPhase1Reached)
                {
                    holePosition.GetComponent<HoleBehaviour>().PlayHitHoleImpactEffectLoop();

                    effectPhase1Reached = true;
                }
            }
            else if (2f <= timeSpent)
            {
                if (!effectPhase2Reached)
                {
                    var localPlayer = GlobalSO.GameplayBus.localLobby.LocalPlayers.Find(player =>
                        player.GetId() == GlobalSO.GameplayBus.localPlayer.GetId());

                    holePosition.GetComponent<HoleBehaviour>().PlayEffectFinisher(GlobalSO.PlayFieldSO.HoleInfo.Par
                        , ballList[MasterManager.Instance.CurrentBallInCamera].localPlayer.GetStrokeCount());

                    controller.ShowAllBall();

                    ActionDispatcher.Dispatch(new CameraOrbitHolePhase2StartToken());

                    effectPhase2Reached = true;
                }
            }
        }

        public void FixedUpdate()
        {

        }

        public void LateUpdate()
        {
            if (0 <= timeSpent && timeSpent < phase1Duration)
            {
                currentCamera.gameObject.transform.
                    RotateAround(holePosition.transform.position,
                    holePosition.transform.up, 
                    30 * Time.deltaTime
                );

                currentCamera.transform.LookAt(holePosition.transform.position);
            }
            else if (phase1Duration <= timeSpent && timeSpent < phase2Duration)
            {
                if (phase2 == false)
                {
                    phase2 = true;

                    CameraToggle(1);

                    SetInitPos(phase1: false);
                }

                currentCamera.gameObject.transform.
                    RotateAround(holePosition.transform.position,
                    holePosition.transform.up, 
                    45 * Time.deltaTime
                );

                currentCamera.transform.LookAt(holePosition.transform.position);
            }
            else if (phase2Duration <= timeSpent && timeSpent < phase3Duration)
            {
                if (phase3 == false)
                {
                    phase3 = true;

                    CameraToggle(3);

                    Lift();
                }
            }
            else
            {
                if (ended == false)
                {
                    ended = true;

                    ActionDispatcher.Dispatch(new CameraOrbitHoleFinishedToken(ball, trajectory));
                }
            }

            timeSpent += Time.deltaTime;
        }

        private void CameraToggle(int idx)
        {
            // 2 -> 1 -> 3
            if (idx == 0)
            {
                cameraInCharge1.gameObject.SetActive(false);
                cameraInCharge1.gameObject.SetActive(false);
            }
            else if (idx == 1)
            {
                cinemachineBrain.m_DefaultBlend = new CinemachineBlendDefinition(CinemachineBlendDefinition.Style.EaseInOut, .75f);
                cameraInCharge2.gameObject.SetActive(false);
                cameraInCharge1.gameObject.SetActive(true); currentCamera = cameraInCharge1;
            }
            else if (idx == 2)
            {
                cameraInCharge1.gameObject.SetActive(false);
                cameraInCharge2.gameObject.SetActive(true); currentCamera = cameraInCharge2;
            }
            else if (idx == 3)
            {
                cinemachineBrain.m_DefaultBlend = new CinemachineBlendDefinition(CinemachineBlendDefinition.Style.EaseInOut, .5f);
                cameraInCharge2.gameObject.SetActive(true);
                cameraInCharge1.gameObject.SetActive(false); currentCamera = cameraInCharge2;
            }
        }

        private void Lift()
        {
            currentCamera.gameObject.transform.position = mainCamera.gameObject.transform.position + Vector3.up * 0.1f;
            currentCamera.transform.LookAt(holePosition.transform.position + Vector3.up * 2.5f);
        }

        private void SetInitPos(bool phase1)
        {
            float currentPhaseDistance = 1f;
            float cameraDesignatedHeightFromHole = 0.2f;

            if (phase1) { currentPhaseDistance = phase1Distance; cameraDesignatedHeightFromHole = cameraDesignatedHeightFromHole1; }
            else { currentPhaseDistance = phase2Distance; cameraDesignatedHeightFromHole = cameraDesignatedHeightFromHole2; }

            Vector3 currentCameraPos = mainCamera.gameObject.transform.position;

            float yDifference = currentCameraPos.y - holePosition.transform.position.y;
            if (yDifference < cameraDesignatedHeightFromHole)
            {
                currentCameraPos += new Vector3(0, cameraDesignatedHeightFromHole - yDifference, 0);
            }
            else
            {
                currentCameraPos -= new Vector3(0, yDifference - cameraDesignatedHeightFromHole, 0);
            }

            float currentDistance = (mainCamera.gameObject.transform.position - holePosition.transform.position).magnitude;

            Vector3 finalPosition = Vector3.zero;

            if (currentDistance < currentPhaseDistance)
            {
                finalPosition = currentCameraPos +
                    (currentCameraPos - holePosition.transform.position) * (currentPhaseDistance - currentDistance);

                finalPosition = GolfUtility.RaycastDownToTerrain(finalPosition, cameraDesignatedHeightFromHole);
            }
            else
            {
                finalPosition = holePosition.transform.position +
                    (currentCameraPos - holePosition.transform.position).normalized * currentPhaseDistance;

                finalPosition = GolfUtility.RaycastDownToTerrain(finalPosition, cameraDesignatedHeightFromHole);
            }

            currentCamera.gameObject.transform.position = finalPosition;
        }

        public CameraStateEnum OnEvent(CameraControllerEvent e, object[] data)
        {
            return CameraStateEnum.NONE;
        }
        #endregion

        #region Data Injection
        public void SetHolePosition(GameObject _holePosition)
        {
            holePosition = _holePosition;
        }

        public void SetLineRenderer(LineRenderer _lineRenderer, CameraBirdEyeViewDrawTrajectoryLine trajectoryLine)
        {
            lineRenderer = _lineRenderer;
        }

        public void SetDecalProjector(DecalProjector _decalProjector, DecalProjector PinCenter, DecalProjector PinRadius)
        {

        }

        public void SetBall(List<Ball> balls)
        {
            ballList = balls;
        }
        #endregion
    }
}
