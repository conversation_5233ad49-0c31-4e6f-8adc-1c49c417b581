using _Golf.Physics.Data;
using _Golf.Scripts.Common;
using _Golf.Scripts.Core.CourseController;
using Cinemachine;
using System.Collections.Generic;
using _Golf.Scripts.ScriptableObjects;
using UnityEngine;
using UnityEngine.Rendering.Universal;
using GolfPhysics;

namespace GolfGame
{
    /// <summary>
    /// A state of the cinemachine brain in which the camera orbits hole after ball goes into it 
    /// </summary>
    public class CameraOrbitHole : ICameraState
    {
        private CameraStateController controller;

        #region Cinemachine camera
        private Camera mainCamera;
        private CinemachineBrain cinemachineBrain;
        private Dictionary<CameraStateEnum, CinemachineVirtualCamera> virtualCameras;
        private CameraStateEnum key;
        private CinemachineVirtualCamera cameraInCharge1;
        private CinemachineVirtualCamera cameraInCharge2;
        private CinemachineVirtualCamera currentCamera;
        #endregion

        #region Scene objects
        private List<Ball> ballList;
        private int LocalPlayerIndex => ballList.IndexOf(ballList.Find(ball => ball.IsLocalBall()));
        private Ball LocalBall => ballList[LocalPlayerIndex];

        private Ball currentBall;

        private GameObject holePosition;
        private LineRenderer lineRenderer;
        #endregion

        #region Effect timeline
        float effectPhase1 = 0.2f;
        float effectPhase2 = 2.0f; //

        bool effectPhase1Reached = false;
        bool effectPhase2Reached = false;
        #endregion

        #region Magic numbers
        float phase1Duration = 1.25f;
        float phase2Duration = 3.25f;
        float phase3Duration = 5.25f;

        float phase1Distance = 0.75f;
        float phase2Distance = 0.80f;
        float phase3Distance = 0.85f;

        float cameraDesignatedHeightFromHole1 = 0.10f;
        float cameraDesignatedHeightFromHole2 = 0.35f;
        float cameraDesignatedHeightFromHole3 = 0.25f;

        float lowHeightFromHoleLookAt = 0.1f;
        float mediumHeightFromHoleLookAt = 0.2f;

        float lowRotationSpeed = 5f;
        float mediumRotationSpeed = 10f;
        float highRotationSpeed = 25f;

        float phase1StartRotationSpeed = 50f;
        float phase1EndRotationSpeed = 5f;

        float darkenFadeTime = 1f;
        float lightenFadeTime = 0.1f;
        #endregion

        float timeSpent = 0;
        bool phase2 = false; bool phase3 = false; bool ended = false;

        Ball ball;
        BallTrajectory trajectory;

        #region ICameraState Implementation
        public CameraOrbitHole(Camera _mainCamera,
            Dictionary<CameraStateEnum, CinemachineVirtualCamera> _virtualCameras,
            CameraStateEnum _key,
            CameraStateController _controller)
        {
            mainCamera = _mainCamera; cinemachineBrain = mainCamera.GetComponent<CinemachineBrain>();
            controller = _controller;

            virtualCameras = _virtualCameras;
            key = _key;

            cameraInCharge1 = virtualCameras[key];
            cameraInCharge2 = virtualCameras[CameraStateEnum.BALL_FOLLOW_AFTER_FIRST_ROLL];
        }

        public void OnEnter(object[] data)
        {
            if (data != null)
            {
                trajectory = (BallTrajectory)data[0];
                ball = (Ball)data[1];
            }

            MasterManager.Instance.SetRenderInterval(1);

            timeSpent = 0; 
            
            phase2 = false; phase3 = false; ended = false;

            effectPhase1Reached = false;
            effectPhase2Reached = false;

            CameraToggle(0);

            SetInitPos(phase: 1);

            controller.HideWhicheverBallInHole();
            controller.SetActiveFlag(false);
        }

        public void SetUpCamera(object[] data)
        {

        }

        public void OnExit()
        {
            CameraToggle(3);

            controller.SetActiveFlag(true);

            MasterManager.Instance.SetRenderInterval(2);

            if (ended == false)
            {
                ended = true;

                ActionDispatcher.Dispatch(new CameraOrbitHoleFinishedToken(ball, trajectory));
            }
        }

        public void Update()
        {
            if (effectPhase1 <= timeSpent && timeSpent < effectPhase2)
            {
                if (!effectPhase1Reached)
                {
                    holePosition.GetComponent<HoleBehaviour>().PlayHitHoleImpactEffect();

                    controller.AdjustPostExposure(
                        GlobalSO.PlayFieldSO.GetOriginalPPColorAdjustmentValue() - CameraStateController.ColorAdjustmentOffetToDark,
                        darkenFadeTime
                    );

                    effectPhase1Reached = true;
                }
            }
            else if (effectPhase2 <= timeSpent)
            {
                if (!effectPhase2Reached)
                {
                    var localPlayer = GlobalSO.GameplayBus.localLobby.LocalPlayers.Find(player =>
                        player.GetId() == GlobalSO.GameplayBus.localPlayer.GetId());
                    
                    holePosition.GetComponent<HoleBehaviour>().PlayEffectFinisher(GlobalSO.PlayFieldSO.HoleInfo.Par
                        , ballList[MasterManager.Instance.CurrentBallInCamera].localPlayer.GetStrokeCount());

                    ActionDispatcher.Dispatch(new CameraOrbitHolePhase2StartToken());

                    effectPhase2Reached = true;
                }
            }
        }

        public void FixedUpdate()
        {

        }

        public void LateUpdate()
        {
            if (0 <= timeSpent && timeSpent < phase1Duration)
            {
                currentCamera.gameObject.transform.
                    RotateAround(holePosition.transform.position,
                    holePosition.transform.up, Mathf.Lerp(phase1StartRotationSpeed, phase1EndRotationSpeed, timeSpent / phase1Duration) * Time.deltaTime);

                currentCamera.transform.LookAt(holePosition.transform.position + Vector3.up * lowHeightFromHoleLookAt);
            }
            else if (phase1Duration <= timeSpent && timeSpent < phase2Duration)
            {
                if (phase2 == false)
                {
                    phase2 = true;

                    CameraToggle(1);

                    SetInitPos(phase: 2);
                }

                float speed = cinemachineBrain.IsBlending ? highRotationSpeed : lowRotationSpeed;

                currentCamera.gameObject.transform.
                    RotateAround(holePosition.transform.position,
                    holePosition.transform.up, speed * Time.deltaTime);
                currentCamera.transform.LookAt(holePosition.transform.position + Vector3.up * mediumHeightFromHoleLookAt);
            }
            else if (phase2Duration <= timeSpent && timeSpent < phase3Duration)
            {
                if (phase3 == false)
                {
                    phase3 = true;

                    CameraToggle(2);

                    SetInitPos(phase: 3);
                }

                currentCamera.gameObject.transform.
                    RotateAround(holePosition.transform.position,
                    holePosition.transform.up, mediumRotationSpeed * Time.deltaTime);
                currentCamera.transform.LookAt(holePosition.transform.position + Vector3.up * lowHeightFromHoleLookAt);
            }
            else
            {
                if (ended == false)
                {
                    ended = true;

                    controller.ShowAllBall();

                    controller.AdjustPostExposure(GlobalSO.PlayFieldSO.GetOriginalPPColorAdjustmentValue(), lightenFadeTime);

                    ActionDispatcher.Dispatch(new CameraOrbitHoleFinishedToken(ball, trajectory));
                }

                currentCamera.transform.LookAt(holePosition.transform.position + Vector3.up * lowHeightFromHoleLookAt);
            }

            timeSpent += Time.deltaTime;
        }

        private void CameraToggle(int idx)
        {
            switch (idx)
            {
                case 0:
                    {
                        cinemachineBrain.m_DefaultBlend = new CinemachineBlendDefinition(CinemachineBlendDefinition.Style.EaseInOut, 0.1f);

                        cameraInCharge1.gameObject.SetActive(false);
                        cameraInCharge2.gameObject.SetActive(true); currentCamera = cameraInCharge2;

                        currentCamera.m_Lens.FieldOfView = 60;

                        break;
                    }
                case 1:
                    {
                        cinemachineBrain.m_DefaultBlend = new CinemachineBlendDefinition(CinemachineBlendDefinition.Style.EaseInOut, 1f);
                        cameraInCharge2.gameObject.SetActive(false);
                        cameraInCharge1.gameObject.SetActive(true); currentCamera = cameraInCharge1;

                        currentCamera.m_Lens.FieldOfView = 60;

                        break;
                    }
                case 2:
                    {
                        cinemachineBrain.m_DefaultBlend = new CinemachineBlendDefinition(CinemachineBlendDefinition.Style.EaseInOut, 1f);
                        cameraInCharge1.gameObject.SetActive(false);
                        cameraInCharge2.gameObject.SetActive(true); currentCamera = cameraInCharge2;

                        break;
                    }
                case 3:
                    {
                        cameraInCharge1.gameObject.SetActive(false);
                        cameraInCharge2.gameObject.SetActive(false);

                        cinemachineBrain.m_DefaultBlend = new CinemachineBlendDefinition(CinemachineBlendDefinition.Style.EaseInOut, 2f);
                        cameraInCharge2.m_Lens.FieldOfView = 22;
                        break;
                    }
            }
        }

        private void SetInitPos(int phase)
        {
            float currentPhaseDistance = 1f;
            float cameraDesignatedHeightFromHole = 0.2f;

            if (phase == 1)
            {
                currentPhaseDistance = phase1Distance;
                cameraDesignatedHeightFromHole = cameraDesignatedHeightFromHole1;
            }
            else if (phase == 2)
            {
                currentPhaseDistance = phase2Distance;
                cameraDesignatedHeightFromHole = cameraDesignatedHeightFromHole2;
            }
            else if (phase == 3)
            {
                currentPhaseDistance = phase3Distance;
                cameraDesignatedHeightFromHole = cameraDesignatedHeightFromHole3;
            }

            Vector3 currentCameraPos = mainCamera.gameObject.transform.position;
            currentCameraPos = IsInHoleRadius(currentCameraPos);

            float yDifference = currentCameraPos.y - holePosition.transform.position.y;
            if (yDifference < cameraDesignatedHeightFromHole)
            {
                currentCameraPos += new Vector3(0, cameraDesignatedHeightFromHole - yDifference, 0);
            }
            else
            {
                currentCameraPos -= new Vector3(0, yDifference - cameraDesignatedHeightFromHole, 0);
            }

            float currentDistance = (mainCamera.gameObject.transform.position - holePosition.transform.position).magnitude;

            Vector3 finalPosition = Vector3.zero;

            if (currentDistance < currentPhaseDistance)
            {
                finalPosition = currentCameraPos +
                    (currentCameraPos - holePosition.transform.position) * (currentPhaseDistance - currentDistance);

                finalPosition = GolfUtility.RaycastDownToTerrain(finalPosition, cameraDesignatedHeightFromHole);
            }
            else
            {
                finalPosition = holePosition.transform.position +
                    (currentCameraPos - holePosition.transform.position).normalized * currentPhaseDistance;

                finalPosition = GolfUtility.RaycastDownToTerrain(finalPosition, cameraDesignatedHeightFromHole);
            }

            currentCamera.gameObject.transform.position = finalPosition;
        }

        public Vector3 IsInHoleRadius(Vector3 currentPosition)
        {
            Vector3 cameraPosition = new Vector3(currentPosition.x, holePosition.transform.position.y, currentPosition.z);

            float distance = (cameraPosition - holePosition.transform.position).magnitude;

            if (distance < Constant.radiusOfGolfBall_Metric)
            {
                cameraPosition = holePosition.transform.position +
                    (cameraPosition - holePosition.transform.position).normalized * (float)(Constant.radiusOfGolfBall_Metric * 4f);
            }

            return cameraPosition;
        }

        public CameraStateEnum OnEvent(CameraControllerEvent e, object[] data)
        {
            return CameraStateEnum.NONE;
        }
        #endregion

        #region Data Injection
        public void SetHolePosition(GameObject _holePosition)
        {
            holePosition = _holePosition;
        }

        public void SetLineRenderer(LineRenderer _lineRenderer, CameraBirdEyeViewDrawTrajectoryLine trajectoryLine)
        {
            lineRenderer = _lineRenderer;
        }

        public void SetDecalProjector(DecalProjector _decalProjector, DecalProjector PinCenter, DecalProjector PinRadius)
        {

        }

        public void SetBall(List<Ball> balls)
        {
            ballList = balls;
        }
        #endregion
    }
}
