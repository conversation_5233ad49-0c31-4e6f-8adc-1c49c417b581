using _Golf.Physics.Data;
using _Golf.Scripts.Common;
using _Golf.Scripts.Core.CourseController;
using _Golf.Scripts.ScriptableObjects;
using Cinemachine;
using System.Collections;
using System.Collections.Generic;
using System.Threading.Tasks;
using UnityEngine;
using UnityEngine.Rendering.Universal;

namespace GolfGame
{
    /// <summary>
    /// A state of the cinemachine brain in which the camera orbits hole while descending with a particle
    /// </summary>
    public class CameraOrbitHoleDescend : ICameraState
    {
        private CameraStateController controller;

        #region Cinemachine camera
        private Camera mainCamera;
        private CinemachineBrain cinemachineBrain;
        private Dictionary<CameraStateEnum, CinemachineVirtualCamera> virtualCameras;
        private CameraStateEnum key;
        private CinemachineVirtualCamera cameraInCharge1;
        private CinemachineVirtualCamera cameraInCharge2;
        private CinemachineVirtualCamera currentCamera;
        #endregion

        #region Scene objects
        private List<Ball> ballList;
        private int LocalPlayerIndex => ballList.IndexOf(ballList.Find(ball => ball.IsLocalBall()));
        private Ball LocalBall => ballList[LocalPlayerIndex];

        private Ball currentBall;

        private GameObject holePosition; private HoleBehaviour holeBehaviour;
        private LineRenderer lineRenderer;
        #endregion

        #region Effect timeline
        float effectPhase1 = 0.00f;
        float effectPhase2 = 1.50f;
        float effectPhase3 = 3.50f;

        bool effectPhase1Reached = false;
        bool effectPhase2Reached = false;
        bool effectPhase3Reached = false;

        ParticleGroupBehaviour eagleDescendEfx;
        ParticleSystem.Particle eagleDescendParticle;
        #endregion

        #region Camera movement
        float phase1Duration = 1.50f;
        float phase12Duration = 3.00f;
        float phase2Duration = 4.00f;
        float phase3Duration = 6.00f;

        bool particleInit = false;
        bool phase12 = false;
        bool phase2 = false;
        bool phase3 = false;
        bool ended = false;

        float particleActualTopPosition = 6.33f;
        float particleActualBotPosition = 0.00f;

        float rotationTotal = 90f;
        float lowRotationSpeed = 5f;
        float mediumRotationSpeed = 10f;
        float highRotationSpeed = 25f;

        float lookAtActualTopPosition = 5f;
        float lookAtActualBotPosition = 0.00f;

        float originalHeight = 2.0f;
        float descendAmount = 1.65f;
        float descendStartHorizontalLimit = 0.75f;
        float descendEndHorizontalLimit = 0.80f;
        float zoomInHorizontalLimit = 0.70f;

        float phase2Distance = 0.80f;
        float phase3Distance = 0.85f;

        float cameraDesignatedHeightFromHole2 = 0.35f;
        float cameraDesignatedHeightFromHole3 = 0.25f;

        float heightFromHoleLookAt = 0.2f;

        float darkenFadeTime = 1f;
        float lightenFadeTime = 0.1f;

        Vector3 phase1StartPosition;
        Vector3 phase1FinishPosition;
        Vector3 phase12FinishPosition;
        #endregion

        float timeSpent = 0;

        Ball ball;
        BallTrajectory trajectory;

        #region ICameraState Implementation
        public CameraOrbitHoleDescend(Camera _mainCamera,
            Dictionary<CameraStateEnum, CinemachineVirtualCamera> _virtualCameras,
            CameraStateEnum _key,
            CameraStateController _controller)
        {
            mainCamera = _mainCamera; cinemachineBrain = mainCamera.GetComponent<CinemachineBrain>();
            controller = _controller;

            virtualCameras = _virtualCameras;
            key = _key;

            cameraInCharge1 = virtualCameras[key];
            cameraInCharge2 = virtualCameras[CameraStateEnum.BALL_FOLLOW_AFTER_FIRST_ROLL];
        }

        public void OnEnter(object[] data)
        {
            if (data != null)
            {
                trajectory = (BallTrajectory)data[0];
                ball = (Ball)data[1];
            }

            timeSpent = 0; 
            
            particleInit = false;
            phase12 = false;
            phase2 = false;
            phase3 = false;
            ended = false;

            effectPhase1Reached = false;
            effectPhase2Reached = false;
            effectPhase3Reached = false;

            SetUpCamera(data);
            CameraToggle(0); MasterManager.Instance.SetRenderInterval(1);

            SetPos(0);

            controller.HideWhicheverBallInHole();
            controller.SetActiveFlag(false);
        }

        public void SetUpCamera(object[] data)
        {
            
        }

        private void CameraToggle(int idx)
        {
            if (idx == 0)
            {
                cinemachineBrain.m_DefaultBlend = new CinemachineBlendDefinition(CinemachineBlendDefinition.Style.Cut, 0f);

                cameraInCharge1.gameObject.SetActive(false);
                cameraInCharge2.gameObject.SetActive(true); 
                
                currentCamera = cameraInCharge2;
                currentCamera.m_Lens.FieldOfView = 75;
            }
            else if (idx == 1)
            {
                cinemachineBrain.m_DefaultBlend = new CinemachineBlendDefinition(CinemachineBlendDefinition.Style.EaseInOut, 1.5f);
                cameraInCharge2.gameObject.SetActive(false);
                cameraInCharge1.gameObject.SetActive(true); 
                
                currentCamera = cameraInCharge1;
                currentCamera.m_Lens.FieldOfView = 60;
            }
            else if (idx == 2)
            {
                cinemachineBrain.m_DefaultBlend = new CinemachineBlendDefinition(CinemachineBlendDefinition.Style.EaseInOut, 0.75f);
                cameraInCharge1.gameObject.SetActive(false);
                cameraInCharge2.gameObject.SetActive(true);

                currentCamera = cameraInCharge2;
                currentCamera.m_Lens.FieldOfView = 75;
            }
            else if (idx == 3)
            {
                cinemachineBrain.m_DefaultBlend = new CinemachineBlendDefinition(CinemachineBlendDefinition.Style.EaseInOut, 1.0f);
                cameraInCharge2.gameObject.SetActive(false);
                cameraInCharge1.gameObject.SetActive(true);

                currentCamera = cameraInCharge1;
                currentCamera.m_Lens.FieldOfView = 75;
            }
            else if (idx == 4)
            {
                cameraInCharge1.gameObject.SetActive(false);
                cameraInCharge2.gameObject.SetActive(false);

                cinemachineBrain.m_DefaultBlend = new CinemachineBlendDefinition(CinemachineBlendDefinition.Style.EaseInOut, 2f);
                cameraInCharge2.m_Lens.FieldOfView = 22;

            }
        }

        private async Task WaitForParticleAsync(ParticleGroupBehaviour particleGroupBehaviour)
        {
            // Ensure the ParticleGroupBehaviour instance is assigned
            if (particleGroupBehaviour == null)
            {
                return;
            }

            // Wait for the first particle asynchronously
            ParticleSystem.Particle firstParticle = await particleGroupBehaviour.WaitForAndGetFirstParticleAsync();

            eagleDescendParticle = firstParticle;

            particleInit = true;
        }

        public void Update()
        {
            if (effectPhase1 <= timeSpent && timeSpent < effectPhase2)
            {
                if (!effectPhase1Reached)
                {
                    eagleDescendEfx = holeBehaviour.PlayEagleDescend();
                    _ = WaitForParticleAsync(eagleDescendEfx);

                    controller.AdjustPostExposure(GlobalSO.PlayFieldSO.GetOriginalPPColorAdjustmentValue() - CameraStateController.ColorAdjustmentOffetToDark, darkenFadeTime);

                    effectPhase1Reached = true;
                }
            }
            else if (effectPhase2 <= timeSpent && timeSpent < effectPhase3)
            {
                if (!effectPhase2Reached)
                {
                    holeBehaviour.PlayEagleImplosion();

                    effectPhase2Reached = true;
                }
            }
            else if (effectPhase3 <= timeSpent)
            {
                if (!effectPhase3Reached)
                {
                    var localPlayer = GlobalSO.GameplayBus.localLobby.LocalPlayers.Find(player =>
                        player.GetId() == GlobalSO.GameplayBus.localPlayer.GetId());

                    holeBehaviour.PlayEffectFinisher(
                        GlobalSO.PlayFieldSO.HoleInfo.Par,
                        ballList[MasterManager.Instance.CurrentBallInCamera].localPlayer.GetStrokeCount()
                    );

                    effectPhase3Reached = true;

                    ActionDispatcher.Dispatch(new CameraOrbitHolePhase2StartToken());
                }
            }
        }

        public void LateUpdate()
        {
            if (0 <= timeSpent && timeSpent < phase1Duration)
            {
                if (particleInit)
                {
                    eagleDescendParticle = eagleDescendEfx.GetParticle();

                    float currentParticleHeight = eagleDescendParticle.position.y - holePosition.transform.position.y;
                    float currentParticleProgress = (particleActualTopPosition - currentParticleHeight) / (particleActualTopPosition - particleActualBotPosition);
                    currentParticleProgress = Mathf.Clamp(currentParticleProgress, 0, 1);

                    float currentInnerLimit = Mathf.Lerp(descendStartHorizontalLimit, descendEndHorizontalLimit, currentParticleProgress);

                    Vector3 currentPosition = holePosition.transform.position - new Vector3(currentInnerLimit, 0, 0);

                    float currentPositionY = originalHeight - descendAmount * currentParticleProgress + holePosition.transform.position.y;
                    currentPosition.y = currentPositionY;

                    Vector3 centerPosition = new Vector3(holePosition.transform.position.x, currentPosition.y, holePosition.transform.position.z);
                    Vector3 centerToCurrentPosition = currentPosition - centerPosition;

                    centerToCurrentPosition = Quaternion.Euler(0, rotationTotal * timeSpent / phase1Duration, 0) * centerToCurrentPosition;
                    currentPosition = centerPosition + centerToCurrentPosition;

                    currentCamera.gameObject.transform.position = currentPosition;

                    // cache camera position
                    phase1FinishPosition = currentCamera.gameObject.transform.position;

                    // Worthy of note, position of particle does not reflect the true position of the particle that is observable
                    // This debug is used to calculate the offset between the two
                    // Debug.Log(eagleDescendParticle.position + " / " + holePosition.transform.position);

                    currentCamera.transform.LookAt(holePosition.transform.position +
                        Vector3.up * lookAtActualTopPosition -
                        Vector3.up * (lookAtActualTopPosition - lookAtActualBotPosition) * currentParticleProgress
                    );
                }
                else
                {
                    currentCamera.gameObject.transform.position = phase1StartPosition;
                    currentCamera.transform.LookAt(holePosition.transform.position + Vector3.up * (particleActualTopPosition)); // look at particle first position
                }
            }
            else if (phase1Duration <= timeSpent && timeSpent < phase12Duration)
            {
                if (phase12 == false)
                {
                    phase12 = true;

                    CameraToggle(1);

                    SetPos(1);
                }

                currentCamera.transform.LookAt(holePosition.transform.position);
            }
            else if (phase12Duration <= timeSpent && timeSpent < phase2Duration)
            {
                if (phase2 == false)
                {
                    phase2 = true;

                    CameraToggle(2);

                    SetPos(2);
                }

                float speed = cinemachineBrain.IsBlending ? highRotationSpeed : lowRotationSpeed;

                currentCamera.gameObject.transform.
                    RotateAround(holePosition.transform.position,
                    holePosition.transform.up, speed * Time.deltaTime);

                currentCamera.transform.LookAt(holePosition.transform.position + Vector3.up * heightFromHoleLookAt);
            }
            else if (phase2Duration <= timeSpent && timeSpent < phase3Duration)
            {
                if (phase3 == false)
                {
                    phase3 = true;

                    CameraToggle(3);

                    SetPos(3);
                }

                float speed = mediumRotationSpeed;

                currentCamera.gameObject.transform.
                    RotateAround(holePosition.transform.position,
                    holePosition.transform.up, speed * Time.deltaTime);

                currentCamera.transform.LookAt(holePosition.transform.position + Vector3.up * heightFromHoleLookAt);
            }
            else
            {
                if (ended == false)
                {
                    controller.ShowAllBall();

                    controller.AdjustPostExposure(GlobalSO.PlayFieldSO.GetOriginalPPColorAdjustmentValue(), lightenFadeTime);

                    ended = true;

                    ActionDispatcher.Dispatch(new CameraOrbitHoleFinishedToken(ball, trajectory));
                }

                currentCamera.transform.LookAt(holePosition.transform.position + Vector3.up * heightFromHoleLookAt);
            }

            timeSpent += Time.deltaTime;
        }

        public void FixedUpdate()
        {

        }

        public void OnExit()
        {
            CameraToggle(4); MasterManager.Instance.SetRenderInterval(2);

            if (ended == false)
            {
                ended = true;

                ActionDispatcher.Dispatch(new CameraOrbitHoleFinishedToken(ball, trajectory));
            }
        }

        public CameraStateEnum OnEvent(CameraControllerEvent e, object[] data)
        {
            return CameraStateEnum.NONE;
        }
        #endregion


        #region Data Injection
        public void SetHolePosition(GameObject _holePosition)
        {
            holePosition = _holePosition;
            holeBehaviour = holePosition.GetComponent<HoleBehaviour>();
        }

        public void SetLineRenderer(LineRenderer _lineRenderer, CameraBirdEyeViewDrawTrajectoryLine trajectoryLine)
        {
            lineRenderer = _lineRenderer;
        }

        public void SetDecalProjector(DecalProjector _decalProjector, DecalProjector PinCenter, DecalProjector PinRadius)
        {

        }

        public void SetBall(List<Ball> balls)
        {
            ballList = balls;
        }
        #endregion

        private void SetPos(int phase)
        {
            float currentPhaseDistance = 1f;
            float cameraDesignatedHeightFromHole = 0.2f;

            if (phase == 0)
            {
                phase1StartPosition = new Vector3(
                        holePosition.transform.position.x,
                        holePosition.transform.position.y + originalHeight,
                        holePosition.transform.position.z
                    ) - Vector3.right * descendStartHorizontalLimit;

                currentCamera.transform.position = phase1StartPosition;

                return;
            }
            else if (phase == 1)
            {
                phase12FinishPosition = holePosition.transform.position
                    + Vector3.down * 0.1f
                    + (phase1FinishPosition - holePosition.transform.position).normalized * zoomInHorizontalLimit;

                currentCamera.transform.position = phase12FinishPosition;

                return;
            }
            else if (phase == 2)
            {
                currentPhaseDistance = phase2Distance;
                cameraDesignatedHeightFromHole = cameraDesignatedHeightFromHole2;
            }
            else if (phase == 3)
            {
                currentPhaseDistance = phase3Distance;
                cameraDesignatedHeightFromHole = cameraDesignatedHeightFromHole3;
            }

            Vector3 currentCameraPos = mainCamera.gameObject.transform.position;

            float yDifference = currentCameraPos.y - holePosition.transform.position.y;
            if (yDifference < cameraDesignatedHeightFromHole)
            {
                currentCameraPos += new Vector3(0, cameraDesignatedHeightFromHole - yDifference, 0);
            }
            else
            {
                currentCameraPos -= new Vector3(0, yDifference - cameraDesignatedHeightFromHole, 0);
            }

            float currentDistance = (mainCamera.gameObject.transform.position - holePosition.transform.position).magnitude;

            Vector3 finalPosition = Vector3.zero;

            if (currentDistance < currentPhaseDistance)
            {
                finalPosition = currentCameraPos +
                    (currentCameraPos - holePosition.transform.position) * (currentPhaseDistance - currentDistance);

                finalPosition = GolfUtility.RaycastDownToTerrain(finalPosition, cameraDesignatedHeightFromHole);
            }
            else
            {
                finalPosition = holePosition.transform.position +
                    (currentCameraPos - holePosition.transform.position).normalized * currentPhaseDistance;

                finalPosition = GolfUtility.RaycastDownToTerrain(finalPosition, cameraDesignatedHeightFromHole);
            }

            currentCamera.gameObject.transform.position = finalPosition;
        }
    }
}
