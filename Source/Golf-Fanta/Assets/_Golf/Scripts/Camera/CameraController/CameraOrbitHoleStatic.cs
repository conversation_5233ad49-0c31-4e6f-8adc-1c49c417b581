using _Golf.Physics.Data;
using _Golf.Scripts.Common;
using _Golf.Scripts.Core.CourseController;
using _Golf.Scripts.ScriptableObjects;
using Cinemachine;
using GolfPhysics;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Rendering.Universal;

namespace GolfGame
{
    /// <summary>
    /// A state of the cinemachine brain in which the camera orbits hole after ball goes into it 
    /// </summary>
    public class CameraOrbitHoleStatic : ICameraState
    {
        private CameraStateController controller;

        #region Cinemachine camera
        private Camera mainCamera;
        private CinemachineBrain cinemachineBrain;
        private Dictionary<CameraStateEnum, CinemachineVirtualCamera> virtualCameras;
        private CameraStateEnum key;
        private CinemachineVirtualCamera cameraInCharge1;
        private CinemachineVirtualCamera cameraInCharge2;
        private CinemachineVirtualCamera currentCamera;
        #endregion

        #region Scene objects
        private List<Ball> ballList;
        private int LocalPlayerIndex => ballList.IndexOf(ballList.Find(ball => ball.IsLocalBall()));
        private Ball LocalBall => ballList[LocalPlayerIndex];

        private Ball currentBall;

        private GameObject holePosition;
        private LineRenderer lineRenderer;
        #endregion

        #region Magic numbers
        float effectPhase1 = 1.3f;
        float effectPhase2 = 2.0f;

        float phase1Duration = 3.0f;

        float phase1Distance = 0.45f;
        float phase2Distance = 0.50f;

        float cameraDesignatedHeightFromHole1 = 0.10f;
        float cameraDesignatedHeightFromHole2 = 0.125f;

        float heightFromHoleLookAt = 0.2f;

        float darkenFadeTime = 1f;
        float lightenFadeTime = 0.1f;

        float lowRotationSpeed = 0f;
        float mediumRotationSpeed = 10f;
        #endregion

        float timeSpent = 0;

        bool phase1 = false;
        bool ended = false;

        bool effectPhase1Reached = false;

        private Ball ball;
        private BallTrajectory trajectory;

        #region ICameraState Implementation
        public CameraOrbitHoleStatic(Camera _mainCamera,
            Dictionary<CameraStateEnum, CinemachineVirtualCamera> _virtualCameras,
            CameraStateEnum _key,
            CameraStateController _controller)
        {
            mainCamera = _mainCamera; cinemachineBrain = mainCamera.GetComponent<CinemachineBrain>();
            controller = _controller;

            virtualCameras = _virtualCameras;
            key = _key;

            cameraInCharge1 = virtualCameras[key];
            cameraInCharge2 = virtualCameras[CameraStateEnum.BALL_FOLLOW_AFTER_FIRST_ROLL];
        }

        public void OnEnter(object[] data)
        {
            if (data != null)
            {
                trajectory = (BallTrajectory)data[0];
                ball = (Ball)data[1];
            }

            timeSpent = 0;

            phase1 = false;
            ended = false;

            effectPhase1Reached = false;

            CameraToggle(0); MasterManager.Instance.SetRenderInterval(1);

            SetInitPos(1);

            controller.HideWhicheverBallInHole();
            controller.SetActiveFlag(false);
        }

        public void SetUpCamera(object[] data)
        {

        }

        public void OnExit()
        {
            cinemachineBrain.m_DefaultBlend = new CinemachineBlendDefinition(CinemachineBlendDefinition.Style.EaseInOut, 2f);
            cameraInCharge2.m_Lens.FieldOfView = 22;

            CameraToggle(2); MasterManager.Instance.SetRenderInterval(2);

            if (ended == false)
            {
                ended = true;

                ActionDispatcher.Dispatch(new CameraOrbitHoleFinishedToken(ball, trajectory));
            }
        }

        public void Update()
        {
            if (effectPhase1 <= timeSpent && timeSpent < effectPhase2)
            {
                if (!effectPhase1Reached)
                {
                    var localPlayer = GlobalSO.GameplayBus.localLobby.LocalPlayers.Find(player =>
                        player.GetId() == GlobalSO.GameplayBus.localPlayer.GetId());

                    holePosition.GetComponent<HoleBehaviour>().PlayEffectFinisher(GlobalSO.PlayFieldSO.HoleInfo.Par
                        , ballList[MasterManager.Instance.CurrentBallInCamera].localPlayer.GetStrokeCount());

                    effectPhase1Reached = true;

                    ActionDispatcher.Dispatch(new CameraOrbitHolePhase2StartToken());

                    controller.ShowAllBall();
                }
            }
        }

        public void FixedUpdate()
        {

        }

        public void LateUpdate()
        {
            if (0 <= timeSpent && timeSpent < phase1Duration)
            {
                if (phase1 == false)
                {
                    phase1 = true;

                    CameraToggle(1);

                    SetInitPos(2);

                    controller.AdjustPostExposure(GlobalSO.PlayFieldSO.GetOriginalPPColorAdjustmentValue() - CameraStateController.ColorAdjustmentOffetToDark, darkenFadeTime);
                }

                float speed = cinemachineBrain.IsBlending ? mediumRotationSpeed : lowRotationSpeed;

                currentCamera.gameObject.transform.
                    RotateAround(holePosition.transform.position,
                    holePosition.transform.up, speed * Time.deltaTime);
            }
            else
            {
                if (ended == false)
                {
                    ended = true;

                    // controller.SetActiveFlag(true);

                    controller.AdjustPostExposure(GlobalSO.PlayFieldSO.GetOriginalPPColorAdjustmentValue(), lightenFadeTime);

                    ActionDispatcher.Dispatch(new CameraOrbitHoleFinishedToken(ball, trajectory));
                }
            }

            currentCamera.transform.LookAt(holePosition.transform.position + Vector3.up * heightFromHoleLookAt);

            timeSpent += Time.deltaTime;
        }

        private void CameraToggle(int idx)
        {
            if (idx == 0)
            {
                cinemachineBrain.m_DefaultBlend = new CinemachineBlendDefinition(CinemachineBlendDefinition.Style.Cut, 0f);
                cameraInCharge2.m_Lens.FieldOfView = 75;

                cameraInCharge1.gameObject.SetActive(false);
                cameraInCharge2.gameObject.SetActive(true); currentCamera = cameraInCharge2;
            }
            else if (idx == 1)
            {
                cinemachineBrain.m_DefaultBlend = new CinemachineBlendDefinition(CinemachineBlendDefinition.Style.EaseInOut, 2f);
                cameraInCharge2.gameObject.SetActive(false);
                cameraInCharge1.gameObject.SetActive(true); currentCamera = cameraInCharge1;
            }
            else if (idx == 2)
            {
                cameraInCharge1.gameObject.SetActive(false);
                cameraInCharge1.gameObject.SetActive(false);
            }
        }

        private void SetInitPos(int phase)
        {
            float currentPhaseDistance = 1f;
            float cameraDesignatedHeightFromHole = 0.2f;

            if (phase == 1)
            {
                currentPhaseDistance = phase1Distance;
                cameraDesignatedHeightFromHole = cameraDesignatedHeightFromHole1;
            }
            else if (phase == 2)
            {
                currentPhaseDistance = phase2Distance;
                cameraDesignatedHeightFromHole = cameraDesignatedHeightFromHole2;
            }

            Vector3 currentCameraPos = mainCamera.gameObject.transform.position;
            currentCameraPos = IsInHoleRadius(currentCameraPos);

            float yDifference = currentCameraPos.y - holePosition.transform.position.y;
            if (yDifference < cameraDesignatedHeightFromHole)
            {
                currentCameraPos += new Vector3(0, cameraDesignatedHeightFromHole - yDifference, 0);
            }
            else
            {
                currentCameraPos -= new Vector3(0, yDifference - cameraDesignatedHeightFromHole, 0);
            }

            float currentDistance = (mainCamera.gameObject.transform.position - holePosition.transform.position).magnitude;

            Vector3 finalPosition = Vector3.zero;

            if (currentDistance < currentPhaseDistance)
            {
                finalPosition = currentCameraPos +
                    (currentCameraPos - holePosition.transform.position) * (currentPhaseDistance - currentDistance);

                finalPosition = GolfUtility.RaycastDownToTerrain(finalPosition, cameraDesignatedHeightFromHole);
            }
            else
            {
                finalPosition = holePosition.transform.position +
                    (currentCameraPos - holePosition.transform.position).normalized * currentPhaseDistance;

                finalPosition = GolfUtility.RaycastDownToTerrain(finalPosition, cameraDesignatedHeightFromHole);
            }

            currentCamera.gameObject.transform.position = finalPosition;
        }

        public Vector3 IsInHoleRadius(Vector3 currentPosition)
        {
            Vector3 cameraPosition = new Vector3(currentPosition.x, holePosition.transform.position.y, currentPosition.z);

            float distance = (cameraPosition - holePosition.transform.position).magnitude;

            if (distance < Constant.radiusOfGolfBall_Metric)
            {
                cameraPosition = holePosition.transform.position +
                    (cameraPosition - holePosition.transform.position).normalized * (float)(Constant.radiusOfGolfBall_Metric * 4f);
            }

            return cameraPosition;
        }

        public CameraStateEnum OnEvent(CameraControllerEvent e, object[] data)
        {
            return CameraStateEnum.NONE;
        }
        #endregion

        #region Data Injection
        public void SetHolePosition(GameObject _holePosition)
        {
            holePosition = _holePosition;
        }

        public void SetLineRenderer(LineRenderer _lineRenderer, CameraBirdEyeViewDrawTrajectoryLine trajectoryLine)
        {
            lineRenderer = _lineRenderer;
        }

        public void SetDecalProjector(DecalProjector _decalProjector, DecalProjector PinCenter, DecalProjector PinRadius)
        {

        }

        public void SetBall(List<Ball> balls)
        {
            ballList = balls;
        }
        #endregion
    }
}
