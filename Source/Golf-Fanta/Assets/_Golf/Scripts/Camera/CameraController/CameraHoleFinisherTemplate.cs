using _Golf.Physics.Data;
using _Golf.Scripts.Common;
using _Golf.Scripts.Core.CourseController;
using Cinemachine;
using System.Collections.Generic;
using _Golf.Scripts.ScriptableObjects;
using UnityEngine;
using UnityEngine.Rendering.Universal;
using GolfPhysics;
using Unity.VectorGraphics;
using Unity.VisualScripting.Antlr3.Runtime;

namespace GolfGame
{
    /// <summary>
    /// A state of the cinemachine brain which is a template for camera movement for hole finisher effects.
    /// </summary>
    public class CameraHoleFinisherTemplate : ICameraState
    {
        private CameraStateController controller;

        #region Cinemachine camera
        private Camera mainCamera;
        private CinemachineBrain cinemachineBrain;
        private Dictionary<CameraStateEnum, CinemachineVirtualCamera> virtualCameras;
        private CameraStateEnum key;
        private CinemachineVirtualCamera cameraInCharge1;
        private CinemachineVirtualCamera cameraInCharge2;
        private CinemachineVirtualCamera currentCamera;
        #endregion

        #region Scene objects
        private List<Ball> ballList;
        private int LocalPlayerIndex => ballList.IndexOf(ballList.Find(ball => ball.IsLocalBall()));
        private Ball LocalBall => ballList[LocalPlayerIndex];

        private Ball currentBall;

        private GameObject holePosition; private HoleBehaviour holeBehaviour;
        private LineRenderer lineRenderer;
        #endregion

        #region Effect timeline
        List<float> effectTimestamps;
        List<bool> effectCheckPoints;
        #endregion

        #region Camera timeline
        List<float> cameraTimestamps;
        List<bool> cameraCheckPoints;
        #endregion

        Ball ball;
        BallTrajectory trajectory;

        GolfShotRanking shotRanking;
        HoleFinisherEffectType effectType;

        float timeSpent = 0;

        #region ICameraState Implementation
        public CameraHoleFinisherTemplate(Camera _mainCamera,
            Dictionary<CameraStateEnum, CinemachineVirtualCamera> _virtualCameras,
            CameraStateEnum _key,
            CameraStateController _controller)
        {
            mainCamera = _mainCamera; cinemachineBrain = mainCamera.GetComponent<CinemachineBrain>();
            controller = _controller;

            virtualCameras = _virtualCameras;
            key = _key;

            cameraInCharge1 = virtualCameras[key];
            cameraInCharge2 = virtualCameras[CameraStateEnum.BALL_FOLLOW_AFTER_FIRST_ROLL];
        }

        public void OnEnter(object[] data)
        {
            if (data != null)
            {
                trajectory = (BallTrajectory)data[0];
                ball = (Ball)data[1];
            }

            MasterManager.Instance.SetRenderInterval(1);

            timeSpent = 0;

            controller.SetActiveFlag(false);

            shotRanking = GolfUtility.GetPoint(GlobalSO.PlayFieldSO.HoleInfo.Par, ballList[MasterManager.Instance.CurrentBallInCamera].localPlayer.GetStrokeCount());
            if (GlobalSO.LocalGameSetting.FinisherDebugging)
            {
                shotRanking = GlobalSO.LocalGameSetting.Finisher;
            }
            
            effectType = GolfUtility.GetHoleEffectType(shotRanking);

            SetUpEffectTimestamps();
            SetUpCameraTimestamps();
        }

        public void SetUpCamera(object[] data)
        {

        }

        public void Update()
        {

        }

        public void FixedUpdate()
        {

        }

        public void LateUpdate()
        {
            UpdateEffectPhase();
            UpdateCameraPhase();

            timeSpent += Time.deltaTime;
        }

        public void OnExit()
        {
            // controller.SetActiveFlag(true);

            MasterManager.Instance.SetRenderInterval(2);
        }

        public CameraStateEnum OnEvent(CameraControllerEvent e, object[] data)
        {
            return CameraStateEnum.NONE;
        }
        #endregion

        #region Template Mechanics
        public void SetUpEffectTimeLineCheckPoints(int count)
        {
            effectCheckPoints = new List<bool>();

            for (int i = 0; i < count; i++)
            {
                effectCheckPoints.Add(false);
            }
        }

        public void SetUpCameraTimeLineCheckPoints(int count)
        {
            cameraCheckPoints = new List<bool>();

            for (int i = 0; i < count; i++)
            {
                cameraCheckPoints.Add(false);
            }
        }

        public bool CheckEffectCheckPoint(int index)
        {
            return effectCheckPoints[index];
        }

        public void SetEffectCheckPoint(int index)
        {
            effectCheckPoints[index] = true;
        }

        public void UpdateEffectPhase()
        {
            for (int i = 0; i < effectTimestamps.Count; i++)
            {
                bool isInPhase = false;

                if (i == effectTimestamps.Count - 1)
                {
                    isInPhase = (effectTimestamps[i] <= timeSpent);
                }
                else
                {
                    isInPhase = (effectTimestamps[i] <= timeSpent && timeSpent < effectTimestamps[i + 1]);
                }

                if (isInPhase)
                {
                    if (CheckEffectCheckPoint(i) == false)
                    {
                        SetEffectCheckPoint(i);
                        EffectPhaseStartAction(i);
                    }
                }
            }
        }

        public bool CheckCameraCheckPoint(int index)
        {
            return cameraCheckPoints[index];
        }

        public void SetCameraCheckPoint(int index)
        {
            cameraCheckPoints[index] = true;
        }

        public void UpdateCameraPhase()
        {
            for (int i = 0; i < cameraTimestamps.Count; i++)
            {
                bool isInPhase = false;

                if (i == cameraTimestamps.Count - 1)
                {
                    isInPhase = (cameraTimestamps[i] <= timeSpent);
                }
                else
                {
                    isInPhase = (cameraTimestamps[i] <= timeSpent && timeSpent < cameraTimestamps[i + 1]);
                }

                if (isInPhase)
                {
                    if (CheckCameraCheckPoint(i) == false)
                    {
                        SetCameraCheckPoint(i);
                        CameraPhaseStartAction(i);
                    }
                }
            }
        }
        #endregion

        #region Template Adjustable
        public void SetUpEffectTimestamps()
        {
            switch (effectType)
            {
                case HoleFinisherEffectType.Zero:
                    {
                        effectTimestamps = new List<float>
                        {
                            0.00f,
                            1.00f,
                            1.25f
                        };
                        SetUpEffectTimeLineCheckPoints(effectTimestamps.Count);
                        break;
                    }
                case HoleFinisherEffectType.One:
                    {
                        effectTimestamps = new List<float>
                        {
                            0.00f,
                            1.12f,
                            1.25f,
                            2.12f,
                            3.70f,
                            5.00f
                        };
                        SetUpEffectTimeLineCheckPoints(effectTimestamps.Count);
                        break;
                    }
            }
        }

        public void EffectPhaseStartAction(int index)
        {
            switch (effectType)
            {
                case HoleFinisherEffectType.Zero:
                    {
                        switch (index)
                        {
                            case 0:
                                {
                                    break;
                                }
                            case 1:
                                {
                                    break;
                                }
                            case 2:
                                {
                                    ActionDispatcher.Dispatch(new CameraOrbitHolePhase2StartToken());

                                    switch (shotRanking)
                                    {
                                        case GolfShotRanking.Eagle:
                                            {
                                                holeBehaviour.PlayVfxOneShot(HoleVfx.Eagle);
                                                break;
                                            }
                                        case GolfShotRanking.Birdie:
                                            {
                                                holeBehaviour.PlayVfxOneShot(HoleVfx.Birdie);
                                                break;
                                            }
                                        case GolfShotRanking.Par:
                                            {
                                                holeBehaviour.PlayVfxOneShot(HoleVfx.Par);
                                                break;
                                            }
                                        case GolfShotRanking.Bogey:
                                            {
                                                holeBehaviour.PlayVfxOneShot(HoleVfx.Bogey);
                                                break;
                                            }
                                        default:
                                            {
                                                holeBehaviour.PlayVfxOneShot(HoleVfx.Eagle);
                                                break;
                                            }
                                    }

                                    AudioManager.Instance.PlayHoleFinisherSfx(shotRanking);

                                    break;
                                }
                        }

                        break;
                    }
                case HoleFinisherEffectType.One:
                    {
                        switch (index)
                        {
                            case 0:
                                {
                                    break;
                                }
                            case 1:
                                {
                                    AudioManager.Instance.PlayHoleFinisherSfx(GolfShotRanking.HoleInOne);

                                    break;
                                }
                            case 2:
                                {
                                    holeBehaviour.PlayVfxOneShot(HoleVfx.Ace1);

                                    break;
                                }
                            case 3:
                                {
                                    holeBehaviour.PlayVfxOneShot(HoleVfx.Ace2);
                                    holeBehaviour.PlayVfxOneShot(HoleVfx.Ace4);
                                    break;
                                }
                            case 4:
                                {
                                    holeBehaviour.PlayVfxOneShot(HoleVfx.Ace3);

                                    break;
                                }
                            case 5:
                                {
                                    ActionDispatcher.Dispatch(new CameraOrbitHolePhase2StartToken());

                                    break;
                                }
                        }

                        break;
                    }
            }
        }

        public void SetUpCameraTimestamps()
        {
            switch (effectType)
            {
                case HoleFinisherEffectType.Zero:
                    {
                        cameraTimestamps = new List<float>
                        {
                            0.00f,
                            4.00f,
                        };
                        SetUpCameraTimeLineCheckPoints(cameraTimestamps.Count);
                        break;
                    }
                case HoleFinisherEffectType.One:
                    {
                        cameraTimestamps = new List<float>
                        {
                            0.00f,
                            3.70f,
                            7.00f,
                        };
                        SetUpCameraTimeLineCheckPoints(cameraTimestamps.Count);
                        break;
                    }
            }
        }

        public void CameraPhaseStartAction(int index)
        {
            switch (effectType)
            {
                case HoleFinisherEffectType.Zero:
                    {
                        switch (index)
                        {
                            case 0:
                                {
                                    cinemachineBrain.m_DefaultBlend = new CinemachineBlendDefinition(CinemachineBlendDefinition.Style.EaseInOut, 1.25f);

                                    cameraInCharge1.gameObject.SetActive(true); currentCamera = cameraInCharge1;
                                    cameraInCharge2.gameObject.SetActive(false);

                                    currentCamera.m_Lens.FieldOfView = 75;

                                    Vector3 currentCameraPos = mainCamera.gameObject.transform.position;
                                    currentCameraPos.y = holePosition.transform.position.y;

                                    Vector3 directionFromHoleToCurrentPosition = currentCameraPos - holePosition.transform.position;

                                    Vector3 finalPosition = holePosition.transform.position
                                        + directionFromHoleToCurrentPosition.normalized * 1.876158f
                                        + Vector3.up * 0.421917f;

                                    currentCamera.transform.position = finalPosition;
                                    currentCamera.transform.LookAt(holePosition.transform.position + Vector3.up * 1.095f);

                                    controller.AdjustPostExposure(GlobalSO.PlayFieldSO.GetOriginalPPColorAdjustmentValue()
                                        - CameraStateController.ColorAdjustmentOffetToDark, 0.5f);

                                    break;
                                }
                            case 1:
                                {
                                    Restore();

                                    controller.AdjustPostExposure(GlobalSO.PlayFieldSO.GetOriginalPPColorAdjustmentValue(), 0.1f);

                                    ActionDispatcher.Dispatch(new CameraOrbitHoleFinishedToken(ball, trajectory));

                                    break;
                                }
                        }

                        break;
                    }
                case HoleFinisherEffectType.One:
                    {
                        switch (index)
                        {
                            case 0:
                                {
                                    cinemachineBrain.m_DefaultBlend = new CinemachineBlendDefinition(CinemachineBlendDefinition.Style.EaseInOut, 1.25f);

                                    cameraInCharge1.gameObject.SetActive(true); currentCamera = cameraInCharge1;
                                    cameraInCharge2.gameObject.SetActive(false);

                                    currentCamera.m_Lens.FieldOfView = 75;

                                    Vector3 currentCameraPos = mainCamera.gameObject.transform.position;
                                    currentCameraPos.y = holePosition.transform.position.y;

                                    Vector3 directionFromHoleToCurrentPosition = currentCameraPos - holePosition.transform.position;

                                    Vector3 finalPosition = holePosition.transform.position
                                        + directionFromHoleToCurrentPosition.normalized * 1.876158f
                                        + Vector3.up * 0.421917f;

                                    currentCamera.transform.position = finalPosition;
                                    currentCamera.transform.LookAt(holePosition.transform.position + Vector3.up * 1.095f);

                                    controller.AdjustPostExposure(GlobalSO.PlayFieldSO.GetOriginalPPColorAdjustmentValue()
                                        - CameraStateController.ColorAdjustmentOffetToDark, 0.5f);

                                    break;
                                }
                            case 1:
                                {
                                    cinemachineBrain.m_DefaultBlend = new CinemachineBlendDefinition(CinemachineBlendDefinition.Style.HardOut, 0.50f);

                                    cameraInCharge2.gameObject.SetActive(true);
                                    cameraInCharge1.gameObject.SetActive(false); currentCamera = cameraInCharge2;

                                    Vector3 currentCameraPos = mainCamera.gameObject.transform.position;
                                    Vector3 directionFromHoleToCamera = currentCameraPos - holePosition.transform.position;

                                    currentCamera.transform.position = holePosition.transform.position + (directionFromHoleToCamera * 2.5f);
                                    

                                    currentCamera.transform.LookAt(holePosition.transform.position + Vector3.up * 13.75f);

                                    break;
                                }
                            case 2:
                                {
                                    Restore();

                                    controller.AdjustPostExposure(GlobalSO.PlayFieldSO.GetOriginalPPColorAdjustmentValue(), 0.1f);

                                    ActionDispatcher.Dispatch(new CameraOrbitHoleFinishedToken(ball, trajectory));

                                    break;
                                }
                        }

                        break;
                    }
            }
        }
        #endregion

        #region Utility
        public void Restore()
        {
            cameraInCharge1.gameObject.SetActive(false);
            cameraInCharge2.gameObject.SetActive(false);

            cinemachineBrain.m_DefaultBlend = new CinemachineBlendDefinition(CinemachineBlendDefinition.Style.EaseInOut, 2f);
        }
        #endregion

        #region Data Injection
        public void SetHolePosition(GameObject _holePosition)
        {
            holePosition = _holePosition;
            holeBehaviour = holePosition.GetComponent<HoleBehaviour>();
        }

        public void SetLineRenderer(LineRenderer _lineRenderer, CameraBirdEyeViewDrawTrajectoryLine trajectoryLine)
        {
            lineRenderer = _lineRenderer;
        }

        public void SetDecalProjector(DecalProjector _decalProjector, DecalProjector PinCenter, DecalProjector PinRadius)
        {

        }

        public void SetBall(List<Ball> balls)
        {
            ballList = balls;
        }
        #endregion
    }
}

