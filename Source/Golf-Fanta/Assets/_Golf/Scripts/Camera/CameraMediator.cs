using _Golf.Physics.Data;
using _Golf.Scripts.Common;
using _Golf.Scripts.Core;
using Cinemachine;
using TinyMessenger;
using UnityEngine;
using UnityEngine.Rendering.Universal;
using System.Collections.Generic;
using _Golf.Scripts.Lobby;
using _Golf.Scripts.ScriptableObjects;
using GolfFantaModule.Models.Economy;

namespace GolfGame
{
    /// <summary>
    /// Mediator for controlling cameras in the scene
    /// </summary>
    public class CameraMediator : ModuleMediator
    {
        private CameraStateController cameraStateController;

        #region cameras
        private Camera mainCamera;
        private CinemachineVirtualCamera freeLookCamera;
        private CinemachineVirtualCamera ballRearLookCam;
        private CinemachineVirtualCamera ballStartFlyLookCamera;
        private CinemachineVirtualCamera ballFlyPassCameraWaitingLookCamera;
        private CinemachineVirtualCamera ballFlyAfterFirstBounceFollowCamera;
        private CinemachineVirtualCamera ballFlyAfterFirstRollFollowCamera;
        private Camera DragBallCamera;
        #endregion

        #region scene objects
        private List<Ball> ballList;
        private int LocalPlayerIndex => ballList.IndexOf(ballList.Find(ball => ball.IsLocalBall()));
        private Ball LocalBall => ballList[LocalPlayerIndex];

        private GameObject hole;
        private LineRenderer lineRenderer; 
        private CameraBirdEyeViewDrawTrajectoryLine trajectoryLine;
        private DecalProjector decalProjector;
        private DecalProjector pinCenter;
        private DecalProjector pinRadius;
        #endregion

        #region subscription
        private TinyMessageSubscriptionToken _previewCourseToken;
        private TinyMessageSubscriptionToken _previewCourseSkipToken;
        private TinyMessageSubscriptionToken _prepShotToken;
        private TinyMessageSubscriptionToken _gameStartToken;
        private TinyMessageSubscriptionToken _changeCameraMode;
        private TinyMessageSubscriptionToken _continueShotAction;
        private TinyMessageSubscriptionToken _exitGameplayToken;
        private TinyMessageSubscriptionToken _cameraOrbitHoleToken;

        private TinyMessageSubscriptionToken _onBallChangedToken;
        private TinyMessageSubscriptionToken _onClubChangedToken;
        private TinyMessageSubscriptionToken _onGearChangedToken;

        private TinyMessageSubscriptionToken _onSpectateButtonClick;
        private TinyMessageSubscriptionToken _onReceiveOpponentCommand;
        #endregion

        private CameraMediatorManager cameraMediatorManager;

        private PlayerGadget playerGadget;

        #region mediator implementation
        public void InitReference()
        {
            if (mainCamera != null
                && freeLookCamera != null
                && ballRearLookCam != null
                && ballFlyAfterFirstBounceFollowCamera != null
                && ballList != null
                && hole != null
                && lineRenderer != null
                && decalProjector != null
                && pinCenter != null
                && pinRadius != null
                && playerGadget != null)
            {
                cameraMediatorManager = new CameraMediatorManager();
                cameraMediatorManager.InitData(LocalBall, hole, lineRenderer, trajectoryLine, decalProjector, pinCenter, pinRadius, playerGadget, null);
                cameraStateController = mainCamera.gameObject.GetComponent<CameraStateController>();
                cameraStateController.Init(this);

                cameraStateController.SetPlayerGadget(playerGadget);

                ballFlyAfterFirstBounceFollowCamera.m_Follow = LocalBall.transform;
                ballFlyAfterFirstBounceFollowCamera.m_LookAt = LocalBall.transform;

                cameraStateController.Init(
                    _mainCam: mainCamera,
                    // virtual cameras
                    freeLookCamera, 
                    ballRearLookCam, 
                    ballStartFlyLookCamera,
                    ballFlyPassCameraWaitingLookCamera,
                    ballFlyAfterFirstBounceFollowCamera,
                    ballFlyAfterFirstRollFollowCamera,
                    //
                    _DragBallCamera: DragBallCamera,
                    balls: ballList,
                    _hole: hole,
                    _lineRenderer: lineRenderer,
                    _trajectoryLine: trajectoryLine,
                    _decalProjector: decalProjector,
                    _pinCenter: pinCenter,
                    _pinRadius: pinRadius
                );
            }
            else
            {
                Debug.LogError("This mediator needs more injection data");
            }
        }

        public void InitListener()
        {
            MasterManager.Instance.BallStartMoving += BallStartMovingListener;
            MasterManager.Instance.BallMoveHitPointInWhichCameraStopWaiting += StopWaitingAfterBallMove;

            MasterManager.Instance.BallMoveHitFirstRollPosition += FollowRollAfterFirstBounce;
            MasterManager.Instance.BallGoingPutting += BallGoingPutting;

            MasterManager.Instance.ForceCameraBackToRearBallView += OnForceCameraBackToRearBallView;

            _previewCourseToken = ActionDispatcher.Bind<PreviewCourseToken>(OnPreviewCourse);
            _previewCourseSkipToken = ActionDispatcher.Bind<PreviewCourseSkipToken>(OnPreviewCourseSkipped);
            _prepShotToken = ActionDispatcher.Bind<PrepShotAction>(OnPrepShot);
            _gameStartToken = ActionDispatcher.Bind<ReadyToPlayAction>(OnGameStart);
            _changeCameraMode = ActionDispatcher.Bind<ChangeCameraModeAction>(OnCameraChangeMode);
            _continueShotAction = ActionDispatcher.Bind<MoveCameraForNextShotAction>(OnPrepContinueShot);
            _exitGameplayToken = ActionDispatcher.Bind<ExitGameplayAction>(OnExitGamePlay);
            _cameraOrbitHoleToken = ActionDispatcher.Bind<CameraOrbitHoleToken>(OnCameraOrbitHole);

            _onBallChangedToken = ActionDispatcher.Bind<ChangeBallAction>(OnBallChanged);
            _onClubChangedToken = ActionDispatcher.Bind<ChangeClubAction>(OnClubChanged);
            _onGearChangedToken = ActionDispatcher.Bind<ChangeGearAction>(OnGearChanged);

            _onSpectateButtonClick = ActionDispatcher.Bind<OnChangeBallSpectateToken>(OnSpectateClick);
            _onReceiveOpponentCommand = ActionDispatcher.Bind<SyncOpponentAction>(OnReceiveOpponentCommand);
        }

        public void DisposeReference()
        {

        }

        public void DisposeListener()
        {
            MasterManager.Instance.BallStartMoving -= BallStartMovingListener;
            MasterManager.Instance.BallMoveHitPointInWhichCameraStopWaiting -= StopWaitingAfterBallMove;

            MasterManager.Instance.BallMoveHitFirstRollPosition -= FollowRollAfterFirstBounce;
            MasterManager.Instance.BallGoingPutting -= BallGoingPutting;

            MasterManager.Instance.ForceCameraBackToRearBallView -= OnForceCameraBackToRearBallView;

            ActionDispatcher.Unbind(_previewCourseToken);
            ActionDispatcher.Unbind(_previewCourseSkipToken);
            ActionDispatcher.Unbind(_prepShotToken);
            ActionDispatcher.Unbind(_gameStartToken);
            ActionDispatcher.Unbind(_changeCameraMode);
            ActionDispatcher.Unbind(_continueShotAction);
            ActionDispatcher.Unbind(_exitGameplayToken);
            ActionDispatcher.Unbind(_cameraOrbitHoleToken);

            ActionDispatcher.Unbind(_onBallChangedToken);
            ActionDispatcher.Unbind(_onClubChangedToken);
            ActionDispatcher.Unbind(_onGearChangedToken);

            ActionDispatcher.Unbind(_onSpectateButtonClick);
            ActionDispatcher.Unbind(_onReceiveOpponentCommand);
            cameraStateController.Dispose();
        }

        public ModuleManager GetManager()
        {
            return cameraMediatorManager;
        }

        #endregion

        #region data injection
        public void SetCamera(
            Camera _MainCamera, 
            CinemachineVirtualCamera _FreeLookCamera, 
            CinemachineVirtualCamera _BallRearLookCamera, 
            CinemachineVirtualCamera _BallStartFlyLookCamera,
            CinemachineVirtualCamera _BallFlyPassCameraWaitingLookCamera,
            CinemachineVirtualCamera _BallFlyAfterFirstBounceFollowCamera,
            CinemachineVirtualCamera _BallFlyAfterFirstRollFollowCamera,
            Camera _DragBallCamera)
        {
            mainCamera = _MainCamera;
            freeLookCamera = _FreeLookCamera;
            ballRearLookCam = _BallRearLookCamera;
            ballStartFlyLookCamera = _BallStartFlyLookCamera;
            ballFlyPassCameraWaitingLookCamera = _BallFlyPassCameraWaitingLookCamera;
            ballFlyAfterFirstBounceFollowCamera = _BallFlyAfterFirstBounceFollowCamera;
            ballFlyAfterFirstRollFollowCamera = _BallFlyAfterFirstRollFollowCamera;
            DragBallCamera = _DragBallCamera;
        }

        public void SetBall(List<Ball> balls)
        {
            ballList = balls;
        }

        public void SetHolePosition(GameObject _hole)
        {
            hole = _hole;
        }

        public void SetLineRenderer(LineRenderer _lineRenderer, CameraBirdEyeViewDrawTrajectoryLine trajectoryLineRenderer)
        {
            lineRenderer = _lineRenderer;
            trajectoryLine = trajectoryLineRenderer;

            ActionDispatcher.Dispatch(new LineRendererDepenencyInjectAction(_lineRenderer));
        }

        public void SetPin(DecalProjector _decalProjector, DecalProjector PinCenter, DecalProjector PinRadius)
        {
            decalProjector = _decalProjector;
            pinCenter = PinCenter;
            pinRadius = PinRadius;
        }

        public void SetPlayerGadget(PlayerGadget _playerGadget)
        {
            playerGadget = _playerGadget;    
        }
        #endregion

        #region subscription methods
        private void OnCameraChangeMode(ChangeCameraModeAction action)
        {
            int index = action.id;
            if (index == 1)
            {
                cameraStateController.ReceiveEvent(CameraControllerEvent.SET_PIN, null);
            }
            else if (index == 0)
            {
                cameraStateController.ReceiveEvent(CameraControllerEvent.SET_INPUT, null);
            }
        }

        private void BallStartMovingListener(BallMediatorManagerData ballTrajectory)
        {
            cameraStateController.ChangeState(CameraStateEnum.BALL_START_FLY_LOOKAT, new object[1] { ballTrajectory });
        }

        private void StopWaitingAfterBallMove(BallMediatorManagerData ballTrajectory)
        {
            AIRBORNE_TRANSITION transition = ballTrajectory.airborneTransition;

            if (transition == AIRBORNE_TRANSITION.ALPHA)
            {
                cameraStateController.ChangeState(CameraStateEnum.BALL_FOLLOW_AFTER_WAITING, new object[1] { ballTrajectory });
            }
            else if (transition == AIRBORNE_TRANSITION.BETA)
            {
                cameraStateController.ChangeState(CameraStateEnum.BALL_LOOK_AT_AFTER_WAITING, new object[1] { ballTrajectory });
            }
            else if (transition == AIRBORNE_TRANSITION.GAMMA)
            {
                cameraStateController.ChangeState(CameraStateEnum.BALL_FOLLOW_LOOK_AT_AFTER_WAITING, new object[1] { ballTrajectory });
            }
            else if (transition == AIRBORNE_TRANSITION.DELTA)
            {
                cameraStateController.ChangeState(CameraStateEnum.BALL_FOLLOW_LOOK_AT_AFTER_WAITING_WIDE, new object[1] { ballTrajectory });
            }
        }

        private void FollowRollAfterFirstBounce(BallMediatorManagerData ballTrajectory)
        {
            cameraStateController.ChangeState(CameraStateEnum.BALL_FOLLOW_AFTER_FIRST_ROLL, new object[1] { ballTrajectory });
        }

        private void BallGoingPutting(BallTrajectory ballTrajectory, AIRBORNE_TRANSITION puttTransition, bool isBallPastHoleAndInnerRadius)
        {
            if (puttTransition == AIRBORNE_TRANSITION.FOLLOW_PUTT)
            {
                cameraStateController.ChangeState(CameraStateEnum.FOLLOW_BALL_PUTTING, new object[2] { ballTrajectory, isBallPastHoleAndInnerRadius });
            }
            else if (puttTransition == AIRBORNE_TRANSITION.LOOKAT_PUTT)
            {
                cameraStateController.ChangeState(CameraStateEnum.LOOKAT_BALL_PUTTING, new object[2] { ballTrajectory, isBallPastHoleAndInnerRadius });
            }
            else if (puttTransition == AIRBORNE_TRANSITION.LOOKDOWN_PUTT)
            {
                cameraStateController.ChangeState(CameraStateEnum.LOOKDOWN_BALL_PUTTING, new object[2] { ballTrajectory, isBallPastHoleAndInnerRadius });
            }
        }

        private void OnCameraOrbitHole(CameraOrbitHoleToken token)
        {
            int par = GlobalSO.PlayFieldSO.HoleInfo.Par;
            int stroke = token.ball.localPlayer.GetStrokeCount();

            if (GlobalSO.GameplayBus.currentLogic.logicData is MultiplayerNormalLogicSO or TournamentLogicSO)
            {
                GolfShotRanking point = GolfUtility.GetPoint(par, stroke);
                Finisher(point, token);
            }
            else 
            {
                // sandbox
                Finisher(GolfShotRanking.Eagle, token);
            }
        }

        private void Finisher(GolfShotRanking point, CameraOrbitHoleToken token)
        {
            cameraStateController.ChangeState(CameraStateEnum.HOLE_FINISHER_TEMPLATE, new object[2] { token.trajectory, token.ball });
        }

        public void OnPrepContinueShot(MoveCameraForNextShotAction ctx)
        {
            if (ctx.ball.localPlayer.GetId() != ballList[MasterManager.Instance.CurrentBallInCamera].localPlayer.GetId())
            {
                return;
            }

            Ball sentBall = ctx.ball;

            if (sentBall.IsLocalBall())
            {
                if (sentBall.RestingSurfaceType == GolfPhysics.UnitySurface.SurfaceType.Green)
                {
                    cameraStateController.ChangeState(CameraStateEnum.BALL_REAR, new object[1] { true });
                }
                else
                {
                    cameraStateController.ChangeState(CameraStateEnum.BALL_BIRD_EYE, null);
                }
            }
            else
            {
                if (sentBall.RestingSurfaceType == GolfPhysics.UnitySurface.SurfaceType.Green)
                {
                    cameraStateController.ChangeState(CameraStateEnum.BALL_REAR, new object[1] { true });
                }
                else
                {
                    cameraStateController.ChangeState(CameraStateEnum.BALL_BIRD_EYE, null);
                }
            }
        }
        
        private void OnPreviewCourse(PreviewCourseToken token)
        {
            cameraStateController.Preview();
        }

        private void OnPreviewCourseSkipped(PreviewCourseSkipToken token)
        {
            cameraStateController.SkipPreview();
        }

        public void ResetAim()
        {
            cameraMediatorManager.ResetAim();
            Vector3 endPoint = cameraMediatorManager.FindInitialAimPoint();
            var trajectoryInput = cameraMediatorManager.HashSpeedAndSideAngle(false, endPoint);
            cameraMediatorManager.Draw(out _, trajectoryInput);
        }

        private void OnPrepShot(PrepShotAction action)
        {
            if (action.ball.IsLocalBall())
            {
                cameraMediatorManager.FindInitialAimPoint();
            }
        }

        private void OnClubChanged(ChangeClubAction action)
        {
            if (action.isChangedMainClub)
            {
                OnGadgetChanged();
            }
        }

        private void OnBallChanged(ChangeBallAction action)
        {
            OnGadgetChanged();
        }

        private void OnGearChanged(ChangeGearAction action)
        {
            // check if current gear affects final distance
            GearData currentGearData = GlobalSO.PlayerGadget.ChosenChip == null ? null : GlobalSO.PlayerGadget.ChosenChip.serverData.customData;
            if (currentGearData == null)
            {
                OnGadgetChanged();
            }
            else
            {
                if (currentGearData.IsPrepShotCalculationGear())
                {
                    OnGadgetChanged();
                }
            }
        }

        private void OnGadgetChanged()
        {
            cameraMediatorManager.FindInitialAimPoint();

            if (cameraStateController.GetState() is CameraBirdEyeView)
            {
                // camera is in bird eye view, changing gadget entails the calculation of trajectory
                CameraBirdEyeView cameraBirdEyeView = (CameraBirdEyeView)cameraStateController.GetState();
                cameraBirdEyeView.ChangeGadgetRedrawTrajectory();
            }
        }

        private void OnGameStart(ReadyToPlayAction token)
        {
            cameraStateController.StartGameSetUp();
        }

        private void OnExitGamePlay(ExitGameplayAction _)
        {
            cameraStateController.ShutOff();
        }

        private void OnSpectateClick(OnChangeBallSpectateToken token)
        {

        }

        private void OnReceiveOpponentCommand(SyncOpponentAction action)
        {
            if (MasterManager.Instance.CurrentBallInCamera == LocalPlayerIndex)
            {
                return;
            }

            PlayerCommand opponentCommand = action.playerCommand;

            if (opponentCommand is DragAimCommand)
            {
                DragAimCommand dragAimCommand = (DragAimCommand) opponentCommand;
                cameraStateController.MoveOpponentAimPoint(new Vector3(
                    dragAimCommand.aimPointPositionX, dragAimCommand.aimPointPositionY, dragAimCommand.aimPointPositionZ
                ));
            }
            else if (opponentCommand is GoToRearViewCommand)
            {
                cameraStateController.ChangeState(CameraStateEnum.BALL_REAR, null);

                decalProjector.gameObject.SetActive(false);
                pinCenter.gameObject.SetActive(false);
            }
            else if (opponentCommand is GoToTopViewCommand)
            {
                cameraStateController.ChangeState(CameraStateEnum.BALL_BIRD_EYE, null);

                decalProjector.gameObject.SetActive(false);
                pinCenter.gameObject.SetActive(false);
            }
        }

        private void OnForceCameraBackToRearBallView()
        {
            cameraStateController.ChangeState(CameraStateEnum.BALL_REAR, null);
        }
        #endregion
    }
}
