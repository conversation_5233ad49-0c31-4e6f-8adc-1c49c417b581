using _Golf.Scripts.Common;
using System.Collections.Generic;
using _Golf.Scripts.Core;
using _Golf.Scripts.ScriptableObjects;
using UnityEngine;

namespace GolfGame
{
    public static class GolfShotRatingHelper
    {
        public static float FetchPerfectDegreeConfig()
        {
            float result = 0f;

            string rankId = GlobalSO.GameplayBus.localPlayer.GetPlayerRankInfo().rankId;

            List<GolfShotRatingByRank> ratingConfig = GlobalSO.RemoteConfigData.GolfShotRating.GolfShotRatingByRanks;

            foreach (var golfShotRating in ratingConfig)
            {
                if (golfShotRating.RankId == rankId)
                {
                    result = golfShotRating.PerfectShotSideAngleDelta;
                }
            }

            // check debug
            if (GlobalSO.LocalGameSetting.IsDebuggingRankShotData)
            {
                return GlobalSO.LocalGameSetting.PerfectShotSideAngleDelta;
            }

            if (result == 0)
            {
                Debug.LogError("Perfect degree config cannot be 0.");
                return 2f; // default
            }
            else
            {
                return result;
            }
        }


        public static float FetchGreatDegreeConfig()
        {
            float result = 0f;

            string rankId = GlobalSO.GameplayBus.localPlayer.GetPlayerRankInfo().rankId;

            List<GolfShotRatingByRank> ratingConfig = GlobalSO.RemoteConfigData.GolfShotRating.GolfShotRatingByRanks;

            foreach (var golfShotRating in ratingConfig)
            {
                if (golfShotRating.RankId == rankId)
                {
                    result = golfShotRating.GreatShotSideAngleDelta;
                }
            }

            // check debug
            if (GlobalSO.LocalGameSetting.IsDebuggingRankShotData)
            {
                return GlobalSO.LocalGameSetting.GreatShotSideAngleDelta;
            }

            if (result == 0)
            {
                Debug.LogError("Great degree config cannot be 0.");
                return 10f; // default
            }
            else
            {
                return result;
            }
        }

        public static ShotRating RateGolfShot(float physicsSideAngle, float perfectSideAngle, float greatSideAngle)
        {
            if (Mathf.Abs(physicsSideAngle) <= perfectSideAngle)
            {
                // PERFECT SHOT
                ActionDispatcher.Dispatch(new OnGolfShotRated(ShotRating.Perfect));
                return ShotRating.Perfect;
            }

            if (Mathf.Abs(physicsSideAngle) <= greatSideAngle)
            {
                // GREAT SHOT
                ActionDispatcher.Dispatch(new OnGolfShotRated(ShotRating.Great));
                return ShotRating.Great;
            }

            return ShotRating.None;
        }

        public static float GetSideAngleMultiplierFactor(string rankId, List<GolfShotRatingByRank> GolfShotRatingByRanks)
        {
            foreach (var golfShotRating in GolfShotRatingByRanks)
            {
                if (golfShotRating.RankId == rankId)
                {
                    return golfShotRating.SideAngleMultiplyingFactor;
                }
            }

            return 0f;
        }
    }

    public class OnGolfShotRated : ActionBase
    {
        public ShotRating result;

        public OnGolfShotRated(ShotRating result)
        {
            this.result = result;
        }
    }
}
