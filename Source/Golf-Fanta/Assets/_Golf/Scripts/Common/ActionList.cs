using System;
using _Golf.Scripts.Core;
using _Golf.Scripts.Lobby;
using System.Collections.Generic;
using _Golf.Physics;
using _Golf.Physics.Data;
using _Golf.Scripts.Core.CourseController;
using _Golf.Scripts.Networking.Photon.Lobby;
using _Golf.Scripts.PlayField;
using _Golf.Scripts.ScriptableObjects.Item;
using Unity.Services.Lobbies.Models;
using Unity.Services.Lobbies;
using UnityEngine;
using GolfGame;
using _Golf.Scripts.UI;
using UnityEngine.UIElements;
using UnityEngine.Rendering.Universal;
using _Golf.Scripts.CirclePoint;
using _Golf.Scripts.ScriptableObjects;
using GolfFantaModule.Models;

namespace _Golf.Scripts.Common
{
    #region Local Lobby Data Change Dispatch Events (Syntax: LocalLobby_____Action)
    public class LocalLobbyAddPlayerAction : ActionBase
    {
        public int Index;
        public LocalPlayer Player;
        public LocalLobbyAddPlayerAction(int index, LocalPlayer player)
        {
            Index = index;
            Player = player;
        }
    }

    public class LocalLobbyStateChangedAction : ActionBase
    {
        public LobbyState LobbyState { get; }
        public LocalLobbyStateChangedAction(LobbyState lobbyState)
        {
            LobbyState = lobbyState;
        }
    }

    public class LocalLobbyPlayerStatusChangedToInGameAction : ActionBase 
    {
        public int InGameCount { get; }
        public LocalLobbyPlayerStatusChangedToInGameAction(int inGameCount)
        {
            InGameCount = inGameCount;
        }
    }

    public class LocalLobbyPlayerStatusChangedToHitBallAtion : ActionBase
    {
        public LocalPlayer LocalPlayer { get; }
        public PlayerStatus PlayerStatus { get; }
        public LocalLobbyPlayerStatusChangedToHitBallAtion(LocalPlayer localPlayer, PlayerStatus playerStatus)
        {
            LocalPlayer = localPlayer;
            PlayerStatus = playerStatus;
        }
    }

    public class LocalLobbyPlayerStatusChangedToReadyForShotAtion : ActionBase
    {
        public LocalPlayer LocalPlayer { get; }
        public PlayerStatus PlayerStatus { get; }
        public LocalLobbyPlayerStatusChangedToReadyForShotAtion(LocalPlayer localPlayer, PlayerStatus playerStatus)
        {
            LocalPlayer = localPlayer;
            PlayerStatus = playerStatus;
        }
    }

    public class LocalLobbyPlayerStrokeCountChangedAction : ActionBase 
    {
        public LocalPlayer LocalPlayer { get; }
        public int StrokeCount { get; }

        public LocalLobbyPlayerStrokeCountChangedAction(LocalPlayer localPlayer, int strokeCount)
        {
            LocalPlayer = localPlayer;
            StrokeCount = strokeCount;
        }
    }

    public class LocalLobbyEndGame : ActionBase
    {
        public LocalLobbyEndGame()
        {

        }
    }

    public class LocalLobbyOpponentLeft : ActionBase
    {
        public LocalPlayer OpponentPlayer { get; }
        public LocalLobbyOpponentLeft(LocalPlayer opponentPlayer)
        {
            OpponentPlayer = opponentPlayer;
        }
    }
    
    public class LocalPlayerReadyForRematch : ActionBase
    {
        public LocalPlayerReadyForRematch()
        {

        }
    }
    
    public class LocalLobbyRematchAction : ActionBase
    {
        public LocalLobbyRematchAction()
        {

        }
    }
    
    public class RequireRematchAction : ActionBase
    {
        public LocalPlayer OpponentPlayer { get; }
        public RequireRematchAction(LocalPlayer opponentPlayer)
        {
            OpponentPlayer = opponentPlayer;
        }
    }
    
    
    public class LocalLobbyReadyForNewMatch : ActionBase
    {
        public List<LocalPlayer> Players { get; }
        public LocalLobbyReadyForNewMatch(List<LocalPlayer> players)
        {
            Players = players;
        }
    }
    
    public class LocalLobbyReConfigForRematch : ActionBase
    {
        public LocalLobbyReConfigForRematch()
        {
        }
    }

    public class LocalLobbyForfeitAction : ActionBase
    {
        public LocalPlayer Player;
        public LocalLobbyForfeitAction(LocalPlayer whoseForfeit)
        {
            Player = whoseForfeit;
        }
    }
    
    #endregion

    #region Lobby and Match making
    public class QueueStartButtonClickAction : ActionBase
    {
        public LobbyProperty Lobby;
        public QueueStartButtonClickAction(LobbyProperty lobby)
        {
            Lobby = lobby;
        }
    }

    public class MatchMakingCancelAction : ActionBase
    {
        public MatchMakingCancelAction()
        {

        }
    }

    public class FoundOpponentAction : ActionBase
    {
        public LocalPlayer Opponent { get; private set; }
        public LobbyProperty LobbyProperty { get; private set; }
        public FoundOpponentAction(LocalPlayer opponent, LobbyProperty lobbyProperty)
        {
            Opponent = opponent;
            LobbyProperty = lobbyProperty;
        }
    }

    public class MatchMakingFinishAction : ActionBase
    {
        public MatchMakingFinishAction()
        {
            
        }
    }

    public class PlayerJoinedAction : ActionBase
    {
        public List<LobbyPlayerJoined> Players { get; }
        public PlayerJoinedAction(List<LobbyPlayerJoined> players)
        {
            Players = players;
        }
    }

    public class PlayerLeftAction : ActionBase
    {
        public List<int> Players { get; }
        public PlayerLeftAction(List<int> players)
        {
            Players = players;
        }
    }

    public class LobbyChangedAction : ActionBase
    {
        public ILobbyChanges Changes { get; }
        public LobbyChangedAction(ILobbyChanges changes)
        {
            Changes = changes;
        }
    }

    public class PlayerStatusChangedToLobbyAction : ActionBase
    {
        public PlayerStatusChangedToLobbyAction()
        {
            
        }
    }
    #endregion

    public class BallBounceAction : ActionBase
    {
        public Ball ball;
        public BallTrajectory ballTrajectory;
        public int ballIndex;

        public BallBounceAction()
        {

        }
    }

    public class BallLandedAction : ActionBase
    {
        public Ball ball;
        public BallTrajectory ballTrajectory;
        public int ballIndex;

        public BallLandedAction()
        {

        }
    }

    public class DragStateChangeAction : ActionBase
    {
        public DragState dragState;

        public DragStateChangeAction()
        {
            
        }
    }

    public class GameOverAction : ActionBase { }
    
    public class ShowEndGamePanelAction : ActionBase { }

    public class ResetGameAction : ActionBase 
    { 
        public ResetGameAction()
        {

        }
    }

    public class LocalSendGolfShotDataAction : ActionBase
    {
        public GolfShotData golfShotData;
        public LocalSendGolfShotDataAction(GolfShotData shotData)
        {
            golfShotData = shotData;
        }
    }

    public class HitBallAction : ActionBase
    {
        public GolfShotData Input { get; }
        public BallTrajectory BallTrajectory { get; }

        public HitBallAction(GolfShotData input, BallTrajectory ballTrajectory)
        {
            Input = input;
            BallTrajectory = ballTrajectory;
        }
    }
    public class OpponentHitBallPrepAction : ActionBase
    {
        public LocalPlayer Player { get; }
        public GolfShotData Input { get; }
        public Ball Ball { get; }
        public bool IsReplay { get; }
        public OpponentHitBallPrepAction(LocalPlayer player, GolfShotData input, Ball ball, bool isReplay)
        {
            Player = player;
            Input = input;
            Ball = ball;
            IsReplay = isReplay;
        }
    }

    public class OpponentHitBallAction : ActionBase
    {
        public LocalPlayer Player { get; }
        public GolfShotData Input { get; }
        public Ball Ball { get; }
        public bool IsReplay { get; }
        public OpponentHitBallAction(LocalPlayer player, GolfShotData input, Ball ball, bool isReplay)
        {
            Player = player;
            Input = input;
            Ball = ball;
            IsReplay = isReplay;
        }
    }

    public class LocalPlayerHitBall : ActionBase
    {
        public GolfShotData Input { get; }

        public LocalPlayerHitBall(GolfShotData input)
        {
            Input = input;
        }
    }

    public class ChangeBallAction : ActionBase
    {
        public BallSO ball;
        public ChangeBallAction(BallSO _ball)
        {
            ball = _ball;
        }
    }

    public class ChangeClubAction : ActionBase
    {
        public bool isChangedMainClub;
        public ClubSO playerMainClub;
        public ClubSO playerSecondaryClub;
        public ClubType clubType;

        public ChangeClubAction(bool isMainClub, ClubSO mainClub, ClubSO secondClub, ClubType type)
        {
            isChangedMainClub = isMainClub;
            playerMainClub = mainClub;
            playerSecondaryClub = secondClub;
            clubType = type;
        }
    }

    public class ChangeGearAction : ActionBase
    {
        public ChipSO gear;
        public ChangeGearAction(ChipSO _gear)
        {
            gear = _gear;
        }
    }

    public class ChangeTrailAction : ActionBase
    {
        public BallTrailSO trail;
        public ChangeTrailAction(BallTrailSO _trail)
        {
            trail = _trail;
        }
    }

    public class ChangeCameraModeAction : ActionBase
    {
        public int id;
        public ChangeCameraModeAction(int _id)
        {
            id = _id;
        }
    }

    public class BallFinishedAction : ActionBase
    {
        public Ball Ball;
        public BallFinishedAction(Ball ball)
        {
            Ball = ball;
        }
    }

    public class PinChangeToken : ActionBase
    {
        public bool isManual;
        public float velocity; public float overPoweredVelocity;
        public Vector3 position;
        public float distanceToAim;
        public float finalDistance;
        public float launchAngle;
        public float hashedSideAngle;

        public PinChangeToken(bool manual, float ballVelocity, float overDraggedVelocity,
            Vector3 pinPosition, float aimDistance, float finalDistance, float launchAngle, float hashedSideAngle)
        {
            velocity = ballVelocity; 
            overPoweredVelocity = overDraggedVelocity;
            position = pinPosition;
            distanceToAim = aimDistance;
            this.finalDistance = finalDistance;
            this.launchAngle = launchAngle;
            this.hashedSideAngle = hashedSideAngle;
        }
    }
    
    public class SideSpinChanged : ActionBase
    {
        public float SideSpin;
        
        public SideSpinChanged UpdateSpin(float changedSpin)
        {
            SideSpin = changedSpin;
            return this;
        }
    }
    
    public class BackSpinChanged : ActionBase
    {
        public float BackSpin;

        public BackSpinChanged UpdateSpin(float changedSpin)
        {
            BackSpin = changedSpin;
            return this;
        }
    }

    #region Scene Objects Spawn
    public class BallPlaceHolderSpawnAction : ActionBase
    {
        public BallPlaceholder ballPlaceholder;

        public BallPlaceHolderSpawnAction(BallPlaceholder ballPlaceholder)
        {
            this.ballPlaceholder = ballPlaceholder;
        }
    }

    public class LineRendererSpawnAction : ActionBase
    {
        public LineRenderer aimPointLineRenderer;
        public LineRenderer ballPathLineRenderer;
        public LineRenderer ballPredictionLineRenderer;

        public LineRendererSpawnAction(LineRenderer aimPointLineRenderer, LineRenderer ballPathLineRenderer, LineRenderer ballPredictionLineRenderer)
        {
            this.aimPointLineRenderer = aimPointLineRenderer;
            this.ballPathLineRenderer = ballPathLineRenderer;
            this.ballPredictionLineRenderer = ballPredictionLineRenderer;
        }
    }

    public class LineRendererDepenencyInjectAction : ActionBase
    {
        public LineRenderer LineRenderer;

        public LineRendererDepenencyInjectAction(LineRenderer aimPointLineRenderer)
        {
            this.LineRenderer = aimPointLineRenderer;
        }
    }

    public class PinSpawnAction : ActionBase
    {
        public DecalProjector pin;
        public DecalProjector pinCenter;
        public DecalProjector pinRadius;

        public PinSpawnAction(DecalProjector pin, DecalProjector pinCenter, DecalProjector pinRadius)
        {
            this.pin = pin;
            this.pinCenter = pinCenter;
            this.pinRadius = pinRadius;
        }
    }

    public class CirclesSpawnAction : ActionBase
    {
        public List<CirclePointBehaviour> circles;

        public CirclesSpawnAction(List<CirclePointBehaviour> circles) 
        {
            this.circles = circles;
        }
    }

    public class BallSpawnAction : ActionBase
    {
        public Ball ball;

        public BallSpawnAction(Ball ball)
        {
            this.ball = ball;
        }
    }

    public class HoleSpawnAction : ActionBase
    {
        public HoleBehaviour holeBehaviour;

        public HoleSpawnAction(HoleBehaviour holeBehaviour)
        {
            this.holeBehaviour = holeBehaviour;
        }
    }

    public class FlagSpawnAction : ActionBase
    {
        public FlagBehaviour flagBehaviour;

        public FlagSpawnAction(FlagBehaviour flagBehaviour) 
        {
            this.flagBehaviour = flagBehaviour;
        }
    }
    
    public class FlagIconEnableAction : ActionBase
    {
        public bool IsEnable;

        public FlagIconEnableAction(bool value)
        {
            this.IsEnable = value;
        }
    }
    
    #endregion

    public class SetWindAction: ActionBase
    {
        public WeatherData WeatherData;
        
        public SetWindAction(WeatherData weatherData)
        {
            this.WeatherData = weatherData;
        }
    }
    
    public class ReadyToPlayAction : ActionBase 
    {
        public ReadyToPlayAction()
        {

        }
    }

    public class PrepShotAction : ActionBase 
    {
        public Ball ball;

        public PrepShotAction(Ball ball)
        {
            this.ball = ball;
        }
    }

    public class ReadyToHitAction : ActionBase 
    {
        
    }

    public class PreviewCourseInitToken : ActionBase
    {
        public string CourseName;
        public string Par;

        public PreviewCourseInitToken(string courseName, string par)
        {
            this.CourseName = courseName;
            this.Par = par;
        }
    }
    public class PreviewCourseToken : ActionBase { }

    public class PreviewCourseSkipToken : ActionBase { }

    public class PreviewCourseFinishedToken : ActionBase { }

    public class CameraOrbitHoleToken : ActionBase
    {
        public Ball ball;
        public BallTrajectory trajectory;

        public CameraOrbitHoleToken(Ball ball, BallTrajectory trajectory)
        {
            this.ball = ball;
            this.trajectory = trajectory;
        }
    }

    #region Closest To Pin
    public class ClosestToPinConfigAction : ActionBase
    {
        public bool IsLucky;
        public int Mode { get; }
        public string RankType { get; }
        public ClosesToPinRankReward RewardModel { get; }
        public ClosestToPinConfigAction(int mode, string rankType, ClosesToPinRankReward rewardModel, bool isLucky)
        {
            this.Mode = mode;
            this.RankType = rankType;
            this.RewardModel = rewardModel;
            this.IsLucky = isLucky;
        }
    }

    public class ClosestToPinCircleConfigAction : ActionBase
    {
        public List<float> Distances;

        public ClosestToPinCircleConfigAction(List<float> distances)
        {
            Distances = distances;
        }
    }

    public class ClosestToPinEnableAction : ActionBase
    {
        public bool IsEnable { get; }

        public ClosestToPinEnableAction(bool isEnable)
        {
            this.IsEnable = isEnable;
        } 
    }

    public class ClosestToPinEffectAction : ActionBase
    {
        
    }

    public class ClosestToPinDistanceNotifyAction : ActionBase
    {
        public float Distance { get; }

        public ClosestToPinDistanceNotifyAction(float distance)
        {
            this.Distance = distance;
        } 
    }
    
    #endregion

    #region TournamentAction
    public class TournamentEnableAction : ActionBase
    {
        public bool IsEnable { get; }

        public TournamentEnableAction(bool isEnable)
        {
            this.IsEnable = isEnable;
        } 
    }
    
    #endregion

    public class CameraRearViewTransistionFinishAction : ActionBase
    {
        public CameraRearViewTransistionFinishAction()
        {

        }
    }

    public class CompleteResetShotAction : ActionBase
    {
        public CompleteResetShotAction()
        {

        }
    }

    public class MoveCameraForNextShotAction : ActionBase
    {
        public Ball ball;
        public BallTrajectory ballTrajectory;

        public MoveCameraForNextShotAction(Ball ball, BallTrajectory trajectory)
        {
            this.ball = ball;
            ballTrajectory = trajectory;
        }
    }

    public class ContinueShotAction : ActionBase
    {
        public ContinueShotAction()
        {

        }
    }

    public class ResetBallLastPositionAction : ActionBase
    {
        public string PlayerId;
        public bool IsTieBreak;

        public ResetBallLastPositionAction(string playerId, bool isTieBreak)
        {
            PlayerId = playerId;
            IsTieBreak = isTieBreak;
        }
    }

    public class OnFinishGameAction : ActionBase
    {
        public OnFinishGameAction()
        {

        }
    }

    public class RequestAddressableLoad : ActionBase
    {
        
    }

    public class HeadToHeadTieBreakAction : ActionBase
    {
        public HeadToHeadTieBreakAction()
        {

        }
    }

    public class StrokeTimerTimeUp : ActionBase
    {
        public StrokeTimerTimeUp()
        {

        }
    }

    public class CameraOrbitHolePhase2StartToken : ActionBase
    {
        public CameraOrbitHolePhase2StartToken()
        {

        }
    }

    public class CameraOrbitHoleFinishedToken : ActionBase
    {
        public Ball ball;
        public BallTrajectory trajectory;

        public CameraOrbitHoleFinishedToken(Ball ball, BallTrajectory trajectory)
        {
            this.ball = ball;
            this.trajectory = trajectory;
        }
    }

    public class OnChangeBallSpectateToken : ActionBase
    {
        public bool isManual;
        public OnChangeBallSpectateToken(bool manual) 
        {
            isManual = manual;
        }
    }

    public class ChangeScreenAction : ActionBase
    {
        public EScreenEnum screenName;

        public ChangeScreenAction(EScreenEnum screen)
        {
            screenName = screen;
        }
    }

    public class OnOpenBagSinglePrize : ActionBase
    {
        
    }
    
    public class OnOpenBagReceiveItem : ActionBase
    {
        public UICard CardData;
        public OnOpenBagReceiveItem(UICard cardData)
        {
            CardData = cardData;
        }
    }

    public class OnOpenBagDone : ActionBase
    {
        public OnOpenBagDone()
        {

        }
    }

    #region Player Sync
    public class SyncPlayerAction : ActionBase
    {
        public PlayerCommand playerCommand;

        public SyncPlayerAction(PlayerCommand command)
        {
            playerCommand = command;
        }
    }

    public class SyncOpponentAction : ActionBase
    {
        public PlayerCommand playerCommand;

        public SyncOpponentAction(PlayerCommand command)
        {
            playerCommand = command;
        }
    }
    #endregion

    #region Tournament

    public class ClickPlayTournament : ActionBase
    {
        public Tournament Tournament;
        public Action<float,string> OnLoadingProgress;
        public bool ForceRestart;

        public ClickPlayTournament(Tournament tournament , Action<float,string> onLoadingProgress, bool forceRestart = false)
        {
            Tournament = tournament;
            OnLoadingProgress = onLoadingProgress;
            ForceRestart = forceRestart;
        }

        public ClickPlayTournament()
        {
            Tournament = null;
            OnLoadingProgress = null;
        }

        public void ClearReference()
        {
            Tournament = null;
            OnLoadingProgress = null;
        }
    }
    
    public class FinishHoleAction : ActionBase
    {
        public int StrokesCount = 0;
        public float TieBreakPoint;
        public float LongestShotDistance;
        public List<GolfShotDataModel> GolfShotDatas;

        public FinishHoleAction(int strokesCount, float tieBreakPoint, float longestShotDistance, List<GolfShotDataModel> golfShotDatas)
        {
            StrokesCount = strokesCount;
            TieBreakPoint = tieBreakPoint;
            LongestShotDistance = longestShotDistance;
            GolfShotDatas = golfShotDatas;
        }
    }
    
    public class AssetsCheckAndDownload : ActionBase
    {
        public Action<float> Progress;
        public AssetsCheckAndDownload(Action<float> progress)
        {
            Progress = progress;
        }
    }

    public class ButtonClickAction : ActionBase
    {
        public ClickEvent ClickEvent { get; }
        public ButtonClickAction(ClickEvent clickEvent)
        {
            ClickEvent = clickEvent;
        }
    }

    public class TournamentAnouncementAction : ActionBase
    {
        public string Message;
        public TournamentAnouncementAction(string message)
        {
            Message = message;
        }
    }

    public class ShowTieBreakPointAction : ActionBase
    {
        public float TieBreakPoint;
        public ShowTieBreakPointAction(float tieBreakPoint)
        {
            TieBreakPoint = tieBreakPoint;
        }
    }

    public class UpdatePlayerInfoAction : ActionBase
    {
        public PlayerInfo PlayerInfo;

        public UpdatePlayerInfoAction(PlayerInfo playerInfo)
        {
            PlayerInfo = playerInfo;
        }
    }
    
    public class SetPlayerAvaiblityAction : ActionBase
    {
        public UserAvailability Availability;

        public SetPlayerAvaiblityAction(UserAvailability availability)
        {
            Availability = availability;
        }
    }

    #endregion

    public class CinemachineFinishTransitionAction : ActionBase
    {
        public BallStateEnum ballState;

        public CinemachineFinishTransitionAction(BallStateEnum state)
        {
            ballState = state;
        }
    }

    public class DisconnectRelayServerAction : ActionBase
    {
        public DisconnectRelayServerAction()
        {

        }
    }

    public class RelayInactiveDisconnectAction : ActionBase
    {
        public LocalPlayer playerWhoDisconnected;

        public RelayInactiveDisconnectAction(LocalPlayer _playerWhoDisconnected)
        {
            playerWhoDisconnected = _playerWhoDisconnected;
        }
    }

    public class LobbyHostSendRelayJoinCode : ActionBase
    {
        public string relayJoinCode;

        public LobbyHostSendRelayJoinCode(string joinCode)
        {
            relayJoinCode = joinCode;
        }
    }

    #region ---Game Operations--- 
    public class ResetApplicationAction : ActionBase
    {
        public ResetApplicationAction()
        {

        }
    }
    #endregion
}
