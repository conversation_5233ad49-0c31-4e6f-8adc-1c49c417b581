using System;
using System.Collections.Generic;
using _Golf.Scripts.Common;
using _Golf.Scripts.Lobby;
using _Golf.Scripts.Networking;
using _Golf.Scripts.ScriptableObjects;
using UnityEngine;
using Random = UnityEngine.Random;

namespace _Golf.Scripts.Core
{
	[Flags]
	public enum PlayerStatus
	{
		None = 0,
		Connecting = 1,
		Lobby = 2,
		Ready = 4,
		InGame = 8,
		HitBall = 5,
		FinishGame = 11,
		Forfeit = 12,
		Menu = 16,
		ReadyForShot = 32,
		ReadyForRematch = 33
	}

	[Serializable]
	public class InputParam
	{
		public double BallPositionX;
		public double BallPositionY;
		public double BallPositionZ;
		public double Velocity;
		public double LaunchAngle;
		public double SideAngle;
		public double BackSpin;
		public double SideSpin;
		public double ForwardDirectionX;
		public double ForwardDirectionY;
		public double ForwardDirectionZ;
		public string CurrentBall;
		public string CurrentGear;
		public string CurrentClub;
	}
	
    public static class LocalPlayerData
    {
		public const string k_DisplayName = "DisplayName";
        public const string k_StrokeCount = "StrokeCount";
		public const string k_UserStatus = "UserStatus";
		public const string k_RankInfo = "RankInfo";
		public const string k_BallPos = "BallPos";
		public const string k_InputParam = "InputParam";
		public const string k_Commands = "Commands";
        public const string k_RelayJoinCodeId = "RelayJoinCodeId";
        public const string k_AllocationId = "AllocationId";
        public const string k_AvatarInfo = "AvatarInfo";
    }

    [Serializable]
	public class LocalPlayer : IBot
	{
        #region Events dispatched to local lobby
        public Action<LocalPlayer, PlayerStatus> onPlayerStatusChanged;
		public Action<LocalPlayer, int> onStrokeCountChanged;
		public Action<LocalPlayer, string> onAllocationIDChanged;
		public Action<LocalPlayer, string> onRelayJoinCodeIDChanged;
        #endregion

        #region Host
        /// <summary>
        /// Is this player the host of the lobby
        /// </summary>
        private CallbackValue<bool> isHost = new CallbackValue<bool>(false);

        public void SetIsHost(bool isHost)
        {
            this.isHost.Value = isHost;
        }

        public bool GetIsHost()
        {
            return isHost.Value;
        }
        #endregion

        #region Display Name
		/// <summary>
		/// Name to be displayed
		/// </summary>
        private CallbackValue<string> displayName = new CallbackValue<string>("");

        public void SetDisplayName(string name)
        {
            displayName.Value = name;
        }

        public string GetDisplayName()
        {
            return displayName.Value;
        }
        #endregion

        #region Player Status
		/// <summary>
		/// Status of player, used to control gameplay flow
		/// </summary>
        private CallbackValue<PlayerStatus> userStatus = new CallbackValue<PlayerStatus>((PlayerStatus)0);

        public void SetUserStatus(PlayerStatus status)
        {
			if (status == PlayerStatus.Lobby)
			{
				// lobby status is changed before events are subscribed, so dispatch it manually
				ActionDispatcher.Dispatch(new PlayerStatusChangedToLobbyAction());  
            }
            userStatus.Value = status;
        }

        public PlayerStatus GetUserStatus()
        {
            return userStatus.Value;
        }
        #endregion

        #region Id
        /// <summary>
        /// ID of player
        /// </summary>
        private CallbackValue<string> id = new CallbackValue<string>("");

        public void SetId(string playerId)
        {
			id.Value = playerId;
        }

        public string GetId()
        {
            return id.Value;
        }
        #endregion

        #region Player Index
		/// <summary>
		/// Index of player in lobby
		/// </summary>
        private CallbackValue<int> index = new CallbackValue<int>(0);

        public void SetPlayerIndex(int playerIndex)
        {
            index.Value = playerIndex;
        }

        public int GetPlayerIndex()
        {
            return index.Value;
        }
        #endregion

        #region Player Rank Info
		/// <summary>
		/// Rank info of player
		/// </summary>
        private CallbackValue<Rank> rankInfo = new CallbackValue<Rank>(null);

		public void SetPlayerRankInfo(PlayerDataRank rank)
		{
			rankInfo.Value = new Rank(rank);
		}

		public PlayerDataRank GetPlayerRankInfo()
		{
			return new PlayerDataRank(rankInfo.Value);
		}
		public void SetPlayerRankInfo(Rank rank)
        {
			rankInfo.Value = rank;
        }

		#endregion

		#region Avatar Id

		private CallbackValue<PlayerAvatarData> avatarInfo = new CallbackValue<PlayerAvatarData>(null);

		public void SetPlayerAvatarInfo(PlayerAvatarData info)
		{
			avatarInfo.Value = info;
			userAvt = GlobalSO.InventoryItemSo.avatars.Find(x => x.id == info.Id).image;
		}

		public PlayerAvatarData GetPlayerAvatarInfo()
		{
			return avatarInfo.Value;
		}
		
		#endregion

        #region Stroke Count
        /// <summary>
        /// Number of shots this player has taken
        /// </summary>
        private CallbackValue<int> strokeCount = new CallbackValue<int>(0);

		public void SetStrokeCount(int stroke)
		{
			strokeCount.Value = stroke;
		}

		public int GetStrokeCount()
		{
			return strokeCount.Value;
		}
        #endregion

        #region Ball Position
		/// <summary>
		/// Ball position of this player
		/// </summary>
        private CallbackValue<Vector3> ballPos = new CallbackValue<Vector3>(Vector3.zero);

		public void SetBallPos(Vector3 pos)
		{
			ballPos.Value = pos;
		}

		public Vector3 GetBallPos()
		{
			return ballPos.Value;
		}
        #endregion

        #region Input Param
		/// <summary>
		/// Input Param of Player, used for ball flight physics
		/// </summary>
        private CallbackValue<InputParam> inputParam = new CallbackValue<InputParam>(new InputParam());

		public void SetInputParam(InputParam param)
		{
			inputParam.Value = param;
		}

		public InputParam GetInputParam()
		{
			return inputParam.Value;
		}
		#endregion

		#region Sync Action
		/// <summary>
		/// this list contains all player commands of all shots (history)
		/// </summary>
		private CallbackValue<List<List<PlayerCommand>>> playerCommands = new CallbackValue<List<List<PlayerCommand>>>(new List<List<PlayerCommand>>());

		public void AddCommand(int currentStroke, PlayerCommand command)
		{
			if (currentStroke < 0 || currentStroke >= playerCommands.Value.Count)
			{
				int difference = currentStroke - playerCommands.Value.Count + 1;

				for (int i = 0; i < difference; i++)
				{
					playerCommands.Value.Add(new List<PlayerCommand>());
				}
			}

			playerCommands.Value[currentStroke].Add(command);
		}

        public void ClearCommand()
        {
            playerCommands.Value.Clear();
            playerCommands.Value = new List<List<PlayerCommand>>();
        }

        public List<List<PlayerCommand>> GetPlayerCommands()
		{
			return playerCommands.Value;
		}

		/// <summary>
		/// this list contains all player commands waiting to be sent to lobby
		/// once synced into lobby service, this list resets
		/// </summary>
		private CallbackValue<List<List<PlayerCommand>>> lobbyPlayerCommands = new CallbackValue<List<List<PlayerCommand>>>(new List<List<PlayerCommand>>());

        public void AddLobbyCommand(int currentStroke, PlayerCommand command)
        {
            if (currentStroke < 0 || currentStroke >= lobbyPlayerCommands.Value.Count)
            {
                int difference = currentStroke - lobbyPlayerCommands.Value.Count + 1;

                for (int i = 0; i < difference; i++)
                {
                    lobbyPlayerCommands.Value.Add(new List<PlayerCommand>());
                }
            }

            lobbyPlayerCommands.Value[currentStroke].Add(command);
        }

		public void ClearLobbyCommand()
		{
			lobbyPlayerCommands.Value.Clear();
			lobbyPlayerCommands.Value = new List<List<PlayerCommand>>();
        }

        public void ReceiveLobbyPlayerCommands(List<List<PlayerCommand>> commands)
        {
            for (int i = 0; i < commands.Count; i++)
			{
				for (int j = 0; j < commands[i].Count; j++)
				{
					AddCommand(i, commands[i][j]);
				}
			}
        }

        public List<List<PlayerCommand>> GetPlayerLobbyCommands()
        {
            return lobbyPlayerCommands.Value;
        }
        #endregion

        #region BOT

        public bool IsBot { get; private set; }
        public BotDefine BotLevelDefine { get; private set; }
		
        public float RandomDragAccuracy()
        {
	        var par = GlobalSO.PlayFieldSO.HoleInfo.Par;
	        const float minError = 0.1f;
	        var error = strokeCount.Value - par < BotLevelDefine.ApplyErrorUntilScore 
		        ? Random.Range(minError, BotLevelDefine.DragError)
		        : 0;
	        return 1 - error;
        }

		public float GetBotSideAngleRatio(float sideAngle)
		{
			return sideAngle / BotLevelDefine.NeedleDegreeError;
		}

        public float RandomNeedleDegree()
        {
	        var par = GlobalSO.PlayFieldSO.HoleInfo.Par;
	        var accuracy = strokeCount.Value - par < BotLevelDefine.ApplyErrorUntilScore ?  Random.Range(-BotLevelDefine.NeedleDegreeError, BotLevelDefine.NeedleDegreeError) : 0f;
	        return accuracy;
        }

        #endregion

        #region Allocation Id
        /// <summary>
        /// Allocation Id of lobby, used to link with relay
        /// </summary>
        private CallbackValue<string> allocationID = new CallbackValue<string>();

        public void SetAllocationID(string id)
        {
            allocationID.Value = id;
        }

        public string GetAllocationID()
        {
            return allocationID.Value;
        }
        #endregion

        #region Relay Join Code
        /// <summary>
        /// Relay Join Code of lobby, used to join host on the relay server
        /// </summary>
        private CallbackValue<string> relayJoinCodeID = new CallbackValue<string>();

        public void SetRelayJoinCodeID(string id)
        {
            relayJoinCodeID.Value = id;
        }

        public string GetRelayJoinCodeID()
        {
            return relayJoinCodeID.Value;
        }
        #endregion

        public DateTime LastUpdated;

        #region Constructor
        public LocalPlayer(string id, int index, bool isHost, PlayerDataRank rankInfo,PlayerAvatarData avatar ,string displayName = default, PlayerStatus status = default) 
			: this()
		{
			this.id.Value = id;
			this.isHost.Value = isHost;
			this.index.Value = index;
			this.displayName.Value = displayName;
			userStatus.Value = status;
			SetPlayerAvatarInfo(avatar);
			this.rankInfo.Value = new Rank(rankInfo);
        }

		public LocalPlayer(string id, string displayName, Rank rank, PlayerAvatarData avatar) 
			: this()
		{
			this.id.Value = id;
			this.displayName.Value = displayName;
			rankInfo.Value = rank;
			SetPlayerAvatarInfo(avatar);
		}

		public LocalPlayer(LocalPlayer player) 
			: this()
		{
			id.Value = player.id.Value;
			displayName.Value = player.id.Value;
            rankInfo.Value = player.rankInfo.Value;
            SetPlayerAvatarInfo(player.avatarInfo.Value);
		}
        #endregion

        #region Default Constructor and Destructor
        LocalPlayer()
		{
            userStatus.onChanged += OnPlayerStatusChanged;
			strokeCount.onChanged += OnStrokeCountChanged;
            allocationID.onChanged += OnAllocationIDChanged;
            relayJoinCodeID.onChanged += OnRelayJoinCodeIDChanged;
        }

        ~LocalPlayer()
		{
			if (userStatus != null)
			{
				userStatus.onChanged -= OnPlayerStatusChanged;
			}
			if (strokeCount != null)
			{
				strokeCount.onChanged -= OnStrokeCountChanged;
            }
			if (allocationID != null)
			{
				allocationID.onChanged -= OnAllocationIDChanged;
			}
			if (relayJoinCodeID != null)
			{
				relayJoinCodeID.onChanged -= OnRelayJoinCodeIDChanged;
			}
        }
        #endregion

        #region Attribute change callback
        private void OnPlayerStatusChanged(PlayerStatus status)
        {
			onPlayerStatusChanged?.Invoke(this, status);
        }

        private void OnStrokeCountChanged(int strokeCount)
        {
			onStrokeCountChanged?.Invoke(this, strokeCount);
        }

		private void OnAllocationIDChanged(string allocationId)
		{
            onAllocationIDChanged?.Invoke(this, allocationId);
        }

		private void OnRelayJoinCodeIDChanged(string relayJoinCode)
		{
			onRelayJoinCodeIDChanged?.Invoke(this, relayJoinCode);
		}
        #endregion

        #region Stash Values

        private Sprite userAvt;

        public Sprite UserAvt => userAvt;

        #endregion

        public static LocalPlayer GenerateBot(string botId)
		{
			var bot =  new LocalPlayer($"Bot{botId}", $"Bot-{botId}", GlobalSO.GameplayBus.localPlayer.rankInfo.Value, new PlayerAvatarData());
			bot.IsBot = true;
			bot.BotLevelDefine = GlobalSO.BotLevel.SetLevelByRank(bot.rankInfo.Value.Id);
			bot.SetPlayerAvatarInfo(new PlayerAvatarData());
			return bot;
		}

		public void ResetState()
		{
			isHost.Value = false;
			userStatus.Value = PlayerStatus.Menu;
		}

		public void ResetPlayer()
		{
			userStatus.Value = PlayerStatus.Menu;
			strokeCount.Value = 0;
			ballPos.Value = Vector3.zero;
			inputParam.Value = GetInitialInputParam();
			ClearCommand();
			ClearLobbyCommand();
		}

		public InputParam GetInitialInputParam()
		{
			var inputParam = new InputParam();
			inputParam.BallPositionX = 0;
			inputParam.BallPositionY = 0;
			inputParam.BallPositionZ = 0;
			inputParam.Velocity = 0;
			inputParam.LaunchAngle = 0;
			inputParam.SideAngle = 0;
			inputParam.BackSpin = 0;
			inputParam.SideSpin = 0;
			inputParam.ForwardDirectionX = 0;
			inputParam.ForwardDirectionY = 0;
			inputParam.ForwardDirectionZ = 0;

			return inputParam;
		}
	}

	public interface IBot
	{
		public bool IsBot { get; }
		public BotDefine BotLevelDefine { get; }
		public float RandomDragAccuracy();
	}
}