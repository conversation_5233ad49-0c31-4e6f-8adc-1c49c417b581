using System.Collections.Generic;
using _Golf.Scripts.Common.Helper;
using _Golf.Scripts.Core;
using _Golf.Scripts.Lobby;
using GolfGame;
using Newtonsoft.Json;
using Unity.Services.Lobbies.Models;
using UnityEngine;
using Avatar = _Golf.Scripts.Core.Avatar;

namespace _Golf.Scripts.Networking.Photon.Lobby
{
    public static class LobbyConverter
    {
        public static Dictionary<string, string> LocalToRemoteLobbyData(LocalLobby lobby)
        {
            Dictionary<string, string> data = new Dictionary<string, string>
            {
                { LocalLobbyData.k_LobbyState, ((int)lobby.GetLocalLobbyState()).ToString() },
                { LocalLobbyData.k_LobbyWeather, lobby.GetLocalLobbyWeather().ToJson()},
                { LocalLobbyData.k_LobbyTee, lobby.GetTee().ToString()},
                { LocalLobbyData.k_LobbyCurrentHole, lobby.GetCurrentHole().ToString()}
            };

            return data;
        }

        public static Dictionary<string, string> LocalToRemoteUserData(LocalPlayer user)
        {
            Dictionary<string, string> data = new Dictionary<string, string>();

            if (user == null || string.IsNullOrEmpty(user.GetId()))
            {
                return data;
            }

            data.Add(LocalPlayerData.k_DisplayName, user.GetDisplayName());
            data.Add(LocalPlayerData.k_BallPos, user.GetBallPos().ToString());
            data.Add(LocalPlayerData.k_StrokeCount, user.GetStrokeCount().ToString());
            data.Add(LocalPlayerData.k_InputParam, JsonConvert.SerializeObject(user.GetInputParam()));
            data.Add(LocalPlayerData.k_RankInfo, JsonConvert.SerializeObject(user.GetPlayerRankInfo()));
            data.Add(LocalPlayerData.k_AvatarInfo, JsonConvert.SerializeObject(user.GetPlayerAvatarInfo()));
            data.Add(LocalPlayerData.k_UserStatus, ((int)user.GetUserStatus()).ToString());
            data.Add(LocalPlayerData.k_AllocationId, user.GetAllocationID());
            data.Add(LocalPlayerData.k_RelayJoinCodeId, user.GetRelayJoinCodeID());

            List<List<PlayerCommand>> localPlayerLobbyCommands = user.GetPlayerLobbyCommands();
            if (localPlayerLobbyCommands.Count > 0)
            {
                data.Add(LocalPlayerData.k_Commands, SerializePlayerCommand(user));
            }

            return data;
        }

        public static string SerializePlayerCommand(LocalPlayer user)
        {
            List<List<PlayerCommand>> localPlayerCommands = user.GetPlayerLobbyCommands();

            List<List<PlayerCommandJsonWrapper>> lobbyPlayerCommands = new List<List<PlayerCommandJsonWrapper>>();

            foreach (List<PlayerCommand> strokeActionList in localPlayerCommands)
            {
                List<PlayerCommandJsonWrapper> temp = new List<PlayerCommandJsonWrapper>();

                foreach (PlayerCommand playerCommand in strokeActionList)
                {
                    temp.Add(playerCommand.ToJsonWrapper());
                }

                lobbyPlayerCommands.Add(temp);
            }

            return JsonConvert.SerializeObject(lobbyPlayerCommands);
        }

        /// <summary>
        /// Create a new LocalLobby from the content of a retrieved lobby. Its data can be copied into an existing LocalLobby for use.
        /// </summary>
        public static void RemoteToLocal(Unity.Services.Lobbies.Models.Lobby remoteLobby, LocalLobby localLobby)
        {
            if (remoteLobby == null)
            {
                Debug.Log("Remote lobby is null, cannot convert.");
                return;
            }

            if (localLobby == null)
            {
                Debug.Log("Local Lobby is null, cannot convert");
                return;
            }

            foreach (var lobbyData in remoteLobby.Data)
            {
                //switch (lobbyData.Key)
                //{
                //    case UnityLobbyDataKey.k_Elo:
                //        {
                //            continue;
                //        }
                //}
            }

            localLobby.SetLobbyID(remoteLobby.Id);
            localLobby.SetLobbyCode(remoteLobby.LobbyCode);
            localLobby.SetLobbyName(remoteLobby.Name);
            localLobby.SetHostID(remoteLobby.HostId);
            
            
            localLobby.SetPrivateStatus(remoteLobby.IsPrivate);
            localLobby.SetAvailableSlots(remoteLobby.AvailableSlots);
            localLobby.SetMaxPlayerCount(remoteLobby.MaxPlayers);

            //Custom User Data Conversions
            List<string> remotePlayerIDs = new List<string>();
            int index = 0;
            foreach (var player in remoteLobby.Players)
            {
                var id = player.Id;
                remotePlayerIDs.Add(id);
                var isHost = remoteLobby.HostId.Equals(player.Id);
                var displayName = player.Data?.ContainsKey(LocalPlayerData.k_DisplayName) == true
                    ? player.Data[LocalPlayerData.k_DisplayName].Value
                    : default;

                var userStatus = player.Data?.ContainsKey(LocalPlayerData.k_UserStatus) == true
                    ? (PlayerStatus)int.Parse(player.Data[LocalPlayerData.k_UserStatus].Value)
                    : PlayerStatus.Lobby;

                var ballPos = player.Data?.ContainsKey(LocalPlayerData.k_BallPos) == true
                    ? player.Data[LocalPlayerData.k_BallPos].Value.StringToVector3() 
                    : Vector3.zero;
                
                var strokeCount = player.Data?.ContainsKey(LocalPlayerData.k_StrokeCount) == true
                    ? int.Parse(player.Data[LocalPlayerData.k_StrokeCount].Value)
                    : 0;
                var inputParam = player.Data?.ContainsKey(LocalPlayerData.k_InputParam) == true
                    ? JsonConvert.DeserializeObject<InputParam>(player.Data[LocalPlayerData.k_InputParam].Value) 
                    : null;
                
                var rankInfo = player.Data?.ContainsKey(LocalPlayerData.k_RankInfo) == true
                    ? JsonConvert.DeserializeObject<PlayerDataRank>(player.Data[LocalPlayerData.k_RankInfo].Value) 
                    : null; 
                
                var avatarInfo = player.Data?.ContainsKey(LocalPlayerData.k_AvatarInfo) == true
                    ? JsonConvert.DeserializeObject<PlayerAvatarData>(player.Data[LocalPlayerData.k_AvatarInfo].Value) 
                    : null; 
                
                LocalPlayer localPlayer = localLobby.GetPlayerByIndex(index);

                if (localPlayer == null)
                {
                    localPlayer = new LocalPlayer(id, index, isHost, rankInfo, avatarInfo ,displayName, userStatus);
                    localLobby.AddPlayer(index, localPlayer);
                }
                else
                {
                    localPlayer.SetId(id);
                    localPlayer.SetPlayerIndex(index);
                    localPlayer.SetIsHost(isHost);
                    localPlayer.SetDisplayName(displayName);
                    localPlayer.SetUserStatus(userStatus);
                    localPlayer.SetBallPos(ballPos);
                    localPlayer.SetStrokeCount(strokeCount);
                    localPlayer.SetInputParam(inputParam);
                    localPlayer.SetPlayerRankInfo(rankInfo);
                    localPlayer.SetPlayerAvatarInfo(avatarInfo);
                }

                index++;
            }
        }

        ///// <summary>
        ///// Create a list of new LocalLobbies from the result of a lobby list query.
        ///// </summary>
        //public static List<LocalLobby> QueryToLocalList(QueryResponse response)
        //{
        //    List<LocalLobby> retLst = new List<LocalLobby>();
        //    foreach (var lobby in response.Results)
        //        retLst.Add(RemoteToNewLocal(lobby));
        //    return retLst;
        //}

        //static LocalLobby RemoteToNewLocal(Unity.Services.Lobbies.Models.Lobby lobby)
        //{
        //    LocalLobby data = new LocalLobby();
        //    RemoteToLocal(lobby, data);
        //    return data;
        //}
    }
}