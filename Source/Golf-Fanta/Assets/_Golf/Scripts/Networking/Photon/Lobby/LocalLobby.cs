using System;
using System.Collections.Generic;
using System.Text;
using _Golf.Scripts.Common;
using _Golf.Scripts.Core;
using _Golf.Scripts.Lobby;
using _Golf.Scripts.PlayField;
using _Golf.Scripts.ScriptableObjects;
using _Golf.Scripts.ScriptableObjects.PlayField;
using GolfGame;
using Newtonsoft.Json;
using TinyMessenger;
using Unity.Services.Lobbies;
using Unity.Services.Lobbies.Models;
using UnityEngine;

namespace _Golf.Scripts.Networking.Photon.Lobby
{
    public enum LobbyState
    {
        Lobby = 1,
        ReadyToStart = 2,
        InGame = 3,
        ReadyForRematch = 4
    }

    public static class LocalLobbyData
    {
        public const string k_LobbyColor = "LocalLobbyColor";
        public const string k_LobbyState = "LocalLobbyState";
        public const string k_LobbyWeather = "LocalLobbyWeather";
        public const string k_LobbyTee = "LocalLobbyTee";
        public const string k_LobbyCurrentHole = "LocalLobbyCurrentHole";
    }

    public class LocalLobbyWeather
    {
        public float windSpeed;
        public float windDirection;

        public string ToJson()
        {
            return JsonConvert.SerializeObject(this);
        }
    }

    [System.Serializable]
    public class LocalLobby
    {
        public Action<int> onUserLeft;
        public Action<int> onUserReadyChange;

        #region ID
        /// <summary>
        /// ID of lobby, this value DOES NOT change
        /// </summary>
        private CallbackValue<string> lobbyID = new CallbackValue<string>();

        public void SetLobbyID(string id)
        {
            lobbyID.Value = id;
        }

        public string GetLobbyID()
        {
            return lobbyID.Value;
        }
        #endregion

        #region Code
        /// <summary>
        /// Code of lobby, used for private lobby, if at all
        /// </summary>
        private CallbackValue<string> lobbyCode = new CallbackValue<string>();

        public void SetLobbyCode(string code)
        {
            lobbyCode.Value = code;
        }

        public string GetLobbyCode()
        {
            return lobbyCode.Value;
        }
        #endregion

        #region Name
        /// <summary>
        /// Name of lobby, host can set name for lobby when creating one
        /// </summary>
        private CallbackValue<string> lobbyName = new CallbackValue<string>();

        public void SetLobbyName(string name)
        {
            lobbyName.Value = name;
        }

        public string GetLobbyName()
        {
            return lobbyName.Value;
        }
        #endregion

        #region Host ID
        /// <summary>
        /// ID of the host of this lobby, receive event change callback from lobby handler
        /// </summary>
        private CallbackValue<string> hostID = new CallbackValue<string>();

        public void SetHostID(string id)
        {
            hostID.Value = id;
        }

        public string GetHostID()
        {
            return hostID.Value;
        }
        #endregion

        #region Lobby State
        /// <summary>
        /// State of the lobby
        /// </summary>
        private CallbackValue<LobbyState> localLobbyState = new CallbackValue<LobbyState>();

        public void SetLocalLobbyState(LobbyState state)
        {
            localLobbyState.Value = state;
        }

        public LobbyState GetLocalLobbyState()
        {
            return localLobbyState.Value;
        }
        #endregion

        #region Lobby Weather
        /// <summary>
        /// Weather of the lobby, generated by the host
        /// </summary>
        private CallbackValue<LocalLobbyWeather> localLobbyWeather = new CallbackValue<LocalLobbyWeather>();

        public void SetLocalLobbyWeather(LocalLobbyWeather weather)
        {
            localLobbyWeather.Value = weather;
        }

        public LocalLobbyWeather GetLocalLobbyWeather()
        {
            return localLobbyWeather.Value;
        }

        public void GenerateGameWeather(LobbyProperty currentLobbyProperty)
        {
            float lowerWind =  currentLobbyProperty.TourInfo.GetWindSpeedMin();
            float upperWind = currentLobbyProperty.TourInfo.GetWindSpeedMax();
            var windDirection = GolfUtility.GenerateWindDirection();

            LocalLobbyWeather weather = new LocalLobbyWeather();
            weather.windSpeed = GolfUtility.GenerateWindSpeedLimit((int)lowerWind, (int)upperWind);
            weather.windDirection = windDirection;

            localLobbyWeather.Value = weather;
        }
        #endregion

        #region Current Hole

        private CallbackValue<int> localLobbyCurrentHole = new CallbackValue<int>();

        public void SetCurrentHole(int index)
        {
            localLobbyCurrentHole.Value = index;
        }

        public int GetCurrentHole()
        {
            return localLobbyCurrentHole.Value;
        }

        public void GenerateCurrentHole(int holeCount)
        {
            var random = new System.Random();
            localLobbyCurrentHole.Value = random.Next(0, holeCount);

            Debug.Log("[LOBBY HOST CONFIG] GenerateCurrentHole: " + holeCount);
        }

        #endregion

        #region Lobby Tee/Par
        /// <summary>
        /// From 1 to 5, indicating which tee to start with in the match
        /// </summary>
        private CallbackValue<int> localLobbyTee = new CallbackValue<int>();

        public void SetTee(int index)
        {
            localLobbyTee.Value = index;
        }

        public int GetTee()
        {
            return localLobbyTee.Value;
        }

        public void GenerateTee()
        {
            localLobbyTee.Value = (int)GlobalSO.PlayerInfoSO.Info.H2hRank.MajorRank+1;
        }
        #endregion

        #region Locked Status
        /// <summary>
        /// Is this lobby locked?
        /// </summary>
        private CallbackValue<bool> isLocked = new CallbackValue<bool>();

        public void SetLockedValue(bool isLocked)
        {
            this.isLocked.Value = isLocked;
        }

        public bool GetLockedValue()
        {
            return isLocked.Value;
        }
        #endregion

        #region Private status
        /// <summary>
        /// Is this lobby private?
        /// </summary>
        private CallbackValue<bool> isPrivate = new CallbackValue<bool>();

        public void SetPrivateStatus(bool isPrivate)
        {
            this.isPrivate.Value = isPrivate;
        }

        public bool GetPrivateStatus()
        {
            return this.isPrivate.Value;
        }
        #endregion

        #region Available Slots
        /// <summary>
        /// How many slots are available in this lobby.
        /// </summary>
        private CallbackValue<int> availableSlots = new CallbackValue<int>();

        public void SetAvailableSlots(int slots)
        {
            availableSlots.Value = slots;
        }

        public int GetAvailableSlots()
        {
            return availableSlots.Value;
        }
        #endregion

        #region Max Player Count
        /// <summary>
        /// Max player count of this lobby
        /// </summary>
        private CallbackValue<int> maxPlayerCount = new CallbackValue<int>();

        public void SetMaxPlayerCount(int maxPlayer)
        {
            maxPlayerCount.Value = maxPlayer;
        }

        public int GetMaxPlayerCount()
        {
            return maxPlayerCount.Value;
        }
        #endregion

        public int PlayerCount => m_LocalPlayers.Count;

        public List<LocalPlayer> LocalPlayers => m_LocalPlayers;
        List<LocalPlayer> m_LocalPlayers = new List<LocalPlayer>();

        public void ResetLobby()
        {
            m_LocalPlayers.Clear();

            lobbyName.Value = "";
            lobbyID.Value = "";
            lobbyCode.Value = "";
            isLocked.Value = false;
            isPrivate.Value = false;
            availableSlots.Value = 4;
            maxPlayerCount.Value = 4;
            onUserLeft = null;
        }

        /// <summary>
        /// this constructor is used for single player mode and sand box (fake lobby)
        /// </summary>
        public LocalLobby(string lobbyName, string lobbyID,
            string lobbyCode, string hostID, LobbyState lobbyState, bool locked,
            bool isPrivate, int availableSlots, int maxPlayerCount) : this()
        {
            this.lobbyName.Value = lobbyName;
            this.lobbyID.Value = lobbyID;
            this.lobbyCode.Value = lobbyCode;
            this.hostID.Value = hostID;
            localLobbyState.Value = lobbyState;
            this.isLocked.Value = locked;
            this.isPrivate.Value = isPrivate;
            this.availableSlots.Value = availableSlots;
            this.maxPlayerCount.Value = maxPlayerCount;
        }

        private void OnLocalLobbyStateChange(LobbyState state)
        {
            ActionDispatcher.Dispatch(new LocalLobbyStateChangedAction(state));
        }

        public LocalLobby()
        {
            hostID.onChanged += OnHostChanged;
            localLobbyState.onChanged += OnLocalLobbyStateChange;
        }

        ~LocalLobby()
        {
            hostID.onChanged -= OnHostChanged;
            localLobbyState.onChanged -= OnLocalLobbyStateChange;
        }

        public LocalPlayer GetPlayerByIndex(int index)
        {
            return PlayerCount > index ? m_LocalPlayers[index] : null;
        }
        
        private void OnHostChanged(string newHostId)
        {
            foreach (var player in m_LocalPlayers)
            {
                player.SetIsHost(player.GetId() == newHostId);
            }
        }
        
        public void AddPlayer(int index, LocalPlayer user)
        {
            m_LocalPlayers.Insert(index, user);

            user.onStrokeCountChanged += LocalPlayerStrokeCountChanged;
            user.onPlayerStatusChanged += LocalPlayerStatusChanged;
            user.onAllocationIDChanged += LocalPlayerAllocationIdChanged;
            user.onRelayJoinCodeIDChanged += LocalPlayerRelayJoinCodeChanged;

            ActionDispatcher.Dispatch(new LocalLobbyAddPlayerAction(index, user));

            Debug.Log($"Added User: {user.GetDisplayName()} - {user.GetId()} to slot {index + 1}/{PlayerCount}");
        }

        public void RemovePlayer(int playerIndex)
        {
            m_LocalPlayers[playerIndex].onStrokeCountChanged -= LocalPlayerStrokeCountChanged;
            m_LocalPlayers[playerIndex].onPlayerStatusChanged -= LocalPlayerStatusChanged;
            m_LocalPlayers[playerIndex].onAllocationIDChanged -= LocalPlayerAllocationIdChanged;
            m_LocalPlayers[playerIndex].onRelayJoinCodeIDChanged -= LocalPlayerRelayJoinCodeChanged;

            var localPlayer = m_LocalPlayers
                .Find((player) => player.GetId() == GlobalSO.GameplayBus.localPlayer.GetId());
            if (m_LocalPlayers[playerIndex].GetId() != localPlayer.GetId())
            {
                ActionDispatcher.Dispatch(new LocalLobbyOpponentLeft(m_LocalPlayers[playerIndex]));
            }
            
            m_LocalPlayers.RemoveAt(playerIndex);

            
            onUserLeft?.Invoke(playerIndex);
        }

        private void LocalPlayerStrokeCountChanged(LocalPlayer player, int stroke)
        {
            if (!player.GetIsHost())
            {
                ActionDispatcher.Dispatch(new LocalLobbyPlayerStrokeCountChangedAction(player, stroke));
            }
        }

        private void LocalPlayerStatusChanged(LocalPlayer playerWhoseStatusChanged, PlayerStatus statusChanged)
        {
            LocalPlayer whoseStatusChanged = playerWhoseStatusChanged;
            Debug.Log("User status changed: " + whoseStatusChanged.GetId() +  " - " + whoseStatusChanged.GetUserStatus());

            PlayerStatus playerStatus = statusChanged;

            switch (playerStatus) 
            {
                case PlayerStatus.InGame:
                    {
                        // check status of every player in the lobby
                        int inGameCount = 0;

                        foreach (var player in m_LocalPlayers)
                        {
                            if (player.GetUserStatus() == PlayerStatus.InGame)
                            {
                                inGameCount++;
                            }
                        }

                        ActionDispatcher.Dispatch(new LocalLobbyPlayerStatusChangedToInGameAction(inGameCount));

                        break;
                    }
                case PlayerStatus.HitBall:
                    {
                        // only check status of the opponent
                        if (whoseStatusChanged.GetId() != GlobalSO.GameplayBus.localPlayer.GetId())
                        {
                            Debug.Log("OPPONENT changed status to HIT BALL [Stroke " + whoseStatusChanged.GetStrokeCount() + " ]");
                            ActionDispatcher.Dispatch(new LocalLobbyPlayerStatusChangedToHitBallAtion(whoseStatusChanged, playerStatus));
                        }
                        break;
                    }
                case PlayerStatus.ReadyForShot:
                    {
                        // check status of every player in the lobby
                        ActionDispatcher.Dispatch(new LocalLobbyPlayerStatusChangedToReadyForShotAtion(whoseStatusChanged, playerStatus));

                        break;
                    }
                case PlayerStatus.FinishGame: 
                    {
                        // check status of every player in the lobby
                        if (m_LocalPlayers.TrueForAll(x => x.GetUserStatus() == PlayerStatus.FinishGame))
                        {
                            ActionDispatcher.Dispatch(new LocalLobbyEndGame());
                        }

                        break;
                    }
                case PlayerStatus.ReadyForRematch:
                    {
                        var localPlayer = m_LocalPlayers
                            .Find((player) => player.GetId() == GlobalSO.GameplayBus.localPlayer.GetId());
                        if (whoseStatusChanged.GetId() == localPlayer.GetId())
                        {
                            ActionDispatcher.Dispatch(new LocalPlayerReadyForRematch());
                        }
                        else
                        {
                            if(localPlayer.GetUserStatus() != PlayerStatus.ReadyForRematch)   
                                ActionDispatcher.Dispatch(new RequireRematchAction(whoseStatusChanged));
                        }
                        
                        if (m_LocalPlayers.TrueForAll(x => x.GetUserStatus() == PlayerStatus.ReadyForRematch))
                        {
                            ActionDispatcher.Dispatch(new LocalLobbyReConfigForRematch());
                        }

                        break;
                    }
                case PlayerStatus.Forfeit:
                    ActionDispatcher.Dispatch(new LocalLobbyForfeitAction(whoseStatusChanged));
                    break;
            }
        }

        private void LocalPlayerAllocationIdChanged(LocalPlayer player, string allocationId)
        {

        }

        private void LocalPlayerRelayJoinCodeChanged(LocalPlayer player, string relayJoinCode)
        {
            if (player.GetId() != GlobalSO.GameplayBus.localPlayer.GetId())
            {
                // player is the opponent
                if (player.GetIsHost())
                {
                    // opponent is the host and they updated their join code
                    // => join their relay server
                    if (relayJoinCode != null && relayJoinCode.Length > 3)
                    {
                        ActionDispatcher.Dispatch(new LobbyHostSendRelayJoinCode(relayJoinCode));
                    }
                }
            }
        }

        public override string ToString()
        {
            StringBuilder sb = new StringBuilder("Lobby : ");
            sb.AppendLine(lobbyName.Value);
            sb.Append("ID: ");
            sb.AppendLine(lobbyID.Value);
            sb.Append("Code: ");
            sb.AppendLine(lobbyCode.Value);
            sb.Append("Locked: ");
            sb.AppendLine(isLocked.Value.ToString());
            sb.Append("Private: ");
            sb.AppendLine(isPrivate.Value.ToString());
            sb.Append("AvailableSlots: ");
            sb.AppendLine(availableSlots.Value.ToString());
            sb.Append("Max Players: ");
            sb.AppendLine(maxPlayerCount.Value.ToString());
            sb.Append("LocalLobbyState: ");
            sb.AppendLine(localLobbyState.Value.ToString());
            sb.Append("Lobby LocalLobbyState Last Edit: ");
            return sb.ToString();
        }
    }
}