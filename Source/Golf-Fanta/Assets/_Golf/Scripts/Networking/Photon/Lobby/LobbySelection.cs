using System;
using UnityEngine;
using UnityEngine.Serialization;
using UnityEngine.UI;
namespace _Golf.Scripts.Lobby
{
    public class LobbySelection : MonoBehaviour
    {
        public static Action<LobbyProperty> OnSelectLobby;
        [FormerlySerializedAs("_lobbyPropertySO")]
        [SerializeField] private LobbyProperty lobbyProperty;
        [SerializeField] private Button _button;

        public void OnClick()
        {
            //OnSelectLobby?.Invoke(lobbyProperty);
        }
    }
}