using System;
using Photon.Realtime;

namespace BallPool.Multiplayer
{
    public enum NetworkState
    {
        Disconnected = 0,
        Connected,
        LostConnection,
        CreatedRoom,
        JoinedToRoom,
        JoinedLobby,
        ConnectedToMaster,
        OtherJoinedToRoom,
        LeftRoom,
        PlayerLeftRoom,
        PlayerEnteredRoom,
        RoomCreateFailed,
        JoinRoomFailed,
        OpponentReadToPlay
    }

    public delegate void NetworkHandler(NetworkState state);

    public enum RPCMessages
    {
        OnSendCueControl = 0,
        OnSendShotData,
        OnSendCueBallPosition,
        OpponentForceLeftRoom,
        OnSendMessage,
        OnWantToPlayAgain,
        OnDontWantToPlayAgain,
        OnComplitedShot
    }

    public interface IMultiplayerManager
    {
        bool WaitingForOpponent { get; set; }

        bool HasOpponentInRoom { get; }

        string RoomNameForConnect { get; set; }

        string CurrentRoomName { get; }

        bool IsMyTurn { get; set; }

        event NetworkHandler OnNetwork;

        event Action OnRoomStarted;

        event Action OnOpponentForceLeftRoom;

        event Action OnPlayAgain;

        event Action OnDontPlayAgain;


        event Action<Action<bool, string>> OnGetGameData;

        event Action<RPCMessages, object[]> OnReceivedRemoteMessage;

        void Connect(Action handler);

        void Disconnect();

        void JoinRoom(string roomName);

        void JoinRandomOrCreateRoom();
        void JoinRandomOrCreateRoom(int maxPlayer);

        void JoinLobby(TypedLobby lobbyType);
        void LeftRoom();

        void SendRemoteMessage(RPCMessages message, params object[] args);
    }
}