using System;
using System.Collections.Generic;
using System.Linq;
using _Golf.Scripts.Core;
using _Golf.Scripts.ScriptableObjects;
using Photon.Pun;
using Photon.Realtime;
using UnityEngine;
namespace _Golf.Scripts
{
    public class PhotonPartyController : MonoBehaviourPun
    {
        public static Action<Player> OnShotTaken;
        public static Action OnPlayerJoined;

        private GameplayBus _gameplayBus;
        private Dictionary<Player, int> shotsTaken = new Dictionary<Player, int>();

        private void Start()
        {
            _gameplayBus = GlobalSO.GameplayBus;
        }

        private void OnEnable()
        {
            OnShotTaken += PlayerHitBall;
            OnPlayerJoined += PlayerJoined;
        }

        private void OnDisable()
        {
            OnShotTaken -= PlayerHitBall;
            OnPlayerJoined -= PlayerJoined;
        }

        private void PlayerJoined()
        {
            //TODO: write new class to control play connect/disconnect
            _gameplayBus.currentInRoomPlayers = PhotonNetwork.PlayerList.ToDictionary(
                player => player.CustomProperties["ugsId"].ToString(), _ => false);
        }

        public void PlayerHitBall(Player player)
        {
            if (shotsTaken.ContainsKey(player))
            {
                shotsTaken[player]++;
            }

            Dictionary<int, int> dictionary = new Dictionary<int, int>(shotsTaken.ToDictionary(x => x.Key.ActorNumber, x => x.Value));

        }
    }
}