using System;
using System.Collections;
using System.Linq;
using System.Threading.Tasks;
using _Golf.Scripts.Common;
using _Golf.Scripts.Core;
using _Golf.Scripts.Lobby;
using _Golf.Scripts.Networking.Courses;
using _Golf.Scripts.Networking.Photon.Lobby;
using _Golf.Scripts.SceneLoader;
using _Golf.Scripts.ScriptableObjects;
using _Golf.Scripts.Tracking;
using _Golf.Scripts.UI;
using Cysharp.Threading.Tasks;
using GolfGame;
using GolfGame.API;
using TinyMessenger;
using Unity.Services.Authentication;
using UnityEngine;

namespace _Golf.Scripts.Networking.Photon
{
	public class MultiPlayerController : BaseControllerComponent
	{
		private FindingLobbyStateController findingLobbyStateController;

		/// <summary>
		/// Whether or not bot is enabled
		/// </summary>
		[SerializeField] private bool isBotEnabled;

		public bool GetBotEnabledStatus()
		{
			return isBotEnabled;
		}

        private Core.PlayerInfo playerInfo;

		/// <summary>
		/// The reference to the global instance of gameplay bus
		/// </summary>
        private GameplayBus gameplayBus;

		public GameplayBus GetBus()
		{ 
			return gameplayBus; 
		}

		/// <summary>
		/// The reference to the relay handler
		/// </summary>
		private RelayHandler relayHandler;

		public RelayHandler GetRelayHandler()
		{
			return relayHandler;
		}
		
		/// <summary>
		/// The reference to the lobby handler
		/// </summary>
		private LobbyHandler lobbyHandler;

		public LobbyHandler GetLobbyHandler()
		{
			return lobbyHandler;
		}

		/// <summary>
		/// The reference to the current local lobby
		/// </summary>
		private LocalLobby localLobby;

		public LocalLobby GetLocalLobby()
		{
			return localLobby;
		}

		/// <summary>
		/// The reference to the local player
		/// </summary>
		private LocalPlayer localPlayer;

		public LocalPlayer GetLocalPlayer()
		{
			return localPlayer;
		}

        /// <summary>
        /// Time (in seconds) to introduce a random delay for starting matchmaking, randomly chosen between the specified min and max values.
        /// This prevents users from starting a search and creating a lobby at the same time, reducing the chances of them mismatching.
        /// </summary>
        private (int min, int max) RandomDelayMatchRange => (0, 10);

		public int GetMinRandomDelayMatchRange() { return RandomDelayMatchRange.min; }

		public int GetMaxRandomDelayMatchRange() { return RandomDelayMatchRange.max; }

		/// <summary>
		/// Amount of waiting time after creating lobby
		/// </summary>
        private const int WaitingTimePostLobbyCreation = 10;
		
		/// <summary>
		/// Amount of waiting time bot rematch
		/// </summary>
		private const int WaitingTimeBotRematch= 7;
		public int GetWaitingTimePostLobbyCreation() {  return WaitingTimePostLobbyCreation; }

        #region Subscription tokens
        private TinyMessageSubscriptionToken queueStartButtonClickToken;
        private TinyMessageSubscriptionToken cancelMatchMakingButtonClickToken;
        private TinyMessageSubscriptionToken playerJoinedToken;
        private TinyMessageSubscriptionToken playerLeftToken;
        private TinyMessageSubscriptionToken lobbyChangedToken;
        private TinyMessageSubscriptionToken playerDataChangedToken;
        private TinyMessageSubscriptionToken localLobbyStateChangedToken;
        private TinyMessageSubscriptionToken localLobbyAddPlayerToken;
        private TinyMessageSubscriptionToken matchingToken;
        private TinyMessageSubscriptionToken lobbyReconfigToken;
        private TinyMessageSubscriptionToken lobbyRequireRematchToken;
        private TinyMessageSubscriptionToken localPlayerReadyFoRematchToken;
        private TinyMessageSubscriptionToken localLobbyReadyForRematchToken;
		private TinyMessageSubscriptionToken playerInfoChangedToken;
        private TinyMessageSubscriptionToken exitGameplayToken;
		private TinyMessageSubscriptionToken relayDisconnect;
		private TinyMessageSubscriptionToken hostSendRelayJoinCode;
        #endregion

        private Coroutine relayCoroutine;
        private Coroutine heartBeatCoroutine;
        private bool isMatching = false;

        private void Awake()
        {

		}

		private void OnEnable()
		{
			// button clicks
			queueStartButtonClickToken = ActionDispatcher.Bind<QueueStartButtonClickAction>(QueueStartButtonClick);
			cancelMatchMakingButtonClickToken = ActionDispatcher.Bind<MatchMakingCancelAction>(OnCancel);

			// events from lobby handlers
			playerJoinedToken = ActionDispatcher.Bind<PlayerJoinedAction>(PlayerJoined);
			playerLeftToken = ActionDispatcher.Bind<PlayerLeftAction>(PlayerLeft);
			lobbyChangedToken = ActionDispatcher.Bind<LobbyChangedAction>(LobbyChanged);
			playerDataChangedToken = ActionDispatcher.Bind<PlayerStatusChangedToLobbyAction>(PlayerStatusChangedToLobby); // check if every players status has been set to lobby
			localLobbyStateChangedToken = ActionDispatcher.Bind<LocalLobbyStateChangedAction>(OnLocalLobbyStateChanged);
			localLobbyAddPlayerToken = ActionDispatcher.Bind<LocalLobbyAddPlayerAction>(OnLocalLobbyAddPlayer);
			
			localPlayerReadyFoRematchToken = ActionDispatcher.Bind<LocalPlayerReadyForRematch>(OnPlayerReadyForRematch);
			localLobbyReadyForRematchToken = ActionDispatcher.Bind<LocalLobbyRematchAction>(OnReadyForNewMatch);
			lobbyRequireRematchToken = ActionDispatcher.Bind<RequireRematchAction>(OnRequireRematch);
			lobbyReconfigToken = ActionDispatcher.Bind<LocalLobbyReConfigForRematch>(OnReconfig);
			
			// finished match making from the end of ui animation
			matchingToken = ActionDispatcher.Bind<MatchMakingFinishAction>(OnFinishMatching);

			// player info changed
			playerInfoChangedToken = ActionDispatcher.Bind<UpdatePlayerInfoAction>(OnPlayerInfoChanged);

            exitGameplayToken = ActionDispatcher.Bind<ExitGameplayAction>(OnExitGamePlay);
			relayDisconnect = ActionDispatcher.Bind<DisconnectRelayServerAction>(OnDisconnectRelay);
			hostSendRelayJoinCode = ActionDispatcher.Bind<LobbyHostSendRelayJoinCode>(OnHostSendRelayJoinCode);
        }

        #region Lobby Rematch Logic

        private void OnReconfig(LocalLobbyReConfigForRematch obj)
		{
			ConfigLobby();
            _ = lobbyHandler.SyncLobbyData(localLobby);
        }
		
		private async void OnReadyForNewMatch(LocalLobbyRematchAction obj)
		{
			await _cloudCode.StartGameSessionRequest(gameplayBus.currentLobbyProperty.TourInfo.GetEntryFee());
            _ = MasterManager.Instance.HideAllComponents();
			var holeGuid = gameplayBus.currentLobbyProperty.CourseInfo.CurrentHole.Guid;
			GlobalSO.PlayFieldSO.SetHoleInfo(holeGuid);
			await MapLoader.Instance.LoadMapAsync(holeGuid);
		}

		private void OnRequireRematch(RequireRematchAction obj)
		{
            _ = MasterManager.Instance.OpenUIComponent(UIComponentEnum.H2HRematchPopupUIComponent, new object[] { obj.OpponentPlayer });
		}

		private async void OnPlayerReadyForRematch(LocalPlayerReadyForRematch obj)
		{
			object[]  data = new object[1];
			data[0] = 1;

            _ = MasterManager.Instance.OpenUIComponent(UIComponentEnum.MatchMakingComponentUI, data);
			
			// Check Bot Fake Matching
			var opponentPlayer = localLobby.LocalPlayers.Find((player => player.GetId() != localPlayer.GetId()));
			if (opponentPlayer != null)
			{
				if (opponentPlayer.IsBot)
				{
					await Task.Delay(WaitingTimeBotRematch * 1000);
					// // Accept Logic
					// opponentPlayer.ResetPlayer();
					// opponentPlayer.SetUserStatus(PlayerStatus.ReadyForRematch);
					//Decline Logic 
					var botIndex = -1;
					for (int i = 0; i < localLobby.LocalPlayers.Count; i++)
					{
						if (localLobby.LocalPlayers[i].GetId() != localPlayer.GetId())
						{
							botIndex = i;
							break;
						}
					}
					if (botIndex != -1)
					{
						localLobby.RemovePlayer(botIndex);	
					}
				}
			}
		}

		#endregion
		

		private void OnDisable()
		{
			ActionDispatcher.Unbind(queueStartButtonClickToken);
			ActionDispatcher.Unbind(cancelMatchMakingButtonClickToken);
			ActionDispatcher.Unbind(playerJoinedToken);
			ActionDispatcher.Unbind(playerLeftToken);
			ActionDispatcher.Unbind(lobbyChangedToken);
			ActionDispatcher.Unbind(playerDataChangedToken);
			ActionDispatcher.Unbind(localLobbyStateChangedToken);
			ActionDispatcher.Unbind(localLobbyAddPlayerToken);
			ActionDispatcher.Unbind(localPlayerReadyFoRematchToken);
			ActionDispatcher.Unbind(lobbyRequireRematchToken);
			ActionDispatcher.Unbind(localLobbyReadyForRematchToken);
			ActionDispatcher.Unbind(lobbyReconfigToken);
            ActionDispatcher.Unbind(matchingToken);
			ActionDispatcher.Unbind(playerInfoChangedToken);
			ActionDispatcher.Unbind(exitGameplayToken);
			ActionDispatcher.Unbind(relayDisconnect);
			ActionDispatcher.Unbind(hostSendRelayJoinCode);
        }
		
		public override UniTask Init(MasterManager masterManager)
		{
			MasterManager = masterManager;
			
			playerInfo = GlobalSO.PlayerInfoSO.Info;

			relayHandler = new RelayHandler();
			lobbyHandler = new LobbyHandler();
			localPlayer = new LocalPlayer(playerInfo.Id, playerInfo.DisplayName, playerInfo.H2hRank, new PlayerAvatarData(playerInfo.Avatar.Id));

			gameplayBus = GlobalSO.GameplayBus;
			gameplayBus.Reset();
			gameplayBus.localPlayer = localPlayer;

			findingLobbyStateController = GetComponent<FindingLobbyStateController>();
			findingLobbyStateController.Init(this);
			
			return UniTask.CompletedTask;
		}

		public override void Reset()
		{
		}

        private void QueueStartButtonClick(QueueStartButtonClickAction ctx)
        {
            LobbyProperty currentLobbyProperty = ctx.Lobby;
            gameplayBus.currentLobbyProperty = currentLobbyProperty;

			ActionDispatcher.Dispatch(new PopupCloseMessage());
            _ = MasterManager.Instance.OnChangeScreen(EScreenEnum.MatchMaking);

            CheckBalanceAndStartMatchMaking(currentLobbyProperty.TourInfo.GetEntryFee());
        }

        private async void CheckBalanceAndStartMatchMaking(int entryFee)
        {
            bool isBalanceValid = await ValidateBalance(entryFee);

			if (!isBalanceValid)
			{
				Debug.Log("Not enough balance to play!");
				return;
			}

			StartMatchMaking();
        }

		private void StartMatchMaking()
		{
            isMatching = true;

            findingLobbyStateController.ReceiveEvent(FindingLobbyEvent.START_FINDING, new object[1] { false });
        }

		public async Task<LocalLobby> FindLobbyToJoin()
		{
			string courseId = gameplayBus.currentLobbyProperty.CourseInfo.Id;

			localLobby = await lobbyHandler.JoinLobbyAsync(courseId, localPlayer);
			
			GlobalSO.GameplayBus.localLobby = localLobby;

            foreach (LocalPlayer player in localLobby.LocalPlayers)
			{
				if (AuthenticationService.Instance.PlayerId == player.GetId())
				{
					localPlayer = player;
					gameplayBus.localPlayer = localPlayer;
				}
			}

            return localLobby;
		}

        private void OnHostSendRelayJoinCode(LobbyHostSendRelayJoinCode action)
        {
            _ = JoinHostServerRelay(action.relayJoinCode);
        }

        public async Task JoinHostServerRelay(string relayJoinCode)
        {
            await relayHandler.PlayerJoinHostUsingJoinCode(relayJoinCode);
			
            ConfigPingRelay();

            string guestAllocationId = relayHandler.GetGuestAllocationId();
			localPlayer.SetRelayJoinCodeID(guestAllocationId);
			await lobbyHandler.UpdatePlayerConnectionData(guestAllocationId);
        }

        public async Task<string> CreateHostServerRelay(Action onSucess = null)
		{
			string joinCode = await relayHandler.HostAllocateAndGetCode(1);
			onSucess?.Invoke();
            ConfigPingRelay();
			return joinCode;
        }

        public async Task CreateLobby()
		{
            localLobby = await lobbyHandler.CreateLobbyAsync(gameplayBus.currentLobbyProperty, localPlayer);
			foreach (LocalPlayer player in localLobby.LocalPlayers)
			{
				if (AuthenticationService.Instance.PlayerId == player.GetId())
				{
					localPlayer = player;
                    gameplayBus.localPlayer = localPlayer;
                }
			}
        }

		public void CreateBotMatch()
		{
            StartCoroutine(ConfigLobbyData(isBot: true)); // from here
        }

		private bool disposeLobbyRestartInterrupted = false;

        public async Task DisposeLobby(bool isRestart)
        {
			if (localLobby != null)
			{
                disposeLobbyRestartInterrupted = false;

                await lobbyHandler.DeleteLobby(localLobby.GetLobbyID());

                if (isRestart)
                {
					if (disposeLobbyRestartInterrupted)
					{
						return;
					}

                    findingLobbyStateController.ReceiveEvent(FindingLobbyEvent.DELETE_RESTART, new object[1] { true });
                }
            }
        }

		public async Task LockLobby()
		{
			await lobbyHandler.LockLobby();
		}

		public void InterruptDisposeLobbyRestart()
		{
			disposeLobbyRestartInterrupted = true;
        }

        private void OnCancel(MatchMakingCancelAction ctx)
        {
            _ = LobbyFindStateMachineCancel();
        }

		private async Task LobbyFindStateMachineCancel()
		{
            Debug.Log("Current finding lobby state CANCELED: " + findingLobbyStateController.currentState.GetType().ToString());
            await findingLobbyStateController.currentState.OnCancel();
            _ = MasterManager.Instance.OnChangeScreen(EScreenEnum.HeadToHeadStage);
        }

        private void OnFinishMatching(MatchMakingFinishAction obj)
        {
            isMatching = false;
        }

        #region lobby handler events
        private void PlayerJoined(PlayerJoinedAction ctx)
		{
			Debug.Log("Player Joined UGS");
        }

        private void PlayerLeft(PlayerLeftAction ctx) 
		{
            Debug.Log("Player Left UGS");
        }

        private void LobbyChanged(LobbyChangedAction ctx) 
		{
			
		}

        private void PlayerStatusChangedToLobby(PlayerStatusChangedToLobbyAction ctx)
        {
            Debug.Log("Player number count: " + localLobby.PlayerCount + "/" + localLobby.GetMaxPlayerCount());
			if (localLobby.PlayerCount < localLobby.GetMaxPlayerCount()) 
			{ 
				return; 
			}
            CheckLobbyPlayerStatus();
        }

        private void CheckLobbyPlayerStatus()
        {
            bool isAllJoined = localLobby.LocalPlayers.All(x => x.GetUserStatus() == PlayerStatus.Lobby);
			if (isAllJoined)
			{
				StartCoroutine(ConfigLobbyData(isBot: false)); // from here
			}
        }
        #endregion

        #region local lobby events
        private void OnLocalLobbyAddPlayer(LocalLobbyAddPlayerAction data)
        {
			if (localLobby.PlayerCount != localLobby.GetMaxPlayerCount()) 
			{ 
				return; 
			}
            findingLobbyStateController.ReceiveEvent(FindingLobbyEvent.PLAYER_JOIN, null);
        }

        private void OnLocalLobbyStateChanged(LocalLobbyStateChangedAction action)
		{
			switch (action.LobbyState)
			{
				case LobbyState.ReadyToStart:
					StartCoroutine(CoWaitMatching()); // to here
					break;
				case LobbyState.ReadyForRematch:
					ActionDispatcher.Dispatch(new LocalLobbyReadyForNewMatch(localLobby.LocalPlayers));
					break;
			}
		}
        #endregion

        private IEnumerator CoWaitMatching()
        {
	        yield return new WaitUntil(() => isMatching == false);
	        StartGame();
	        isMatching = false;
        }

        #region Setup Lobby Data Before Ready To Start Game

        private bool doneLeaveLobbyBotMatch;

        private void LeaveLobbyBotMatchCallback()
        {
            doneLeaveLobbyBotMatch = true;
        }
        private bool doneCreatingHostServerRelay;

        private void CreateHostServerRelayCallback()
        {
            doneCreatingHostServerRelay = true;
        }

        private IEnumerator ConfigLobbyData(bool isBot)
		{
            Debug.Log("Config lobby. Host? :" + localPlayer.GetIsHost());
            if (isBot)
			{
				// leave lobby if bot match
				doneLeaveLobbyBotMatch = false;

                _ = lobbyHandler.LeaveLobbyAsync(true, LeaveLobbyBotMatchCallback);
				yield return new WaitUntil(() => doneLeaveLobbyBotMatch == true);

                ConfigLobby();

                for (var i = localLobby.LocalPlayers.Count; i < localLobby.GetMaxPlayerCount(); i++)
                {
                    localLobby.AddPlayer(i, LocalPlayer.GenerateBot(i.ToString()));
                }

                findingLobbyStateController.ReceiveEvent(FindingLobbyEvent.CREATE_BOT, null);
            }
			else
			{
				if (localPlayer.GetIsHost())
				{
					// create relay server if player is the host of lobby
					doneCreatingHostServerRelay = false;

					_ = CreateHostServerRelay(CreateHostServerRelayCallback);
					yield return new WaitUntil(() => doneCreatingHostServerRelay == true);

                    string allocationId = relayHandler.GetHostAllocationId();
                    string joinCode = relayHandler.GetJoinCode();

					localPlayer.SetAllocationID(allocationId);
					localPlayer.SetRelayJoinCodeID(joinCode);

					// add allocation id and join code into players data
                    _ = lobbyHandler.SyncPlayerData(localPlayer);
					// update allocation id and join code of player on remote lobby
                    _ = lobbyHandler.UpdatePlayerConnectionData(joinCode, allocationId);

					// set up lobby data
					ConfigLobby();

					// Truc's note [11/3/25]
					// Okay so I only have the faintest of idea as to fucking why there needs to be
					// a 3-second wait here before the host updates the lobby's custom data.
					// Logically there's no need to but apparently somehow there exist edge cases
					// in which the joining player has NOT finished binding to the lobby data change event callbacks
					// even after the host had received the JOIN REQUEST from the player.
					// 3 seconds is a mere speculation thru lots of trial and errors as any less than that proves
					// to have caused the very rare issue.
					// TLDR: do not touch this line of code til a solid solution is found.
					yield return new WaitForSeconds(3f);

                    _ = lobbyHandler.SyncLobbyData(localLobby);
                }
            }
        }

		private void ConfigLobby()
		{
            localLobby.GenerateGameWeather(gameplayBus.currentLobbyProperty);
            localLobby.GenerateTee();
            localLobby.GenerateCurrentHole(gameplayBus.currentLobbyProperty.CourseInfo.Holes.Count);
            localLobby.SetLocalLobbyState(LobbyState.ReadyToStart);
        }
        #endregion

		private void StartGame()
		{
            findingLobbyStateController.ReceiveEvent(FindingLobbyEvent.START_GAME, null);

            ConfigHeartbeat();
			CollectFeeAndStartGame();
		}

		private void ConfigPingRelay()
		{
			relayCoroutine = StartCoroutine(PingRelay());
		}

		private IEnumerator PingRelay()
		{
			while (true)
			{
				relayHandler.Update();
				yield return new WaitForEndOfFrame();
			}
		}

		private void ConfigHeartbeat()
		{
			if (localLobby.GetHostID() != localPlayer.GetId()) return;
			heartBeatCoroutine = StartCoroutine(CoSendHeartBeat());
		}

		private IEnumerator CoStartGame()
		{
			yield return new WaitForSeconds(1);
            _ = MasterManager.Instance.OnChangeScreen(EScreenEnum.Empty);
			GamePropertyPrepare();
		}

		private IEnumerator CoSendHeartBeat()
		{
			var delay = new WaitForSecondsRealtime(25);

			while (!string.IsNullOrEmpty(localLobby.GetLobbyID()))
			{
				Debug.Log("Heart beat sent");
				lobbyHandler.SendHeartBeat(localLobby.GetLobbyID());
				yield return delay;
			}
		}

		private void GamePropertyPrepare()
		{
			gameplayBus.relayHandler = relayHandler;
			gameplayBus.lobbyHandler = lobbyHandler;
			gameplayBus.localLobby = localLobby;
			gameplayBus.localPlayer = localPlayer;

			ActionDispatcher.Dispatch(new RegisterLogicAction(gameplayBus.currentLobbyProperty.GameMode));
			var holeGuid = gameplayBus.currentLobbyProperty.CourseInfo.CurrentHole.Guid;
			GlobalSO.PlayFieldSO.SetHoleInfo(holeGuid);
			MapLoader.Instance.LoadMap(holeGuid);
		}


        private void OnExitGamePlay(ExitGameplayAction action)
        {
			StopCoroutine(heartBeatCoroutine);
        }

        private void OnDisconnectRelay(DisconnectRelayServerAction action)
        {
            StopCoroutine(relayCoroutine);
        }

        #region UGS

        private readonly UGSCloudCode _cloudCode = new UGSCloudCode();

		private async void CollectFeeAndStartGame()
		{
			var currentLobbyProperty = gameplayBus.currentLobbyProperty;
			var consumeCoinFeeResponse = await APIGameClient.Instance.ConsumeCoinFee(currentLobbyProperty.TourInfo.GetUuid());
			ActionDispatcher.Dispatch(new GetPlayerCurrenciesAction());
			
			if (consumeCoinFeeResponse.responseCode != APIResponseCode.Success)
			{
				var data = new GeneralPopupContainer(
					title: "Data Sync Issue",
					description: "We encountered a problem with your data. Please try again",
					continueButtonText: "OK",
					continueAction: () => { ActionDispatcher.Dispatch(new ExitGameplayAction()); }
				);
				MasterManager.OpenUIComponent(UIComponentEnum.GeneralPopupUIComponent, new object[] { data }).Forget();
				return;
			}
			
			TrackingManager.Instance.TrackSpendCurrency(UGSEconomy.CoinCurrencyId, currentLobbyProperty.CourseInfo.Id, currentLobbyProperty.TourInfo.GetEntryFee());
			StartCoroutine(CoStartGame());
		}

		private async Task<bool> ValidateBalance(int entryFee)
		{
			var validateBalanceResponse =  await APIGameClient.Instance.ValidateCurrency(UGSEconomy.CoinCurrencyId, entryFee);
			return validateBalanceResponse.responseCode == APIResponseCode.Success && validateBalanceResponse.data;
		}

		private void OnPlayerInfoChanged(UpdatePlayerInfoAction ctx)
		{
			var local = new LocalPlayer(playerInfo.Id, playerInfo.DisplayName, playerInfo.H2hRank, new PlayerAvatarData(playerInfo.Avatar.Id));

			localPlayer.SetId(playerInfo.Id);
			localPlayer.SetDisplayName(playerInfo.DisplayName);
			localPlayer.SetPlayerRankInfo(playerInfo.H2hRank);
			localPlayer.SetPlayerAvatarInfo(new PlayerAvatarData(playerInfo.Avatar.Id));
		}

		#endregion
	}
}