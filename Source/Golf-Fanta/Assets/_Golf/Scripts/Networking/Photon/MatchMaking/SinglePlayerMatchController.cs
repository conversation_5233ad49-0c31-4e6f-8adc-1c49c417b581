using System;
using System.Collections.Generic;
using _Golf.Scripts.Common;
using _Golf.Scripts.Core;
using _Golf.Scripts.Networking.Courses;
using _Golf.Scripts.Networking.Photon.Lobby;
using _Golf.Scripts.SceneLoader;
using _Golf.Scripts.ScriptableObjects;
using _Golf.Scripts.ScriptableObjects.Tours;
using _Golf.Scripts.UI;
using Cysharp.Threading.Tasks;
using GolfGame;
using TinyMessenger;
using Unity.Services.Authentication;
using UnityEngine;

namespace _Golf.Scripts.Networking
{
    public enum EStage
    {
        Empty = -1,
        Stage1 = 1,
        Stage2 = 2
    }
    [Serializable]
    public class StageInfo
    {
        [field: SerializeField] public EStage Stage { get; private set; }
        [field: SerializeField] public SceneAssetReference AssetReference { get; private set; }
    }
    public class SinglePlayerMatchController : BaseControllerComponent
    {
        private SinglePlayProperties _properties;
        private Core.PlayerInfo _playerInfo;
        
        private GameplayBus _gameplayBus;
        private Dictionary<EStage, SceneAssetReference> _allStageInfoDict = new();
        private TinyMessageSubscriptionToken _messageSubscription;
        private LocalLobby _singlePlayLobby;
        private LocalPlayer _singleLocalPlayer;

        private void OnEnable()
        {
            _messageSubscription = ActionDispatcher.Bind<StartSinglePlayerAction>(RequestStartStage);
        }

        private void OnDisable()
        {
            ActionDispatcher.Unbind(_messageSubscription);
        }
        
        public override UniTask Init(MasterManager masterManager)
        {
            MasterManager = masterManager;
            
            _gameplayBus = GlobalSO.GameplayBus;
            _properties = GlobalSO.SinglePlayProperties;
            _playerInfo = GlobalSO.PlayerInfoSO.Info;
            
            foreach (var info in _properties.StageInfos)
            {
                _allStageInfoDict.TryAdd(info.Stage, info.AssetReference);
            }
            var playerId = AuthenticationService.Instance.PlayerId;
            _singlePlayLobby = new LocalLobby("", "", "",
                playerId, LobbyState.Lobby, true, true, 1, 1);
            _singleLocalPlayer = new LocalPlayer(playerId, _playerInfo.DisplayName, _playerInfo.H2hRank, new PlayerAvatarData(_playerInfo.Avatar.Id));
            _singleLocalPlayer.SetUserStatus(PlayerStatus.InGame);
            _singlePlayLobby.AddPlayer(0,_singleLocalPlayer);
            
            return UniTask.CompletedTask;
        }

        public override void Reset()
        {
        }

        private void RequestStartStage(StartSinglePlayerAction ctx)
        {
            StartSinglePlayerMatch(ctx.Info);
        }

        private void StartSinglePlayerMatch(HoleInfo info)
        {
            MasterManager.OnChangeScreen(EScreenEnum.Empty);
            PrepareSinglePlayerMatch();
            MapLoader.Instance.LoadMap(info.Guid);
        }

        private void PrepareSinglePlayerMatch()
        {
            _gameplayBus.localLobby = _singlePlayLobby;
            _gameplayBus.localPlayer = _singleLocalPlayer;
            ActionDispatcher.Dispatch(new RegisterLogicAction(GameMode.ClosestToPin));
        }
    }

    public class StartSinglePlayerAction : ActionBase
    {
        public HoleInfo Info;
        public StartSinglePlayerAction(HoleInfo info)
        {
            Info = info;
        }
    }
}