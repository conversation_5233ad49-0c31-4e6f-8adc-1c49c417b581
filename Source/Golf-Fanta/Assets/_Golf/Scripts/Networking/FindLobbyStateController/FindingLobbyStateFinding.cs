using _Golf.Scripts.Core;
using _Golf.Scripts.Lobby;
using _Golf.Scripts.Networking.Photon;
using _Golf.Scripts.Networking.Photon.Lobby;
using System;
using System.Threading.Tasks;
using Unity.Services.Authentication;
using UnityEngine;
using UnityEngine.InputSystem.Controls;

namespace GolfGame
{
    public class FindingLobbyStateFinding : IFindingLobbyState
    {
        private MultiPlayerController multiPlayerController;
        private FindingLobbyStateController findingLobbyStateController;

        bool doneWaiting = false; bool isFindLobby = false;
        float delaySecond = 0f;

        public FindingLobbyStateFinding(FindingLobbyStateController findLobbyStateController, MultiPlayerController pvpController)
        {
            findingLobbyStateController = findLobbyStateController;
            multiPlayerController = pvpController;
        }

        public void OnEnter(object[] data)
        {
            Debug.Log("[FINDING LOBBY STATE CONTROLLER] FindLobbyState Enter: Finding");

            delaySecond = UnityEngine.Random.Range(multiPlayerController.GetMinRandomDelayMatchRange(), multiPlayerController.GetMaxRandomDelayMatchRange());
            doneWaiting = false; isFindLobby = false;

            if (data != null)
            {
                doneWaiting = (bool)data[0];
            }

            if (doneWaiting) 
            {
                Debug.Log("[FINDING LOBBY STATE CONTROLLER] Start Finding lobby!");
                isFindLobby = true;
                FindLobby();
            }
            else
            {
                Debug.Log(String.Format("[FINDING LOBBY STATE CONTROLLER] Wait for {0} seconds until finding lobby", delaySecond));
            }
        }

        private async void FindLobby()
        {
            LocalLobby lobby = await multiPlayerController.FindLobbyToJoin();

            if (string.IsNullOrEmpty(lobby.GetLobbyID()))
            {
                findingLobbyStateController.ReceiveEvent(FindingLobbyEvent.NO_LOBBY_FOUND, null);
            }
            else
            {
                findingLobbyStateController.ReceiveEvent(FindingLobbyEvent.LOBBY_FOUND, null);
            }
        }

        public void Update()
        {
            if (doneWaiting) return;

            delaySecond -= Time.deltaTime;

            if (delaySecond <= 0)
            {
                doneWaiting = true;

                Debug.Log("[FINDING LOBBY STATE CONTROLLER] Done waiting. Start Finding lobby!");
                isFindLobby = false;
                FindLobby();
            }
        }

        public void OnExit()
        {

        }

        public FindingLobbyState OnEvent(FindingLobbyEvent e, object[] data)
        {
            switch (e)
            {
                case FindingLobbyEvent.LOBBY_FOUND:
                    {
                        Debug.Log("FindLobbyState [Finding] -> FindingLobbyEvent.LOBBY_FOUND -> FindLobbyState [Complete]");
                        return FindingLobbyState.COMPLETE;
                    }
                case FindingLobbyEvent.NO_LOBBY_FOUND:
                    {
                        Debug.Log("FindLobbyState [Finding] -> FindingLobbyEvent.NO_LOBBY_FOUND -> FindLobbyState [Create then wait]");
                        return FindingLobbyState.CREATE_WAIT;
                    }
                default:
                    {
                        return FindingLobbyState.NONE;
                    }
            }
        }

        public async Task OnCancel()
        {
            if (doneWaiting == false)
            {
                doneWaiting = true;
                findingLobbyStateController.ChangeState(FindingLobbyState.IDLE, null);
            }
            else
            {
                if (!isFindLobby) 
                {
                    findingLobbyStateController.ChangeState(FindingLobbyState.IDLE, null);
                }
                else
                {
                    // lobby handler is finding lobby => interrupt it
                    findingLobbyStateController.ChangeState(FindingLobbyState.IDLE, null);
                }
            }
        }
    }
}
