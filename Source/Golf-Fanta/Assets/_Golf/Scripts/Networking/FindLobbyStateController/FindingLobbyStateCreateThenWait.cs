using _Golf.Scripts.Networking.Photon;
using _Golf.Scripts.UI;
using System;
using System.Threading.Tasks;
using UnityEngine;

namespace GolfGame
{
    public class FindingLobbyStateCreateThenWait : IFindingLobbyState
    {
        private MultiPlayerController multiPlayerController;
        private FindingLobbyStateController findingLobbyStateController;

        float waitTime = 0;
        bool isWaiting = false;
        bool botEnabled = false;
        bool isDisposingLobby = false;

        public FindingLobbyStateCreateThenWait(FindingLobbyStateController findLobbyStateController, MultiPlayerController pvpController)
        {
            findingLobbyStateController = findLobbyStateController;
            multiPlayerController = pvpController;
        }

        public void OnEnter(object[] data)
        {
            Debug.Log("[FINDING LOBBY STATE CONTROLLER] FindLobbyState Enter: Create then wait");

            isWaiting = false; isDisposingLobby = false;
            botEnabled = multiPlayerController.GetBotEnabledStatus();

            waitTime = multiPlayerController.GetWaitingTimePostLobbyCreation();

            CreateLobby();
        }

        private async void CreateLobby()
        {
            Debug.Log("[FINDING LOBBY STATE CONTROLLER] Start Creating Lobby.");
            await multiPlayerController.CreateLobby();
            
            isWaiting = true;
            Debug.Log("[FINDING LOBBY STATE CONTROLLER] Start Waiting For Anyone To Join.");
        }

        private async void DisposeLobby(bool isRestart)
        {
            Debug.Log("[FINDING LOBBY STATE CONTROLLER] Start Deleting Lobby.");

            await multiPlayerController.DisposeLobby(isRestart);
        }

        bool isLockingLobby;
        bool playerJoinDuringLocking;

        private async void LockLobby()
        {
            isLockingLobby = true;
            playerJoinDuringLocking = false;

            await multiPlayerController.LockLobby();
            isLockingLobby = false;

            if (!playerJoinDuringLocking)
            {
                // no player joins during locking lobby
                multiPlayerController.CreateBotMatch();
            }
        }

        public void Update()
        {
            if (isWaiting)
            {
                waitTime -= Time.deltaTime;

                // check if opponent found

                if (waitTime <= 0)
                {
                    isWaiting = false;

                    // delete lobby or create bot
                    if (botEnabled)
                    {
                        Debug.Log("[FINDING LOBBY STATE CONTROLLER] Wait Time Post Lobby Creation Over! Initiate Bot Match!");
                        LockLobby();
                    }
                    else
                    {
                        Debug.Log("[FINDING LOBBY STATE CONTROLLER] Wait Time Post Lobby Creation Over! Delete Lobby And Restart!");
                        isDisposingLobby = true;
                        DisposeLobby(isRestart: true);
                    }
                }
            }
        }

        public void OnExit()
        {

        }

        public FindingLobbyState OnEvent(FindingLobbyEvent e, object[] data)
        {
            switch (e)
            {
                case FindingLobbyEvent.CREATE_BOT:
                    {
                        Debug.Log("FindLobbyState [Create then wait] -> FindingLobbyEvent.CREATE_BOT -> FindLobbyState [Complete]");
                        return FindingLobbyState.COMPLETE;
                    }
                case FindingLobbyEvent.PLAYER_JOIN:
                    {
                        if (isWaiting)
                        {
                            Debug.Log("FindLobbyState [Create then wait] -> FindingLobbyEvent.PLAYER_JOIN -> FindLobbyState [Complete]");

                            if (isLockingLobby)
                            {
                                playerJoinDuringLocking = true;
                            }

                            return FindingLobbyState.COMPLETE;
                        }
                        else
                        {
                            return FindingLobbyState.NONE;
                        }
                    }
                case FindingLobbyEvent.DELETE_RESTART:
                    {
                        Debug.Log("FindLobbyState [Create then wait] -> FindingLobbyEvent.DELETE_RESTART -> FindLobbyState [Finding]");
                        return FindingLobbyState.FINDING;
                    }
                default:
                    {
                        return FindingLobbyState.NONE;
                    }
            }
        }

        public async Task OnCancel()
        {
            if (isWaiting)
            {
                Debug.Log("[FINDING LOBBY STATE CONTROLLER] Start Deleting Lobby On Cancel.");
                await multiPlayerController.DisposeLobby(false);
                findingLobbyStateController.ChangeState(FindingLobbyState.IDLE, null);
            }
            else
            {
                if (!isDisposingLobby)
                {
                    await multiPlayerController.DisposeLobby(false);
                    findingLobbyStateController.ChangeState(FindingLobbyState.IDLE, null);
                }
                else
                {
                    multiPlayerController.InterruptDisposeLobbyRestart();
                    findingLobbyStateController.ChangeState(FindingLobbyState.IDLE, null);
                }
            }
        }
    }
}
