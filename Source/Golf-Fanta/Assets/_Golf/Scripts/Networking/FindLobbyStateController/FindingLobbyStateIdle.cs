using _Golf.Scripts.Networking.Photon;
using System.Threading.Tasks;
using UnityEngine;

namespace GolfGame
{
    public class FindingLobbyStateIdle : IFindingLobbyState
    {
        private MultiPlayerController multiPlayerController;
        private FindingLobbyStateController findingLobbyStateController;

        public FindingLobbyStateIdle(FindingLobbyStateController findLobbyStateController, MultiPlayerController pvpController) 
        {
            findingLobbyStateController = findLobbyStateController;
            multiPlayerController = pvpController;
        }

        public void OnEnter(object[] data)
        {
            Debug.Log("[FINDING LOBBY STATE CONTROLLER] FindLobbyState Enter: Idle");
        }

        public void Update()
        {

        }

        public void OnExit()
        {

        }

        public FindingLobbyState OnEvent(FindingLobbyEvent e, object[] data)
        {
            switch (e)
            {
                case FindingLobbyEvent.START_FINDING:
                    {
                        bool isRestart = false;
                        if (data != null)
                        {
                            isRestart = (bool)data[0];
                        }
                        Debug.Log("FindLobbyState [Idle] -> FindingLobbyEvent.START_FINDING -> FindLobbyState [Finding] (Restart: " + isRestart + ")");
                        return FindingLobbyState.FINDING;
                    }
                default:
                    {
                        return FindingLobbyState.NONE;
                    }
            }
        }

        public async Task OnCancel()
        {
            
        }
    }
}
