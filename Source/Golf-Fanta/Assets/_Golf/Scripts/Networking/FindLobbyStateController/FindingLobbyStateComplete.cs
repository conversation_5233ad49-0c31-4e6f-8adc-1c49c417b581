using _Golf.Scripts.Common;
using _Golf.Scripts.Core;
using _Golf.Scripts.Lobby;
using _Golf.Scripts.Networking.Photon;
using _Golf.Scripts.Networking.Photon.Lobby;
using System.Linq;
using System.Threading.Tasks;
using UnityEngine;

namespace GolfGame
{
    public class FindingLobbyStateComplete : IFindingLobbyState
    {
        private MultiPlayerController multiPlayerController;
        private FindingLobbyStateController findingLobbyStateController;

        public FindingLobbyStateComplete(FindingLobbyStateController findLobbyStateController, MultiPlayerController pvpController)
        {
            findingLobbyStateController = findLobbyStateController;
            multiPlayerController = pvpController;
        }

        public void OnEnter(object[] data)
        {
            Debug.Log("[FINDING LOBBY STATE CONTROLLER] FindLobbyState Enter: Complete");

            LocalPlayer opponent = multiPlayerController.GetLocalLobby().LocalPlayers.FirstOrDefault(x => x.GetId() != multiPlayerController.GetLocalPlayer().GetId());
            ActionDispatcher.Dispatch(new FoundOpponentAction(opponent, multiPlayerController.GetBus().currentLobbyProperty));

            multiPlayerController.GetLocalPlayer().SetUserStatus(PlayerStatus.Lobby);
            _ = multiPlayerController.GetLobbyHandler().SyncPlayerData(multiPlayerController.GetLocalPlayer());
        }

        public void Update()
        {

        }

        public void OnExit()
        {

        }

        public FindingLobbyState OnEvent(FindingLobbyEvent e, object[] data)
        {
            switch (e)
            {
                case FindingLobbyEvent.START_GAME:
                    {
                        Debug.Log("FindLobbyState [Complete] -> FindingLobbyEvent.START_GAME -> FindLobbyState [Idle]");
                        return FindingLobbyState.IDLE;
                    }
                default:
                    {
                        return FindingLobbyState.NONE;
                    }
            }
        }

        public async Task OnCancel()
        {
            await multiPlayerController.DisposeLobby(false);
            findingLobbyStateController.ChangeState(FindingLobbyState.IDLE, null);
        }
    }
}
