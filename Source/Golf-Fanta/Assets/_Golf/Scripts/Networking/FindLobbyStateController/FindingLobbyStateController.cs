using _Golf.Scripts.Networking.Photon;
using System.Collections.Generic;
using UnityEngine;

namespace GolfGame
{
    public class FindingLobbyStateController : MonoBehaviour
    {
        #region State controller
        public IFindingLobbyState currentState;

        private Dictionary<FindingLobbyState, IFindingLobbyState> states;
        #endregion

        private MultiPlayerController multiPlayerController;

        public void Init(MultiPlayerController pvpController)
        {
            multiPlayerController = pvpController;

            states = new Dictionary<FindingLobbyState, IFindingLobbyState>()
            {
                [FindingLobbyState.IDLE] = new FindingLobbyStateIdle(this, multiPlayerController),
                [FindingLobbyState.FINDING] = new FindingLobbyStateFinding(this, multiPlayerController),
                [FindingLobbyState.CREATE_WAIT] = new FindingLobbyStateCreateThenWait(this, multiPlayerController),
                [FindingLobbyState.COMPLETE] = new FindingLobbyStateComplete(this, multiPlayerController),
            };

            currentState = states[FindingLobbyState.IDLE];
            currentState.OnEnter(null);
        }

        public void ReceiveEvent(FindingLobbyEvent e, object[] data)
        {
            FindingLobbyState state = currentState.OnEvent(e, data);

            if (state == FindingLobbyState.NONE)
            {
                return;
            }
            else
            {
                ChangeState(state, data);
            }
        }

        public void ChangeState(FindingLobbyState index, object[] data)
        {
            ChangeState(states[index], data);
        }

        private void ChangeState(IFindingLobbyState newState, object[] data)
        {
            currentState.OnExit();
            currentState = newState;
            currentState.OnEnter(data);
        }

        public void Update()
        {
            if (currentState != null)
            {
                currentState.Update();
            }
        }
    }
}
