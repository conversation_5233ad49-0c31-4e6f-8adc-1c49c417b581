using System.Collections.Generic;
using System.Threading.Tasks;
using _Golf.Scripts.Common;
using UnityEngine;

namespace _Golf.Scripts.Lobby
{
    public abstract class PlayerSyncable : MonoBehaviour
    {
        [HideInInspector] public bool isWaitingTilNextSync = false;
        [HideInInspector] public int waitingTilNextSyncTime = 1000;

        #region Cache
        Dictionary<PlayerCommandSignature, PlayerCommand> cacheCommands = new Dictionary<PlayerCommandSignature, PlayerCommand>();
        #endregion

        public bool CheckCacheIfSyncIsNeeded(PlayerCommandSignature signature, PlayerCommand command)
        {
            switch (signature)
            {
                case PlayerCommandSignature.DragBallCommand:
                    {
                        if (!cacheCommands.ContainsKey(signature)) { return true; }
                        else
                        {
                            DragBallCommand cachedCmd = cacheCommands[signature] as DragBallCommand;
                            DragBallCommand newCmd = command as DragBallCommand;

                            if (Mathf.Abs(cachedCmd.dragPercentage - newCmd.dragPercentage) > 0.01f)
                            {
                                return true;
                            }
                            else
                            {
                                return false;
                            }
                        }
                    }
                case PlayerCommandSignature.PuttBallCommand:
                    {
                        if (!cacheCommands.ContainsKey(signature)) { return true; }
                        else
                        {
                            PuttBallCommand cachedCmd = cacheCommands[signature] as PuttBallCommand;
                            PuttBallCommand newCmd = command as PuttBallCommand;

                            Vector3 ballPosCachedCmd = new Vector3(cachedCmd.ballImagePositionX, cachedCmd.ballImagePositionY, cachedCmd.ballImagePositionZ);
                            Vector3 ballPosNewCmd = new Vector3(newCmd.ballImagePositionX, newCmd.ballImagePositionY, newCmd.ballImagePositionZ);

                            if ((ballPosCachedCmd - ballPosNewCmd).magnitude > 0.01f)
                            {
                                return true;
                            }
                            else
                            {
                                return false;
                            }
                        }
                    }
                case PlayerCommandSignature.DragAimCommand:
                    {
                        if (!cacheCommands.ContainsKey(signature)) { return true; }
                        else
                        {
                            DragAimCommand cachedCmd = cacheCommands[signature] as DragAimCommand;
                            DragAimCommand newCmd = command as DragAimCommand;

                            Vector3 aimPosCachedCmd = new Vector3(cachedCmd.aimPointPositionX, cachedCmd.aimPointPositionY, cachedCmd.aimPointPositionZ);
                            Vector3 aimPosNewCmd = new Vector3(newCmd.aimPointPositionX, newCmd.aimPointPositionY, newCmd.aimPointPositionZ);

                            if ((aimPosCachedCmd - aimPosNewCmd).magnitude > 0.01f)
                            {
                                return true;
                            }
                            else
                            {
                                return false;
                            }
                        }
                    }
            }

            return true;
        }

        public void SyncInterval(PlayerCommandSignature signature, object[] data = null)
        {
            if (isWaitingTilNextSync) { return; }

            PlayerCommand command = GeneratePlayerCommand(signature, data);

            if (CheckCacheIfSyncIsNeeded(signature, command) == false) { return; }

            ActionDispatcher.Dispatch(new SyncPlayerAction(command));

            if (cacheCommands.ContainsKey(signature))
            {
                cacheCommands[signature] = command;
            }
            else
            {
                cacheCommands.Add(signature, command);
            }

            isWaitingTilNextSync = true;
            _ = WaitTilNextSync();
        }

        public void Sync(PlayerCommandSignature signature, object[] data = null)
        {
            PlayerCommand command = GeneratePlayerCommand(signature, data);

            ActionDispatcher.Dispatch(new SyncPlayerAction(command));
        }

        private PlayerCommand GeneratePlayerCommand(PlayerCommandSignature signature, object[] data = null)
        {
            switch (signature)
            {
                case PlayerCommandSignature.DragBallCommand:
                    {
                        float dragPercentage = 0f;
                        float angle = 0f;
                        if (data != null)
                        {
                            dragPercentage = (float)data[0];
                            angle = (float)data[1];
                        }
                        return new DragBallCommand(dragPercentage, angle);
                    }
                case PlayerCommandSignature.PuttBallCommand:
                    {
                        Vector3 imagePosition = Vector3.zero;
                        if (data != null)
                        {
                            imagePosition = (Vector3)data[0];
                        }
                        return new PuttBallCommand(imagePosition.x, imagePosition.y, imagePosition.z);
                    }
                case PlayerCommandSignature.DragAimCommand:
                    {
                        Vector3 aimPosition = Vector3.zero;
                        if (data != null)
                        {
                            aimPosition = (Vector3)data[0];
                        }
                        return new DragAimCommand(aimPosition.x, aimPosition.y, aimPosition.z);
                    }
                case PlayerCommandSignature.GoToTopViewCommand:
                    {
                        return new GoToTopViewCommand();
                    }
                case PlayerCommandSignature.GoToRearViewCommand:
                    {
                        return new GoToRearViewCommand();
                    }
                case PlayerCommandSignature.ChangeBallCommand:
                    {
                        string ballId = "";
                        if (data != null)
                        {
                            ballId = (string)data[0];
                        }
                        return new ChangeBallCommand(ballId);
                    }
                default:
                    {
                        return null;
                    }
            }
        }

        private async Task WaitTilNextSync()
        {
            await Task.Delay(waitingTilNextSyncTime);
            isWaitingTilNextSync = false;
        }
    }
}
