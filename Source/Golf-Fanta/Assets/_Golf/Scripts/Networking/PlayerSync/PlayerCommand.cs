using System;
using System.Collections.Generic;
using Newtonsoft.Json;
using Unity.VisualScripting;
using UnityEngine;

namespace _Golf.Scripts.Lobby
{
    [Serializable]
    public class PlayerCommandJsonWrapper
    {
        public int signature;

        public float dragPercentage;
        public float angle;

        public float aimPointPositionX;
        public float aimPointPositionY;
        public float aimPointPositionZ;

        public float ballImagePositionX;
        public float ballImagePositionY;
        public float ballImagePositionZ;

        public string ballId;

        public PlayerCommandJsonWrapper()
        {
            signature = 0;

            dragPercentage = 0f;
            aimPointPositionX = 0f;
            aimPointPositionY = 0f;
            aimPointPositionZ = 0f;
        }

        public PlayerCommand ToPlayerCommand()
        {
            switch ((PlayerCommandSignature)signature)
            {
                case PlayerCommandSignature.DragBallCommand:
                    {
                        DragBallCommand dragBallCommand = new DragBallCommand(dragPercentage, angle);
                        return dragBallCommand;
                    }
                case PlayerCommandSignature.GoToTopViewCommand:
                    {
                        GoToTopViewCommand goToTopViewCommand = new GoToTopViewCommand();
                        return goToTopViewCommand;
                    }
                case PlayerCommandSignature.GoToRearViewCommand:
                    {
                        GoToRearViewCommand goToTopViewCommand = new GoToRearViewCommand();
                        return goToTopViewCommand;
                    }
                case PlayerCommandSignature.DragAimCommand:
                    {
                        DragAimCommand dragAimCommand = new DragAimCommand(aimPointPositionX, aimPointPositionY, aimPointPositionZ);
                        return dragAimCommand;
                    }
                case PlayerCommandSignature.PuttBallCommand:
                    {
                        PuttBallCommand puttBallCommand = new PuttBallCommand(ballImagePositionX, ballImagePositionY, ballImagePositionZ);
                        return puttBallCommand;
                    }
                case PlayerCommandSignature.ChangeBallCommand:
                    {
                        ChangeBallCommand changeBallCommand = new ChangeBallCommand(ballId);
                        return changeBallCommand;
                    }
                default:
                    {
                        return null;
                    }
            }
        }
    }

    [Serializable]
    public abstract class PlayerCommand
    {
        public PlayerCommandSignature signature;

        public abstract void Execute();

        public abstract PlayerCommandSignature GetSignature();

        public abstract PlayerCommandJsonWrapper ToJsonWrapper();
    }

    [Serializable]
    public class DragBallCommand : PlayerCommand
    {
        public float dragPercentage;
        public float angle;

        public DragBallCommand(float _dragPercentage, float _angle)
        {
            signature = PlayerCommandSignature.DragBallCommand;
            dragPercentage = _dragPercentage;
            angle = _angle;
        }

        public override void Execute()
        {

        }

        public override PlayerCommandSignature GetSignature()
        {
            return signature;
        }

        public override PlayerCommandJsonWrapper ToJsonWrapper()
        {
            PlayerCommandJsonWrapper wrap = new PlayerCommandJsonWrapper();
            wrap.signature = (int)signature;
            wrap.dragPercentage = dragPercentage;
            wrap.angle = angle;

            return wrap;
        }
    }

    [Serializable]
    public class PuttBallCommand: PlayerCommand
    {
        public float ballImagePositionX;
        public float ballImagePositionY;
        public float ballImagePositionZ;

        public PuttBallCommand(float _ballImagePositionX, float _ballImagePositionY, float _ballImagePositionZ)
        {
            signature = PlayerCommandSignature.PuttBallCommand;
            
            ballImagePositionX = _ballImagePositionX;
            ballImagePositionY = _ballImagePositionY;
            ballImagePositionZ = _ballImagePositionZ;
        }

        public override void Execute()
        {

        }

        public override PlayerCommandSignature GetSignature()
        {
            return signature;
        }

        public override PlayerCommandJsonWrapper ToJsonWrapper()
        {
            PlayerCommandJsonWrapper wrap = new PlayerCommandJsonWrapper();
            wrap.signature = (int)signature;
            wrap.ballImagePositionX = ballImagePositionX;
            wrap.ballImagePositionY = ballImagePositionY;
            wrap.ballImagePositionZ = ballImagePositionZ;

            return wrap;
        }
    }

    [Serializable]
    public class GoToTopViewCommand : PlayerCommand
    {
        public GoToTopViewCommand()
        {
            signature = PlayerCommandSignature.GoToTopViewCommand;
        }

        public override void Execute()
        {

        }

        public override PlayerCommandSignature GetSignature()
        {
            return signature;
        }
        public override PlayerCommandJsonWrapper ToJsonWrapper()
        {
            PlayerCommandJsonWrapper wrap = new PlayerCommandJsonWrapper();
            wrap.signature = (int)signature;

            return wrap;
        }

    }

    [Serializable]
    public class GoToRearViewCommand : PlayerCommand
    {
        public GoToRearViewCommand()
        {
            signature = PlayerCommandSignature.GoToRearViewCommand;
        }

        public override void Execute()
        {

        }

        public override PlayerCommandSignature GetSignature()
        {
            return signature;
        }

        public override PlayerCommandJsonWrapper ToJsonWrapper()
        {
            PlayerCommandJsonWrapper wrap = new PlayerCommandJsonWrapper();
            wrap.signature = (int)signature;

            return wrap;
        }
    }

    [Serializable]
    public class ChangeBallCommand : PlayerCommand
    {
        public string ballId;

        public ChangeBallCommand(string _ballId)
        {
            signature = PlayerCommandSignature.ChangeBallCommand;
            ballId = _ballId;
        }

        public override void Execute()
        {

        }

        public override PlayerCommandSignature GetSignature()
        {
            return signature;
        }

        public override PlayerCommandJsonWrapper ToJsonWrapper()
        {
            PlayerCommandJsonWrapper wrap = new PlayerCommandJsonWrapper();
            wrap.signature = (int)signature;
            wrap.ballId = ballId;

            return wrap;
        }
    }

    [Serializable]
    public class DragAimCommand : PlayerCommand
    {
        public float aimPointPositionX;
        public float aimPointPositionY;
        public float aimPointPositionZ;

        public DragAimCommand(float _aimPositionX, float _aimPositionY, float _aimPositionZ)
        {
            signature = PlayerCommandSignature.DragAimCommand;

            aimPointPositionX = _aimPositionX;
            aimPointPositionY = _aimPositionY;
            aimPointPositionZ = _aimPositionZ;
        }

        public override void Execute()
        {

        }

        public override PlayerCommandSignature GetSignature()
        {
            return signature;
        }

        public override PlayerCommandJsonWrapper ToJsonWrapper()
        {
            PlayerCommandJsonWrapper wrap = new PlayerCommandJsonWrapper();
            wrap.signature = (int)signature;
            wrap.aimPointPositionX = aimPointPositionX;
            wrap.aimPointPositionY = aimPointPositionY;
            wrap.aimPointPositionZ = aimPointPositionZ;

            return wrap;
        }
    }

    [Serializable]
    public enum PlayerCommandSignature
    {
        DragBallCommand = 0,
        GoToTopViewCommand = 1,
        GoToRearViewCommand = 2,
        DragAimCommand = 3,
        PuttBallCommand = 4,
        ChangeBallCommand = 5,
    }
}
