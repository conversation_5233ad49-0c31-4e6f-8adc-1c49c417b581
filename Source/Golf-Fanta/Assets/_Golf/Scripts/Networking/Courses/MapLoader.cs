using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using _Golf.Scripts.ScriptableObjects;
using _Golf.Scripts.ScriptableObjects.Tours;
using Cysharp.Threading.Tasks;
using GolfGame.API;
using GolfGame.API.Models.Hole;
using JetBrains.Annotations;
using UnityEngine;

namespace _Golf.Scripts.Networking.Courses
{
    public class MapLoader : MonoBehaviour
    {
        public static MapLoader Instance;

        private Dictionary<string, AddressableDownloadOperation> _downloadOperations = new ();

        private void Awake()
        {
            Instance = this;
        }

        public void LoadMap(string guid, Action<float> onDownloadProgress = null)
        {
            SceneLoader.SceneLoader.OnRequestSingleLoadMap(guid, onDownloadProgress);
        }

        public void PreloadMapInBackground(string guid)
        {
            SceneLoader.SceneLoader.Instance.PreloadSceneAsync(guid);
        }
    
        public async UniTask LoadMapAsync(string guid)
        {
            await SceneLoader.SceneLoader.Instance.LoadSceneAsync(guid);
        }

        /// <summary>
        /// Check if any map need download and download them.
        /// <param name="guids">List of guids to download</param>
        /// <param name="onDownloadProgress">Callback every frame while downloading</param>
        /// </summary>
        /// <param name="guids"></param>
        /// <param name="cancellationToken">Token to check if the Operation is canceled</param>
        /// <param name="onDownloadProgress">Action is Invoke during the Downloading state</param>
        public async Task CheckAndDownloadMaps(List<HoleInfo> holeInfos, CancellationToken cancellationToken, Action<float> onDownloadProgress = null)
        {
            if (holeInfos.Count == 0) return;
        
            var operations = new List<AddressableDownloadOperation>();
            var tasks = new List<Task>();
        
            foreach (var holeInfo in holeInfos)
            {
                var operation = CheckAndDownloadMap(holeInfo, cancellationToken);
                operations.Add(operation);
                tasks.Add(operation.Task);
            }

            while (operations.Any(operation => operation.CurrentState is AddressableDownloadOperation.State.Downloading or AddressableDownloadOperation.State.Checking))
            {
                if (tasks.Any(task => task.Status == TaskStatus.Faulted)) return;
                var downloadProgress = operations.Sum(progress => progress.DisplayProgress) / operations.Count;
                onDownloadProgress?.Invoke(downloadProgress);
                Debug.Log($"DOWNLOADING: {downloadProgress}");
                await Task.Yield();
            }
            onDownloadProgress?.Invoke(1f);
        }
    
        public async Task CheckAndDownloadMapsSequentially(List<HoleInfo> holeInfos, CancellationToken cancellationToken, Action<string, float> onDownloadProgress = null)
        {
            if (holeInfos.Count == 0) return;
        
            for (var i = 0; i < holeInfos.Count; i++)
            {
                var index = i;
                var handle = CheckAndDownloadMap(holeInfos[i], cancellationToken, (progress,_) => onDownloadProgress?.Invoke(holeInfos[index].Guid, progress));
                await handle.Task;
            }
        }
        
        public AddressableDownloadOperation CheckAndDownloadMap(HoleInfo holeInfo, CancellationToken cancellationToken, Action<float, string> onDownloadProgress = null, Action onComplete = null)
        {
            var guid = holeInfo.Guid;
            if (_downloadOperations.TryGetValue(guid, out var operation)
                && !operation.NeedRetry
               )
                return operation;
            
            var bucket = GlobalSO.RemoteConfigData.AddressableConfig.BucketsInfo?.HoleBucket;
            operation = new AddressableDownloadOperation(guid, bucket?.Key, onDownloadProgress, null, ()=>_downloadOperations.Remove(guid));
            operation.CheckAndDownloadHole(holeInfo ,cancellationToken);
            _downloadOperations[guid] = operation;
            return operation;
        }
        
        private string GetCdnPath(string url, string guid)
        {
            if (url == null) return null;
        
            var addressableConfig = GlobalSO.RemoteConfigData.AddressableConfig;
            var badge = addressableConfig.CourseBadgeMap[guid];
            url = url.Replace("[badge]", badge);
        
#if UNITY_EDITOR
        var buildTarget = "Android";
#elif UNITY_STANDALONE_OSX
		var buildTarget = "StandAloneOSX";
#elif UNITY_IOS
		var buildTarget = "iOS";
#elif UNITY_ANDROID
		var buildTarget = "Android";
#else 
        var buildTarget = "StandaloneWindows64";
#endif

            return $"{url}/ServerData/{buildTarget}/{guid}/catalog_0.1.json";
        }
    }
}