using System.Collections.Generic;
using System.Linq;
using _Golf.Scripts.Lobby;
using _Golf.Scripts.ScriptableObjects;
using _Golf.Scripts.ScriptableObjects.Tours;
using _Golf.Scripts.ScriptableObjects.HeadToHead;
using UnityEngine;
using _Golf.Scripts.Core;

namespace _Golf.Scripts.Courses
{
    public class FetchCourseHandler : MonoBehaviour
    {
        private HeadToHeadProperties _headToHeadProperties;

        public void Start()
        {
            _headToHeadProperties = GlobalSO.HeadToHeadProperties;

            ProcessH2hEntryConfigs();
            ProcessClosestToPinEntryConfigs();
            ProcessTournamentEntryConfigs();
        }
        private void ProcessH2hEntryConfigs()
        {
            var remoteConfigData = GlobalSO.RemoteConfigData;
            List<H2HTourInfo> h2hTours = remoteConfigData.ToursConfig.GetH2HTourInfos();
            List<CourseInfo> courseInfos = remoteConfigData.ToursConfig.GetCourseInfos();

            List<LobbyProperty> lobbies = new();

            foreach (H2HTourInfo tour in h2hTours)
            {
                CourseInfo course = courseInfos.FirstOrDefault(c => c.Id == tour.GetCourseUuid());

                if (course == null)
                {
                    continue;
                }

                LobbyProperty lobbyProperty = new LobbyProperty
                {
                    GameMode = GameMode.HeadToHead,
                    TourInfo = tour,
                    CourseInfo = course,

                    WinningAmount = tour.GetEntryFee() * 2,
                };

                lobbies.Add(lobbyProperty);
            }

            _headToHeadProperties.SetLobbiesData(lobbies);
        }

        private void ProcessClosestToPinEntryConfigs()
        {
            var remoteConfigData = GlobalSO.RemoteConfigData;
            List<ClosestToPinTourInfo> closestToPinTours = remoteConfigData.ToursConfig.GetClosestToPinTourInfos();

            remoteConfigData.ClosestToPinConfig.closesToPinConditionConfigData.tours = closestToPinTours;
        }

        private void ProcessTournamentEntryConfigs()
        {
            var remoteConfigData = GlobalSO.RemoteConfigData;
            List<TournamentTourInfo> tournamentTours = remoteConfigData.ToursConfig.GetTournamentTourInfos();

            remoteConfigData.TournamentConfig.tournamentTours = tournamentTours;
        }
    }
}