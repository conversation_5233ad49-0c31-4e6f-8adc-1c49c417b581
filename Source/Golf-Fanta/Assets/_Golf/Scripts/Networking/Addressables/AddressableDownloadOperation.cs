using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using _Golf.Scripts.Common.Helper;
using _Golf.Scripts.GlobalManagers;
using _Golf.Scripts.ScriptableObjects;
using _Golf.Scripts.ScriptableObjects.Tours;
using Cysharp.Threading.Tasks;
using GolfGame.API;
using GolfGame.API.Models.Hole;
using UnityEngine;
using UnityEngine.AddressableAssets;
using UnityEngine.Networking;
using UnityEngine.ResourceManagement.AsyncOperations;
using Constant = GolfPhysics.Constant;

public class AddressableDownloadOperation
{
    public enum State
    {
        None,
        Checking,
        Downloading,
        Error,
        Done,
        Canceled
    }

    private const float DownloadSpeedUpdateInterval = 0.5f;
    private readonly string _keyAddress;
    private readonly string _bucketKey;
    private AsyncOperationHandle _downloadingHandle = default;
    private HashSet<CancellationToken?> _cancelTokens = new HashSet<CancellationToken?>();
    private CancellationTokenSource _actualCancelTokenSource;
    private float _actualProgress;
    private float _updateSpeedTimer = 0f;
    private float _updateSpeedDeltaTime = 0f;
    private float _updateSpeedDeltaSize = 0f;
    
    public State CurrentState { get; private set; }
    public Task Task { get; private set; }
    public bool NeedRetry => CurrentState is State.Error or State.Canceled;
    public event Action<float,string> OnDownloading;
    public event Func<Task> BeforeCompleted;
    public event Action OnCompleted;
    public float BundleSizeInMB { get; private set; }
    public float DownloadSpeedInMBPerSec { get; private set; }
    public float DisplayProgress => _actualProgress;

    string _checkString = "CHECKING...";
    string _downloadString = "DOWNLOADING...";
    string _loadString = "LOADING...";

    public AddressableDownloadOperation(string keyAddress, string bucketKey, Action<float, string> onDownloading = null, Func<Task> beforeCompleted = null, Action onCompleted = null)
    {
        _keyAddress = keyAddress;
        _bucketKey = bucketKey;
        CurrentState = State.None;
        OnDownloading += onDownloading;
        BeforeCompleted += beforeCompleted;
        OnCompleted += onCompleted;
    }

    public void SetState(State state)
    {
        switch (state)
        {
            case State.None:
                break;
        
            case State.Checking:
                _actualProgress = 0f;
                OnDownloading?.Invoke(0f, string.Empty);
                break;
        
            case State.Downloading:
                break;

            case State.Done:
                OnDownloading = null;
                OnCompleted = null;
                _cancelTokens.Clear();
                _actualProgress = 1f;
                OnDownloading?.Invoke(1f, string.Empty);
                break;
        
            case State.Canceled:
            case State.Error:
            {
                OnDownloading = null;
                OnCompleted = null;
                _cancelTokens.Clear();
                break;
            }
        
            default: break;
        }
        CurrentState = state;
    }

    public void Cancel(CancellationToken? token)
    {
        //Need all cancelTokens to be remove to actually cancel the Operation
        if (token == null) return;
        
        _cancelTokens.Remove(token);
        if(_cancelTokens.Count > 0)
            return;
    
        _actualCancelTokenSource?.Cancel();
    }

    public void CheckAndDownloadHole(HoleInfo info, CancellationToken? cancelToken = null, bool isNew = false)
    {
        _cancelTokens.Add(cancelToken);
        _actualCancelTokenSource?.Dispose();
        _actualCancelTokenSource = new CancellationTokenSource();
        Task = CheckAndDownloadHoleAsync(info);
    }
    
    public void CheckAndDownloadAssetBundle(CancellationToken? cancelToken = null, string downloadUrl = null, string loadPath = null, string version = null)
    {
        _cancelTokens.Add(cancelToken);
        _actualCancelTokenSource?.Dispose();
        _actualCancelTokenSource = new CancellationTokenSource();
        Task = CheckAndDownloadAssetBundlesAsync(downloadUrl, loadPath, version);
    }
    
    private async Task CheckAndDownloadHoleAsync(HoleInfo holeInfo)
    {
        string zipFilePath = Path.Combine(Application.persistentDataPath, _keyAddress + ".zip");
        string extractPath = Path.Combine(Application.persistentDataPath, _keyAddress);

        _checkString = LocalizationManager.Instance.IsKeyInExistTable("gameplay_checking_asset") ? LocalizationManager.Instance.GetString("gameplay_checking_asset") : _checkString;
        _downloadString = LocalizationManager.Instance.IsKeyInExistTable("gameplay_downloading_asset") ? LocalizationManager.Instance.GetString("gameplay_downloading_asset") : _downloadString;
        _loadString = LocalizationManager.Instance.IsKeyInExistTable("gameplay_loading_asset") ? LocalizationManager.Instance.GetString("gameplay_loading_asset") : _loadString;
        
        var cancelToken = _actualCancelTokenSource.Token;

        OnDownloading?.Invoke(0f, _checkString);
        SetState(State.Checking);
        
        if (cancelToken.IsCancellationRequested) return;
        
        // Checking
        var downloadUrl = "";
        var version = "";
        try
        {
            UnityEngine.Debug.Log("Checking hole version: " + holeInfo.Guid);
            var currentHoleVersion =
                await APIGameClient.Instance.GetHoleVersion(new HoleVersionRequestData(holeInfo.Guid));
            if (BundleVersionManager.Instance.IsVersionCorrect(holeInfo.Guid, currentHoleVersion.data) && Directory.Exists(extractPath))
            {
                var cacheCatalogPath = Path.Combine(extractPath, "catalog_0.1.json");
                var loadHandle = Addressables.LoadContentCatalogAsync(cacheCatalogPath, true);
                loadHandle.Completed += async (handle) =>
                {
                    if (handle.Status == AsyncOperationStatus.Succeeded)
                    {
                        OnDownloading?.Invoke(1f, string.Empty);
                        await UniTask.WaitForSeconds(0.2f);
                        OnCompleted?.Invoke();
                        SetState(State.Done);
                        return;
                    }
                    else
                    {
                        SetState(State.Error);
                    }
                };
                
                while (loadHandle.PercentComplete < 1f && !loadHandle.IsDone)
                {
                    OnDownloading?.Invoke(loadHandle.PercentComplete, _loadString);
                    await Task.Yield();
                }
            }

            var bucket = GlobalSO.RemoteConfigData.AddressableConfig.BucketsInfo?.HoleBucket;
            var url = await APIGameClient.Instance.GetHoleDownloadUrl(new HoleDownloadRequestData(bucket?.Name, holeInfo));
            downloadUrl = url.data;

            version = currentHoleVersion.data;
        }
        catch (Exception e)
        {
            SetState(State.Error);
            return;
        }

        if (string.IsNullOrEmpty(downloadUrl))
        {
            SetState(State.Canceled);
            return;
        }
        
        // Download
        SetState(State.Downloading);
        UnityWebRequest uwr = UnityWebRequest.Get(downloadUrl);
        uwr.downloadHandler = new DownloadHandlerFile(zipFilePath);
        var operation = uwr.SendWebRequest();
        OnDownloading?.Invoke(0f, _downloadString);
        
        while (!operation.isDone)
        {
            UpdateProgress(uwr.downloadProgress);
            OnDownloading?.Invoke(DisplayProgress, string.Empty);
            await Task.Yield();
        }
        
        if (uwr.result != UnityWebRequest.Result.Success)
        {
            Debug.LogError($"Download failed: {uwr.error}");
            SetState(State.Error);
            return;
        }
        
        
        // Extract
        try
        {
            await FileHelper.ExtractFileAsync(zipFilePath, extractPath, _keyAddress, cancelToken);
        }
        catch (Exception e)
        {
            Debug.LogError($"Extraction failed: {e}");
            SetState(State.Error);
        }
        
        var catalogPath = Path.Combine(extractPath, "catalog_0.1.json");
        
        // if (BeforeCompleted != null)
        // {
        //     var tasks = new List<UniTask>();
        //     foreach (Delegate del in BeforeCompleted.GetInvocationList())
        //     {
        //         var funcTask = (Func<UniTask>)del;
        //         tasks.Add(funcTask());
        //     }
        //     await UniTask.WhenAll(tasks);
        // }
        
        // Init Catalog
        try
        {
            await Addressables.LoadContentCatalogAsync(catalogPath, true);
            if(!string.IsNullOrEmpty(version)) BundleVersionManager.Instance.SaveVersion(_keyAddress, version);
            OnCompleted?.Invoke();
            SetState(State.Done);
        }
        catch (Exception e)
        {
            Debug.LogError($"Failed to load catalog: {_keyAddress} - {e}");
            SetState(State.Error);
        }
    }
    
    private async Task CheckAndDownloadAssetBundlesAsync(string downloadUrl, string loadPath, string version)
    {
        string zipFilePath = Path.Combine(Application.persistentDataPath, _keyAddress + ".zip");
        string extractPath = Path.Combine(Application.persistentDataPath, _keyAddress);
        
        var cancelToken = _actualCancelTokenSource.Token;
        SetState(State.Checking);
        
        if (cancelToken.IsCancellationRequested) return;
        
        // Download
        SetState(State.Downloading);
        UnityWebRequest uwr = UnityWebRequest.Get(downloadUrl);
        uwr.downloadHandler = new DownloadHandlerFile(zipFilePath);
        var operation = uwr.SendWebRequest();
        
        while (!operation.isDone)
        {
            UpdateProgress(uwr.downloadProgress);
            OnDownloading?.Invoke(DisplayProgress, string.Empty);
            await Task.Yield();
        }
        
        if (uwr.result != UnityWebRequest.Result.Success)
        {
            Debug.LogError($"Download failed: {uwr.error}");
            SetState(State.Error);
            return;
        }
        
        // Extract
        try
        {
            await FileHelper.ExtractFileAsync(zipFilePath, extractPath, _keyAddress, cancelToken);
        }
        catch (Exception e)
        {
            Debug.LogError($"Extraction failed: {e}");
            SetState(State.Error);
        }
        
        var catalogPath = Path.Combine(extractPath,Constant.TargetPlatform,"catalog_0.1.json");
        
        // if (BeforeCompleted != null)
        // {
        //     var tasks = new List<UniTask>();
        //     foreach (Delegate del in BeforeCompleted.GetInvocationList())
        //     {
        //         var funcTask = (Func<UniTask>)del;
        //         tasks.Add(funcTask());
        //     }
        //     await UniTask.WhenAll(tasks);
        // }
        
        // Init Catalog
        try
        {
            await Addressables.LoadContentCatalogAsync(catalogPath, true);
            if(!string.IsNullOrEmpty(version)) BundleVersionManager.Instance.SaveVersion(_keyAddress, version);
            SetState(State.Done);
            OnCompleted?.Invoke();
        }
        catch (Exception e)
        {
            Debug.LogError($"Failed to load catalog: {_keyAddress} - {e}");
            SetState(State.Error);
        }
    }
    
    private void UpdateProgress(float newActualProgress)
    {
        _updateSpeedTimer -= Time.deltaTime;
        if (_updateSpeedTimer <= 0f)
        {
            _updateSpeedTimer = DownloadSpeedUpdateInterval;
            DownloadSpeedInMBPerSec = _updateSpeedDeltaTime > 0 ? _updateSpeedDeltaSize / _updateSpeedDeltaTime : 0f;
            _updateSpeedDeltaTime = 0f;
            _updateSpeedDeltaSize = 0f;
        }
        var deltaProgress = newActualProgress - _actualProgress;
        _updateSpeedDeltaSize += deltaProgress * BundleSizeInMB;
        _updateSpeedDeltaTime += Time.deltaTime;
        
        _actualProgress = newActualProgress;
    }

    private float RevampProgressSpeed(float actualProgress)
    {
        var displayValue = 0f;
        if (actualProgress < 0.9f)
            displayValue = actualProgress / 9f;
        else
            displayValue = Mathf.Min(0.1f + (actualProgress - 0.9f) * 9, 1f);
        return displayValue;
    }
}