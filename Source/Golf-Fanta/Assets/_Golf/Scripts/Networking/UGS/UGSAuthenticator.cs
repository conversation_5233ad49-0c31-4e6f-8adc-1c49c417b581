using System;
using System.Text;
using System.Threading.Tasks;
using _Golf.Scripts.Common;
using Unity.Services.Authentication;
using Unity.Services.Core;
using UnityEngine;

namespace _Golf.Scripts.Networking
{
    public class UGSAuthenticator : MonoSingleton<UGSAuthenticator>
    {
        public Unity.Services.Authentication.PlayerInfo PlayerInfo => AuthenticationService.Instance.PlayerInfo;

        public async Task<bool> SignIn()
        {
            var signInTask = SignInAnonymouslyAsync();
            await signInTask;
            if (!signInTask.IsCompletedSuccessfully)
            {
                Debug.LogError($"Failed to Signed In: {signInTask.Exception}");
                return false;
            }

            return true;
        }

        private string Decrypt(string text, string key)
        {
            var result = new StringBuilder();
            for (int c = 0; c < text.Length; c += 2)
                result.Append((char)(Convert.ToUInt16(text.Substring(c, 2), 16) ^ (ushort)key[(c / 2) % key.Length]));
            return result.ToString();
        }
        private async Task SignInAnonymouslyAsync()
        {
            try
            {
                await AuthenticationService.Instance.SignInAnonymouslyAsync();
                await AuthenticationService.Instance.GetPlayerInfoAsync();
                Debug.Log("Sign in anonymously succeeded!");
                Debug.Log($"PlayerID: {AuthenticationService.Instance.PlayerId}");
                Debug.Log($"AccessToken: {AuthenticationService.Instance.AccessToken}");
            }
            catch (AuthenticationException ex)
            {
                Debug.LogException(ex);
            }
            catch (RequestFailedException ex)
            {
                Debug.LogException(ex);
            }
        }

        async Task<bool> SignUpWithUsernamePasswordAsync(string username, string password)
        {
            try
            {
                await AuthenticationService.Instance.SignUpWithUsernamePasswordAsync(username, password);
                Debug.Log("SignUp is successful.");
                return true;
            }
            catch (AuthenticationException ex)
            {
                Debug.LogException(ex);
                return false;
            }
            catch (RequestFailedException ex)
            {
                Debug.LogException(ex);
                return false;
            }
        }

        async Task SignInWithUsernamePasswordAsync(string username, string password)
        {
            try
            {
                await AuthenticationService.Instance.SignInWithUsernamePasswordAsync(username, password);
                Debug.Log("SignIn is successful.");
                Debug.Log(AuthenticationService.Instance.AccessToken);
            }
            catch (AuthenticationException ex)
            {
                Debug.LogException(ex);
            }
            catch (RequestFailedException ex)
            {
                Debug.LogException(ex);
            }
        }

        public async Task<bool> AddUsernamePasswordAsync(string username, string password, Action<string> message = null)
        {
            try
            {
                await AuthenticationService.Instance.AddUsernamePasswordAsync(username, password);
                await AuthenticationService.Instance.GetPlayerInfoAsync();
                return true;
            }
            catch (AuthenticationException ex)
            {
                if (ex.ErrorCode == AuthenticationErrorCodes.AccountAlreadyLinked)
                {
                    message?.Invoke("Account already linked");
                }
                else
                {
                    message?.Invoke(ex.Message);
                }

                return false;
            }
            catch (RequestFailedException ex)
            {
                if (ex.ErrorCode == CommonErrorCodes.InvalidRequest)
                {
                    message?.Invoke("Username or password is invalid");
                }
                else
                {
                    message?.Invoke(ex.Message);
                }

                return false;
            }
        }

        public async Task<bool> SignInWithUsernamePasswordAsync(string username, string password, Action<string> message = null)
        {
            try
            {
                if (AuthenticationService.Instance.IsSignedIn)
                {
                    AuthenticationService.Instance.SignOut();
                }
                
                await AuthenticationService.Instance.SignInWithUsernamePasswordAsync(username, password);
                await AuthenticationService.Instance.GetPlayerInfoAsync();
                await AuthenticationService.Instance.GetPlayerNameAsync();

                return true;
            }
            catch (AuthenticationException ex)
            {
                message?.Invoke("Wrong username or password");
                return false;
            }
            catch (RequestFailedException ex)
            {
                if (ex.ErrorCode == CommonErrorCodes.InvalidRequest)
                {
                    message?.Invoke("Username or password is invalid");
                }
                else
                {
                    message?.Invoke(ex.Message);
                }
                await AuthenticationService.Instance.SignInAnonymouslyAsync();
                return false;
            }
        }

        public bool IsSignedIn()
        {
            return AuthenticationService.Instance.IsSignedIn;
        }
    }
}