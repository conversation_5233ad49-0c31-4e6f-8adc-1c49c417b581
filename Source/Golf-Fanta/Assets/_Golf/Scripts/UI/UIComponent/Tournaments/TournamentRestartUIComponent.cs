using System;
using System.Collections.Generic;
using _Golf.Scripts.Common;
using _Golf.Scripts.GlobalManagers;
using _Golf.Scripts.Networking;
using _Golf.Scripts.ScriptableObjects;
using Cysharp.Threading.Tasks;
using ExitGames.Client.Photon.StructWrapping;
using GolfGame;
using GolfPhysics;
using UnityEngine;
using UnityEngine.Localization;
using UnityEngine.UIElements;

namespace _Golf.Scripts.UI
{
    public class TournamentRestartUIComponent : PopupUIComponentBase<TournamentRestartUIComponentMediator>
    {
        private TournamentRestartUIComponentMediator _mediator;
        private int _replayTicket;
        private Label _replayTicketText;
        private Button _acceptButton;

        private TournamentParticipation _tournamentParticipation;
        private Tournament _tournament;
        private Action<float> _progressCallback;

        public override UIComponentMediator CreateMediator()
        {
            _mediator = new TournamentRestartUIComponentMediator(this);
            return _mediator;
        }

        public override void OnInit()
        {
            Button closeButton = _root.Q<Button>("CloseButton");

            closeButton.clicked += () =>
            {
                MasterManager.Instance.HideUIComponent(UIComponentEnum.TournamentRestartComponentUI);
            };

            _replayTicketText = _root.Q<Label>("ReplayTicketText");
            _acceptButton = _root.Q<Button>("AcceptButton");
        }

        public override void BeforeOpen(object[] data = null)
        {
            if (data != null)
            {
                _tournamentParticipation = (TournamentParticipation)data[0];

                if (data.Length > 1)
                    _progressCallback = (Action<float>)data[1];
                else
                    _progressCallback = null;

                _tournament = new Tournament(GlobalSO.RemoteConfigData.TournamentConfig.tournamentTours.Find(t => t.GetUuid() == _tournamentParticipation.TournamentGuid));

                _replayTicket = (int)(_tournamentParticipation.RetryAttempt + UGSEconomy.ReplayTicketBalance);

                UpdateUI();
            }
        }

        private void UpdateUI()
        {
            _replayTicketText.text = _replayTicket.ToString();

            _acceptButton.clickable = new Clickable(() => { });

            if (_replayTicket <= 0)
            {
                _replayTicketText.style.color = Constant.WarningTextColor;

                _acceptButton.clicked += () =>
                {
                    object[] data = new object[2];
                    data[0] = null;
                    data[1] = 3;
                    MasterManager.Instance.OpenUIComponent(UIComponentEnum.FakeIAPComponentUI, data);

                };

                var binding = LocalizationManager.Instance.GetLocalizedString("tournament_buy_replay");
                _acceptButton.Q<Label>("ButtonText").SetBinding("text", binding);
                binding.RefreshString();

                // _acceptButton.Q<VisualElement>("Image").style.backgroundImage = new(ResourcesManager.Instance.GetSpriteFromAtlas("BlankBlueButton"));
                GolfUIToolKit.Utility.SetButtonValue(_acceptButton, null, GolfUIToolKit.Utility.ButtonColor.Blue);
            }
            else
            {
                _replayTicketText.style.color = Constant.DefaultTextColor;

                _acceptButton.clicked += () =>
                {
                    if (TournamentsStageScreenMediator.ClickPlayTournamentEvent.Tournament != null)
                    {
                        return;
                    }

                    var actionRef = TournamentsStageScreenMediator.ClickPlayTournamentEvent;

                    if (_progressCallback != null)
                    {
                        actionRef.Tournament = _tournament;
                        actionRef.OnLoadingProgress = (progressValue,_) => { _progressCallback.Invoke(progressValue); };
                        actionRef.ForceRestart = true;

                        MasterManager.Instance.HideUIComponent(UIComponentEnum.TournamentRestartComponentUI);
                        ActionDispatcher.Dispatch(actionRef);
                    }
                    else
                    {
                        actionRef.Tournament = _tournament;
                        actionRef.OnLoadingProgress = null;
                        actionRef.ForceRestart = true;

                        MasterManager.Instance.HideAllComponents().Forget();
                        ActionDispatcher.Dispatch(actionRef);
                    }
                };

                var binding = LocalizationManager.Instance.GetLocalizedString("common_yes");
                _acceptButton.Q<Label>("ButtonText").SetBinding("text", binding);
                binding.RefreshString();

                GolfUIToolKit.Utility.SetButtonValue(_acceptButton, null, GolfUIToolKit.Utility.ButtonColor.Yellow);
            }

        }

        public void UpdateReplayTicket(int ticket)
        {
            _replayTicket = (int)(_tournamentParticipation.RetryAttempt + UGSEconomy.ReplayTicketBalance);
            UpdateUI();
        }
    }
}

public class PurchaseReplayTicketAction : ActionBase { }

