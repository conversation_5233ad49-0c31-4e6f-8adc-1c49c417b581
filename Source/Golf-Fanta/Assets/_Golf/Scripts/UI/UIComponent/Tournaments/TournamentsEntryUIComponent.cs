using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using _Golf.Scripts.Common;
using _Golf.Scripts.Core;
using _Golf.Scripts.GlobalManagers;
using _Golf.Scripts.Networking;
using _Golf.Scripts.ScriptableObjects;
using _Golf.Scripts.Tracking;
using Cysharp.Threading.Tasks;
using Game.UI;
using GolfGame;
using GolfGame.API;
using GolfPhysics;
using Kamgam.UIToolkitGlow;
using Kamgam.UIToolkitScrollViewPro;
using UnityEngine;
using UnityEngine.Localization;
using UnityEngine.UIElements;

namespace _Golf.Scripts.UI
{
    public class TournamentsEntryUIComponent : UIComponentBase<TournamentsEntryUIComponentMediator>
    {
        private TournamentsEntryUIComponentMediator _mediator;
        private PlayerInfo _playerInfo;

        [SerializeField] private VisualTreeAsset _tournamentBanner;
        [SerializeField] private List<Sprite> _tournamentBannerImages;
        private List<Gradient> _tournamentBannerGradients;

        #region ----General UI----

        UIDocument document;
        VisualElement root;
        Button BackButton;
        private VisualElement _loadingPanel;
        private ScrollViewPro _stageScrollView;

        #endregion

        #region ----Local Var----
        private IEnumerator _countdownCoroutine;
        private List<Tournament> _tournaments;
        private List<Tournament> _activeTournaments;
        private float _cacheScrollViewWidth;
        private float _cacheScrollViewHeight;
        private List<VisualElement> _listStages = new List<VisualElement>();
        private int _currentSnapIndex = -1;
        private int _cachedStageIndex = 0;

        private List<List<LeaderboardPlayerInfo>> _leaderboardData = new List<List<LeaderboardPlayerInfo>>();
        private List<int> _playerCurrentRank = new List<int>();
        private List<TournamentState> _cacheStates = new List<TournamentState>();
        private bool _handleSnapEvent;

        #endregion


        public override UIComponentMediator CreateMediator()
        {
            _mediator = new TournamentsEntryUIComponentMediator(this);
            return _mediator;
        }

        public override void Init()
        {
            _playerInfo = GlobalSO.PlayerInfoSO.Info;
            _tournamentBannerGradients = new List<Gradient>( Constant.BannerGradients);

            document = GetComponent<UIDocument>();
            root = document.rootVisualElement;

            BackButton = root.Q<Button>("BackButton");

            _loadingPanel = root.Q<VisualElement>("LoadingAsset");
            _tournaments = new List<Tournament>();
            _activeTournaments = new List<Tournament>();

            _stageScrollView = root.Q<ScrollViewPro>("StageScrollView");
            _stageScrollView.RegisterCallback<GeometryChangedEvent>(evt =>
            {
                if (_stageScrollView.resolvedStyle.width > 0f)
                {
                    _cacheScrollViewWidth = _stageScrollView.resolvedStyle.width;
                    _cacheScrollViewHeight = _stageScrollView.resolvedStyle.height;
                }
            });

            root.style.display = DisplayStyle.None;
        }

        public override void OnHide(object[] data = null)
        {
            //StopCoroutine(_countdownCoroutine);
            _cachedStageIndex = _currentSnapIndex;
            _currentSnapIndex = -1;
            root.style.display = DisplayStyle.None;
            _stageScrollView.OnScrollToElement -= UpdateUI;

            _stageScrollView.RemoveFromClassList("show");
        }

        public override void OnOpen(object[] data = null)
        {
            root.style.display = DisplayStyle.Flex;
            _loadingPanel.style.display = DisplayStyle.None;
            _handleSnapEvent = false;

            _tournaments.Clear();
            _activeTournaments.Clear();
            foreach (var tournament in GlobalSO.RemoteConfigData.TournamentConfig.tournamentTours)
            {
                _tournaments.Add(new Tournament(tournament));
            }
            _activeTournaments = _tournaments.Where(tournament => tournament.IsEnabled).ToList();

            _stageScrollView.RegisterCallbackOnce<TransitionStartEvent>(evt =>
            {
                SetupTournaments();
                _stageScrollView.schedule.Execute(() =>
                {
                    SnapTo(_cachedStageIndex);
                }).StartingIn(10);
            });

            BackButton.clickable = new(() =>
            {
                TournamentsStageScreenMediator.ClickPlayTournamentEvent.ClearReference();
                MasterManager.Instance.OnChangeScreen(EScreenEnum.GameMode).Forget();
            });

            _stageScrollView.OnScrollToElement += UpdateUI;

            _stageScrollView.AddToClassList("show");
        }

        private void SetupTournaments()
        {
            _stageScrollView.Clear();
            _listStages.Clear();
            _leaderboardData.Clear();
            _playerCurrentRank.Clear();

            for (int i = 0; i < _activeTournaments.Count; i++)
            {
                _leaderboardData.Add(new List<LeaderboardPlayerInfo>());
                _playerCurrentRank.Add(-1);
            }


            _cacheStates = new(_activeTournaments.Count);

            VisualElement leftPadding = new VisualElement();
            leftPadding.style.width = _cacheScrollViewWidth * Constant.ContentWitdhSizeSmoothDrag / 2;
            _stageScrollView.Add(leftPadding);


            for (int i = 0; i < _activeTournaments.Count; i++)
            {
                var tournament = _tournamentBanner.Instantiate();
                tournament.style.width = _cacheScrollViewWidth * Constant.ContentWitdhSizeSmoothDrag;
                tournament.style.height = _cacheScrollViewHeight;
                tournament.style.bottom = 0;
                tournament.style.flexGrow = 0;

                // tournament.AddToClassList("snap-target");

                _stageScrollView.Add(tournament);
                _listStages.Add(tournament);
                int index = i;
                InitTournament(tournament, index);
                SetUpContentUI(index);
            }

            VisualElement internalPadding = new VisualElement();
            internalPadding.style.width = _cacheScrollViewWidth * Constant.ContentWitdhSizeSmoothDrag / 2;
            _stageScrollView.Add(internalPadding);
        }

        private void SnapTo(int index)
        {
            _handleSnapEvent = true;

            float targetPosition = index * _cacheScrollViewWidth * Constant.ContentWitdhSizeSmoothDrag;
            _stageScrollView.horizontalScroller.value = targetPosition;
            var cachedSnap = _stageScrollView.snapDurationSec;
            _stageScrollView.snapDurationSec = 0.01f;

            ExcuteSnap(cachedSnap).Forget();
        }

        private async UniTask ExcuteSnap(float cachedSnapDuration)
        {
            _stageScrollView.Snap();
            await UniTask.DelayFrame(2);
            _stageScrollView.snapDurationSec = cachedSnapDuration;
            UpdateUI(_cachedStageIndex);
        }

        private void SetUpContentUI(int index)
        {
            TournamentParticipation tournamentParticipationData = null;

            var tournament = _activeTournaments[index];

            if (GlobalSO.PlayerSaves.AllTournamentParticipation.ContainsKey(tournament.Guid))
            {
                tournamentParticipationData = GlobalSO.PlayerSaves.AllTournamentParticipation[tournament.Guid];
            }


            var entryFeeText = _listStages[index].Q<Label>("EntryFeeLabel");
            var coin = _listStages[index].Q<VisualElement>("Coin");
            var playButton = _listStages[index].Q<Button>("PlayButton");
            var WhiteBG = _listStages[index].Q<VisualElement>("WhiteBG");
            var progressText = _listStages[index].Q<Label>("ProgressText");

            var scoreButton = _listStages[index].Q<Button>("ScoreButton");

            var state = GetTournamentState(tournament, tournamentParticipationData);

            //set up text at primary button
            float cost = 0;
            switch (state)
            {
                case TournamentState.ExpriedNotPlayed:
                case TournamentState.ExpriedRewardCollected:
                    entryFeeText.style.unityTextAlign = TextAnchor.MiddleCenter;
                    coin.style.display = DisplayStyle.None;

                    var bindingExpried = LocalizationManager.Instance.GetLocalizedString("common_enter");
                    entryFeeText.SetBinding("text", bindingExpried);
                    bindingExpried.RefreshString();

                    GolfUIToolKit.Utility.SetButtonValue(playButton, null, GolfUIToolKit.Utility.ButtonColor.Grey);
                    cost = 0f;

                    if (state == TournamentState.ExpriedNotPlayed)
                    {
                        scoreButton.style.display = DisplayStyle.None;
                    }
                    break;

                case TournamentState.RewardCollected:
                    entryFeeText.style.unityTextAlign = TextAnchor.MiddleCenter;
                    coin.style.display = DisplayStyle.None;

                    var bindingReward = LocalizationManager.Instance.GetLocalizedString("tournament_reward_collected");
                    entryFeeText.SetBinding("text", bindingReward);
                    bindingReward.RefreshString();

                    GolfUIToolKit.Utility.SetButtonValue(playButton, null, GolfUIToolKit.Utility.ButtonColor.Grey);
                    cost = 0f;
                    break;

                case TournamentState.RankNotEnough:

                    var bindingRank = LocalizationManager.Instance.GetLocalizedString("tournament_require");
                    bindingRank.Arguments = new object[] { tournament.RequiredElo };
                    entryFeeText.SetBinding("text", bindingRank);
                    bindingRank.RefreshString();

                    entryFeeText.style.unityTextAlign = TextAnchor.MiddleCenter;
                    coin.style.display = DisplayStyle.None;
                    GolfUIToolKit.Utility.SetButtonValue(playButton, null, GolfUIToolKit.Utility.ButtonColor.Grey);
                    cost = 0f;
                    break;

                case TournamentState.FirstPlay:

                    var bindingFirstPlay = LocalizationManager.Instance.GetLocalizedString("tournament_entry_fee");
                    var fee = 0;
                    if (tournament.FeeByRank.ContainsKey(_playerInfo.H2hRank.Id))
                    {
                        fee = tournament.FeeByRank[_playerInfo.H2hRank.Id];
                    }
                    else
                    {
                        fee = tournament.FeeByRank.Values.Min();
                    }

                    bindingFirstPlay.Arguments = new object[] { fee.ToString() };
                    entryFeeText.SetBinding("text", bindingFirstPlay);
                    bindingFirstPlay.RefreshString();

                    entryFeeText.style.unityTextAlign = TextAnchor.MiddleCenter;
                    coin.style.display = DisplayStyle.Flex;
                    GolfUIToolKit.Utility.SetButtonValue(playButton, null, GolfUIToolKit.Utility.ButtonColor.Yellow);
                    cost = fee;

                    scoreButton.style.display = DisplayStyle.None;
                    break;

                case TournamentState.InProgress:

                    var bindingInProgress = LocalizationManager.Instance.GetLocalizedString("common_continue");
                    entryFeeText.SetBinding("text", bindingInProgress);
                    bindingInProgress.RefreshString();

                    entryFeeText.style.unityTextAlign = TextAnchor.MiddleCenter;
                    coin.style.display = DisplayStyle.None;
                    GolfUIToolKit.Utility.SetButtonValue(playButton, null, GolfUIToolKit.Utility.ButtonColor.Yellow);
                    cost = 0f;
                    break;

                case TournamentState.CanCollectRewards:

                    var bindingCollectRewards = LocalizationManager.Instance.GetLocalizedString("tournament_collect_rewards");
                    entryFeeText.SetBinding("text", bindingCollectRewards);
                    bindingCollectRewards.RefreshString();

                    entryFeeText.style.unityTextAlign = TextAnchor.MiddleCenter;
                    coin.style.display = DisplayStyle.None;
                    GolfUIToolKit.Utility.SetButtonValue(playButton, null, GolfUIToolKit.Utility.ButtonColor.Yellow);
                    cost = 0f;
                    break;

                case TournamentState.ReplayEnoughTicket:

                    var bindingReplay = LocalizationManager.Instance.GetLocalizedString("common_replay");
                    entryFeeText.SetBinding("text", bindingReplay);
                    bindingReplay.RefreshString();

                    entryFeeText.style.unityTextAlign = TextAnchor.MiddleCenter;
                    coin.style.display = DisplayStyle.None;
                    GolfUIToolKit.Utility.SetButtonValue(playButton, null, GolfUIToolKit.Utility.ButtonColor.Yellow);
                    cost = 0f;
                    break;

                case TournamentState.ReplayNotEnoughTicket:

                    var bindingBuyReplay = LocalizationManager.Instance.GetLocalizedString("tournament_buy_replay");
                    entryFeeText.SetBinding("text", bindingBuyReplay);
                    bindingBuyReplay.RefreshString();

                    entryFeeText.style.unityTextAlign = TextAnchor.MiddleCenter;
                    coin.style.display = DisplayStyle.None;
                    GolfUIToolKit.Utility.SetButtonValue(playButton, null, GolfUIToolKit.Utility.ButtonColor.Blue);
                    cost = 0f;
                    break;
            }

            playButton.clickable = new Clickable(() => { });
            if (IsAbleToPlay(state))
            {
                int currentIndex = index;

                playButton.clicked += () =>
                {
                    switch (state)
                    {
                        case TournamentState.CanCollectRewards:
                            CollectReward(tournament.Guid, currentIndex);
                            break;

                        case TournamentState.FirstPlay:
                            if (UGSEconomy.CoinBalance < cost)
                            {
                                var container = new GeneralPopupContainer(
                                    "Notification",
                                    "Unfortunately, you do not have enough Entry Fee.",
                                    "OK",
                                    () => MasterManager.Instance.HideUIComponent(UIComponentEnum.GeneralPopupUIComponent)
                                );

                                MasterManager.Instance.OpenUIComponent(UIComponentEnum.GeneralPopupUIComponent, new object[] { container });
                            }
                            else
                            {
                                if (TournamentsStageScreenMediator.ClickPlayTournamentEvent.Tournament != null)
                                {
                                    return;
                                }

                                var actionTournamentRef = TournamentsStageScreenMediator.ClickPlayTournamentEvent;
                                actionTournamentRef.Tournament = tournament;
                                actionTournamentRef.OnLoadingProgress = (progressValue, message) =>
                                {
                                    SetUpAssetLoading(progressValue, currentIndex, message);
                                };
                                actionTournamentRef.ForceRestart = false;

                                ActionDispatcher.Dispatch(actionTournamentRef);
                            }
                            break;

                        case TournamentState.ReplayNotEnoughTicket:
                            var data = new object[2];
                            data[0] = null;
                            data[1] = 3;
                            MasterManager.Instance.OpenUIComponent(UIComponentEnum.FakeIAPComponentUI, data);
                            break;

                        case TournamentState.ReplayEnoughTicket:
                        case TournamentState.InProgress:
                            if (TournamentsStageScreenMediator.ClickPlayTournamentEvent.Tournament != null)
                            {
                                return;
                            }

                            var actionRef = TournamentsStageScreenMediator.ClickPlayTournamentEvent;
                            actionRef.Tournament = tournament;
                            actionRef.OnLoadingProgress = (progressValue, message) =>
                            {
                                SetUpAssetLoading(progressValue, currentIndex, message);
                            };
                            actionRef.ForceRestart = false;

                            ActionDispatcher.Dispatch(actionRef);
                            break;

                        default:
                            break;
                    }
                };
            }

            //set up info text at white bg
            switch (state)
            {
                case TournamentState.ReplayEnoughTicket:
                case TournamentState.ReplayNotEnoughTicket:
                case TournamentState.CanCollectRewards:
                case TournamentState.ExpriedRewardCollected:
                    var _completeTime = tournamentParticipationData.Attempts.Sum(a => a.IsFinished ? 1 : 0);

                    WhiteBG.style.display = DisplayStyle.Flex;
                    var binding = progressText.GetBinding("text") as LocalizedString;
                    binding.Arguments = new object[] { 1, _completeTime };
                    break;
                case TournamentState.InProgress:
                    var currentHole = tournamentParticipationData.ResumeAttempt?.Scores.Count;
                    WhiteBG.style.display = DisplayStyle.Flex;
                    var bindingInProgress = progressText.GetBinding("text") as LocalizedString;
                    bindingInProgress.Arguments = new object[] { 2, currentHole + 1 };
                    break;
                default:
                    WhiteBG.style.display = DisplayStyle.None;
                    break;
            }

            //set up secondary button
            var buttonContainer = _listStages[index].Q<VisualElement>("ButtonContainer");
            var restartButton = _listStages[index].Q<Button>("RestartButton");
            switch (state)
            {
                case TournamentState.InProgress:
                    buttonContainer.Q<VisualElement>("Padding").style.display = DisplayStyle.Flex;
                    restartButton.style.display = DisplayStyle.Flex;
                    break;
                default:
                    buttonContainer.Q<VisualElement>("Padding").style.display = DisplayStyle.None;
                    restartButton.style.display = DisplayStyle.None;
                    break;
            }
        }

        private void UpdateUI(int index)
        {
            if (index == _currentSnapIndex || !_handleSnapEvent)
            {
                return;
            }

            if (index < 0)
            {
                index = 0;
            }
            else if (index >= _listStages.Count)
            {
                index = _listStages.Count - 1;
            }

            TournamentParticipation tournamentParticipationData = null;

            var tournament = _activeTournaments[index];
            var freeReplay = 0;
            if (GlobalSO.PlayerSaves.AllTournamentParticipation.ContainsKey(tournament.Guid))
            {
                tournamentParticipationData = GlobalSO.PlayerSaves.AllTournamentParticipation[tournament.Guid];

                int retryAttempt = tournamentParticipationData.RetryAttempt;
                freeReplay = retryAttempt;
            }
            else
            {
                root.Q<Label>("ReplayTicketText").style.color = Constant.DefaultTextColor;
                freeReplay = Constant.DefaultFreeReplayTickets;
            }
            var ticket = freeReplay + UGSEconomy.ReplayTicketBalance;

            if (ticket <= 0)
            {
                root.Q<Label>("ReplayTicketText").style.color = Constant.WarningTextColor;
            }
            else
            {
                root.Q<Label>("ReplayTicketText").style.color = Constant.DefaultTextColor;
            }

            root.Q<Label>("ReplayTicketText").text = ticket.ToString();

            //Animate the stage

            if (index == _currentSnapIndex)
            {
                return;
            }

            _currentSnapIndex = index;

            for (int i = 0; i < index; i++)
            {
                var element = _listStages[i];
                element.pickingMode = PickingMode.Ignore;
                var bG = element.Q<VisualElement>("BG");

                bG.AddToClassList("stage-hide-left");
                bG.RemoveFromClassList("stage-hide-right");
                bG.RemoveFromClassList("stage-show");
            }

            for (int i = index + 1; i < _listStages.Count; i++)
            {
                var element = _listStages[i];
                element.pickingMode = PickingMode.Ignore;
                var bG = element.Q<VisualElement>("BG");

                bG.AddToClassList("stage-hide-right");
                bG.RemoveFromClassList("stage-hide-left");
                bG.RemoveFromClassList("stage-show");
            }

            var focusElement = _listStages[index];
            var focusBG = focusElement.Q<VisualElement>("BG");

            focusElement.pickingMode = PickingMode.Position;
            focusBG.RemoveFromClassList("stage-hide-left");
            focusBG.RemoveFromClassList("stage-hide-right");
            focusBG.AddToClassList("stage-show");
        }
        private void InitTournament(VisualElement tournament, int index)
        {
            int colorIndex = 0;
            string[] parts = _activeTournaments[index].BannerAddress.Split('_');
            if (parts.Length > 1)
            {
                UnityEngine.Debug.Log("Banner Address: " + _activeTournaments[index].BannerAddress + " /" + parts[^1]);
                colorIndex = int.Parse(parts[^1]) - 1;
                if (colorIndex < 0)
                {
                    colorIndex = 0;
                }

                //Safety caclutate the color index
                colorIndex %= _tournamentBannerGradients.Count;
            }
            tournament.Q<GolfLinearGradient>("InfoLayer").Colors = _tournamentBannerGradients[colorIndex];
            tournament.Q<VisualElement>("BannerImage").style.backgroundImage = _tournamentBannerImages[colorIndex].texture;

            // Get the color from the gradient's second color key
            Color baseColor = _tournamentBannerGradients[colorIndex].colorKeys[0].color;

            //Blend mode
            Color dedaultColor = baseColor / (1 - 0.9f);
            baseColor = baseColor / (1 - 0.65f);
            // Set up the glow effect 
            var dynamicGlow = tournament.Q<Glow>("DynamicGlow");
            var defaultGlow = tournament.Q<Glow>("Default");
            dynamicGlow.innerColor = baseColor;
            defaultGlow.innerColor = dedaultColor;

            var outerColor = baseColor;
            outerColor.a = 0f;
            dynamicGlow.outerColor = outerColor;

            var outerColorDefault = dedaultColor;
            outerColorDefault.a = 0f;
            defaultGlow.outerColor = outerColorDefault;

            Button scoreButton = tournament.Q<Button>("ScoreButton");
            Button restartButton = tournament.Q<Button>("RestartButton");

            scoreButton.clicked += () =>
            {
                var dataMap = new Dictionary<string, object>()
                {
                    [TournamentsScoreUIComponent.HoleCountKey] = _activeTournaments[index].Course.Holes.Count,
                    [TournamentsScoreUIComponent.HoleParKey] = _activeTournaments[index].Course.Holes.Select(hole => hole.Par).ToList(),
                };
                if (GlobalSO.PlayerSaves.AllTournamentParticipation.ContainsKey(_activeTournaments[index].Guid))
                {
                    var tournamentParticipation = GlobalSO.PlayerSaves.AllTournamentParticipation[_activeTournaments[index].Guid];
                    dataMap.Add(TournamentsScoreUIComponent.ScoresKey, tournamentParticipation.BestAttempt?.Scores);
                    dataMap.Add(TournamentsScoreUIComponent.IsBestScoreKey, true);
                }
                object[] data = { dataMap };
                MasterManager.Instance.OpenUIComponent(UIComponentEnum.TournamentsScoreComponentUI, data);
            };

            TournamentParticipation tournamentParticipationData = null;

            if (GlobalSO.PlayerSaves.AllTournamentParticipation.ContainsKey(_activeTournaments[index].Guid))
            {
                tournamentParticipationData = GlobalSO.PlayerSaves.AllTournamentParticipation[_activeTournaments[index].Guid];
            }

            var localizeText = LocalizationManager.Instance.GetLocalizedString("tournament_number_hole");
            var hole = _activeTournaments[index].Course.Holes.Count.ToString();
            hole = GolfUIToolKit.Utility.AdjustTextSize(hole, 1.5f);
            hole = GolfUIToolKit.Utility.MakeTextColored(hole, Constant.HighlightTextColor);
            var param = new Dictionary<string, string>()
            {
                ["Name"] = _activeTournaments[index].Course.CourseName,
                ["Hole"] = hole
            };
            localizeText.Arguments = new List<object> { param };
            tournament.Q<Label>("HolesText").text = localizeText.GetLocalizedString();
            // localizeText.RefreshString();

            restartButton.clickable = new Clickable(() => { });

            if (tournamentParticipationData != null)
            {
                if (TournamentsStageScreenMediator.ClickPlayTournamentEvent.Tournament != null)
                {
                    return;
                }

                restartButton.clicked += () =>
                {
                    if (TournamentsStageScreenMediator.ClickPlayTournamentEvent.Tournament != null)
                    {
                        return;
                    }

                    MasterManager.Instance.OpenUIComponent(UIComponentEnum.TournamentRestartComponentUI, new object[] { tournamentParticipationData, (Action<float>)(progress => SetUpAssetLoading(progress, index)) }).Forget();
                };
            }

            var state = GetTournamentState(_activeTournaments[index], tournamentParticipationData);
            GetLeaderBoard(index, state).Forget(); //disable for wrong config

            UpdateUI(index);

            var timeText = tournament.Q<Label>("TimeText");

            _countdownCoroutine = Countdown(_activeTournaments[index].EndTime, timeText, index);
            StartCoroutine(_countdownCoroutine);
        }

        private async UniTask GetLeaderBoard(int currentIndex, TournamentState state)
        {
            int index = currentIndex;

            Button leadersButton = _listStages[index].Q<Button>("LeadersButton");
            Button rewardButton = _listStages[index].Q<Button>("RewardButton");

            rewardButton.clickable = new Clickable(() => { });
            leadersButton.clickable = new Clickable(() => { });
            Rank playerRank = _playerInfo.H2hRank;

            if (state == TournamentState.RankNotEnough)
            {
                return;
            }
            else if (GlobalSO.PlayerSaves.AllTournamentParticipation.ContainsKey(_activeTournaments[index].Guid))
            {
                playerRank = GlobalSO.PlayerSaves.AllTournamentParticipation[_activeTournaments[index].Guid].RankAtParticipate;
            }

            APIServerResponse<TournamentLeaderboard> response = null;

            if (IsEnd(state))
            {
                response = await APIGameClient.Instance.GetTournamentArchivedLeaderboard(playerRank.Id, _activeTournaments[index].LeaderboardId);
            }
            else
            {
                response = await APIGameClient.Instance.GetTournamentLeaderboard(playerRank.Id, _activeTournaments[index].LeaderboardId);
            }

            UnityEngine.Debug.Log("Get Leaderboard: " + response.responseCode);

            if (response == null || response.data == null)
            {
                return;
            }

            UnityEngine.Debug.Log("Get Leaderboard: " + response.data.entries.Count);


            var leaderboard = response.data;

            _leaderboardData[index] = new List<LeaderboardPlayerInfo>();

            var results = leaderboard.entries;
            for (int i = 0; i < results.Count; i++)
            {
                var entry = results[i];
                var player = new LeaderboardPlayerInfo(entry);

                _leaderboardData[index].Add(player);
            }

            if (leaderboard.playerRanking != -1)
            {
                _playerCurrentRank[index] = leaderboard.playerRanking;
            }
            else
            {
                _playerCurrentRank[index] = -1;
            }

            leadersButton.clicked += () =>
            {
                MasterManager.Instance.OpenUIComponent(UIComponentEnum.TournamentsLeadersComponentUI, new object[] { _activeTournaments[index].LeaderboardId, playerRank, _leaderboardData[index], _playerCurrentRank[index] });
            };

            rewardButton.clicked += () =>
            {
                MasterManager.Instance.OpenUIComponent(UIComponentEnum.TournamentsRewardComponentUI, new object[] { _activeTournaments[index].Guid, playerRank.Id, _playerCurrentRank[index] });
            };
        }

        bool IsAbleToPlay(TournamentState state)
        {
            return state switch
            {
                TournamentState.FirstPlay => true,
                TournamentState.InProgress => true,
                TournamentState.CanCollectRewards => true,
                TournamentState.ReplayEnoughTicket => true,
                TournamentState.ReplayNotEnoughTicket => true,
                _ => false,
            };
        }

        internal void UpdateGold(long balance)
        {
            var goldText = root.Q<Label>("GoldAmount");
            goldText.text = balance.ToString();
        }

        internal void UpdateGem(long balance)
        {
            var gemText = root.Q<Label>("GemAmount");
            gemText.text = balance.ToString();
        }

        private IEnumerator Countdown(DateTime endTime, Label label, int index)
        {
            if (endTime <= DateTime.UtcNow)
            {
                label.text = "0H 0M 0S";
                yield break;
            }

            var timeLeft = endTime.Subtract(DateTime.UtcNow);

            while (timeLeft.TotalSeconds > 0)
            {
                if (timeLeft.TotalDays > 1)
                    label.text = $"{timeLeft.Days}D {timeLeft.Hours}H {timeLeft.Minutes}M {timeLeft.Seconds}S";
                else
                    label.text = $"{timeLeft.Hours}H {timeLeft.Minutes}M {timeLeft.Seconds}S";

                yield return new WaitForSeconds(1);
                timeLeft = endTime - DateTime.UtcNow;
            }

            UpdateUI(index);
        }

        private void GetTimeFormat(double totalSecond, out long remainMinutes, out long hours)
        {
            long totalMinutes = (long)(totalSecond / 60);
            hours = totalMinutes / 60;
            remainMinutes = totalMinutes % 60;
        }

        public void SetUpAssetLoading(float loadProgress, int index, string message = "LOADING ASSET...")
        {
            var loadingPanel = _listStages[index].Q<VisualElement>("LoadingAsset");
            var buttonContainer = _listStages[index].Q<VisualElement>("ButtonContainer");
            var loadingText = loadingPanel.Q<Label>("LoadingText");
            var loadingBarUXML = loadingPanel.Q<LoadingBarUXML>();

            if (loadProgress < 0f)
            {
                loadingPanel.style.display = DisplayStyle.None;
                buttonContainer.style.display = DisplayStyle.Flex;
                return;
            }

            buttonContainer.style.display = DisplayStyle.None;
            loadingPanel.style.display = DisplayStyle.Flex;

            loadingBarUXML.SetValue(loadProgress);
            if (!string.IsNullOrEmpty(message))
            {
                loadingText.text = message;
            }
            
            UnityEngine.Debug.Log("Loading: " + loadProgress * 100 + "%");
        }

        public void RefreshUI()
        {
            for (int i = 0; i < _listStages.Count; i++)
            {
                SetUpContentUI(i);
            }

            UpdateUI(_currentSnapIndex);
        }

        private TournamentState GetTournamentState(Tournament tournament, TournamentParticipation tournamentParticipation)
        {
            var timeLeft = tournament.EndTime - DateTime.UtcNow;

            if (timeLeft.TotalSeconds <= 0)
            {
                if (tournamentParticipation == null)
                {
                    return TournamentState.ExpriedNotPlayed;
                }
                else
                {
                    if (tournamentParticipation.IsRewardCollected)
                    {
                        return TournamentState.RewardCollected;
                    }
                    else if (tournamentParticipation.QualifiesForReward && (timeLeft.TotalSeconds + GlobalSO.RemoteConfigData.TournamentConfig.receiveRewardDurationSecond) > 0)
                    {
                        return TournamentState.CanCollectRewards;
                    }
                    else
                    {
                        return TournamentState.ExpriedRewardCollected;
                    }
                }
            }

            var playerRank = _playerInfo.H2hRank;

            if (tournamentParticipation == null)
            {
                if (playerRank.Elo < tournament.RequiredElo)
                {
                    return TournamentState.RankNotEnough;
                }
                else
                {
                    return TournamentState.FirstPlay;
                }
            }

            if (tournamentParticipation.HasUnfinishedGame)
            {
                return TournamentState.InProgress;
            }

            if (tournamentParticipation.RetryAttempt + UGSEconomy.ReplayTicketBalance > 0)
            {
                return TournamentState.ReplayEnoughTicket;
            }
            else
            {
                return TournamentState.ReplayNotEnoughTicket;
            }
        }

        private bool IsEnd(TournamentState state)
        {
            return state == TournamentState.ExpriedNotPlayed || state == TournamentState.ExpriedRewardCollected || state == TournamentState.CanCollectRewards || state == TournamentState.RewardCollected;
        }

        private async void CollectReward(string guid, int index)
        {
            MasterManager.Instance.OpenUIComponent(UIComponentEnum.LoadingComponentUI);

            //cancel if wait too long
            CancellationTokenSource cts = new CancellationTokenSource();
            cts.CancelAfter(5000);
            try
            {
                await UniTask.WaitUntil(() => _playerCurrentRank[index] != -1, cancellationToken: cts.Token);
            }
            catch (OperationCanceledException)
            {
                MasterManager.Instance.HideUIComponent(UIComponentEnum.LoadingComponentUI);

                var data = new GeneralPopupContainer(
                    title: "Data Sync Issue",
                    description: "We encountered a problem with your data. Please try again",
                    continueButtonText: "OK",
                    continueAction: () => { MasterManager.Instance.HideUIComponent(UIComponentEnum.GeneralPopupUIComponent); }
                );
                MasterManager.Instance.OpenUIComponent(UIComponentEnum.GeneralPopupUIComponent, new object[] { data });
                cts.Dispose();
                return;
            }

            var getRewardResponse = await APIGameClient.Instance.GetTournamentReward(guid);
            ActionDispatcher.Dispatch(new GetPlayerCurrenciesAction());
            ActionDispatcher.Dispatch(new FetchInventoryAction());
            GlobalSO.PlayerSaves.UpdateParticipationData(getRewardResponse.data.updatedParticipation);

            MasterManager.Instance.HideUIComponent(UIComponentEnum.LoadingComponentUI);
            if (getRewardResponse.responseCode == APIResponseCode.Success)
            {
                //Show collect reward UI
                var reward = getRewardResponse.data.ConvertToBagRewardResponse();
                object[] data = { reward, _playerCurrentRank[index] };
                TrackingManager.Instance.TrackBagReward(guid, reward);
                MasterManager.Instance.OpenUIComponent(UIComponentEnum.RewardResultComponentUI, data);

            }
            else
            {
                //Show popup "We encountered a problem with your data. Please try again later."
                var data = new GeneralPopupContainer(
                    title: "Data Sync Issue",
                    description: "We encountered a problem with your data. Please try again",
                    continueButtonText: "OK",
                    continueAction: () => { MasterManager.Instance.HideUIComponent(UIComponentEnum.GeneralPopupUIComponent); }
                );
                MasterManager.Instance.OpenUIComponent(UIComponentEnum.GeneralPopupUIComponent, new object[] { data });
            }
        }

        public void DisableBackButton()
        {
            BackButton.clickable = null;
        }
    }

    internal enum TournamentState
    {
        ExpriedNotPlayed,
        RewardCollected,
        ExpriedRewardCollected,
        RankNotEnough,
        FirstPlay,
        InProgress,
        CanCollectRewards,
        ReplayEnoughTicket,
        ReplayNotEnoughTicket,
    }
}