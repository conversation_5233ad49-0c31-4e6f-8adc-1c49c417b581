using _Golf.Scripts.Common;
using _Golf.Scripts.Core;
using _Golf.Scripts.ScriptableObjects;
using _Golf.Scripts.UI.GameplayUI;
using GolfGame;
using GolfGame.UIMediators.Base;
using System;
using System.Collections;
using System.Collections.Generic;
using TinyMessenger;
using UnityEngine;

namespace _Golf.Scripts.UI
{
    public class GameplayEffectsUIComponentMediator : UIComponentMediator
    {
        GameplayEffectsUIComponent gameplayEffectsUIComponent;

        private TinyMessageSubscriptionToken _previewCourseToken;
        private TinyMessageSubscriptionToken _previewCourseSetUpToken;
        private TinyMessageSubscriptionToken _previewCourseFinishedToken;
        private TinyMessageSubscriptionToken _onGolfShotRatedToken;
        private TinyMessageSubscriptionToken _ballLandedToken;
        private TinyMessageSubscriptionToken _onTieBreak;
        private TinyMessageSubscriptionToken _changeCameraMode;
        private TinyMessageSubscriptionToken _continueShotAction;
        private TinyMessageSubscriptionToken _strokeTimerUp;
        private TinyMessageSubscriptionToken _cameraOrbitHoleToken;
        private TinyMessageSubscriptionToken _orbitHolePhase2Token;
        private TinyMessageSubscriptionToken onCameraOrbitHoleFinishedToken;
        private TinyMessageSubscriptionToken _onSpectateButtonClick;
        private TinyMessageSubscriptionToken _finishGamePlay;
        private TinyMessageSubscriptionToken _onWarningTimeUp;


        private List<Ball> ballList;
        private int LocalPlayerIndex => ballList.IndexOf(ballList.Find(ball => ball.IsLocalBall()));
        private Ball LocalBall => ballList[LocalPlayerIndex];

        private TinyMessageSubscriptionToken _tournamentAnouncementToken;
        private TinyMessageSubscriptionToken _tournamentShotAnnouncementToken;

        public GameplayEffectsUIComponentMediator(UIComponentBase uIComponent) : base(uIComponent)
        {
            gameplayEffectsUIComponent = (GameplayEffectsUIComponent)_uiComponent;
        }

        public override void InitDependency(Dictionary<UIComponentEnum, UIComponentMediator> uiComponentMediators, Dictionary<EScreenEnum, UIScreenMediator> uiScreenMediators)
        {

        }

        public override void ComponentInitReference()
        {
            
        }

        public override void ComponentInitListener()
        {
            _previewCourseSetUpToken = ActionDispatcher.Bind<PreviewCourseInitToken>(OnPreviewCourseInit);
            _previewCourseToken = ActionDispatcher.Bind<PreviewCourseToken>(OnPreviewCourseStart);
            _previewCourseFinishedToken = ActionDispatcher.Bind<PreviewCourseFinishedToken>(OnPreviewCourseFinished);
            _onGolfShotRatedToken = ActionDispatcher.Bind<OnGolfShotRated>(GolfShotRated);
            _ballLandedToken = ActionDispatcher.Bind<BallLandedAction>(BallLanded);
            _onTieBreak = ActionDispatcher.Bind<HeadToHeadTieBreakAction>(OnTieBreak);
            _changeCameraMode = ActionDispatcher.Bind<ChangeCameraModeAction>(OnCameraChangeMode);
            _continueShotAction = ActionDispatcher.Bind<MoveCameraForNextShotAction>(OnContinueShot);
            _strokeTimerUp = ActionDispatcher.Bind<StrokeTimerTimeUp>(OnStrokeTimerUp);
            _cameraOrbitHoleToken = ActionDispatcher.Bind<CameraOrbitHoleToken>(OnCameraOrbitHole);
            _orbitHolePhase2Token = ActionDispatcher.Bind<CameraOrbitHolePhase2StartToken>(OnOrbitHolePhase2);
            onCameraOrbitHoleFinishedToken = ActionDispatcher.Bind<CameraOrbitHoleFinishedToken>(CameraOrbitHoleFinished);
            _onSpectateButtonClick = ActionDispatcher.Bind<OnChangeBallSpectateToken>(OnSpectateClick);
            

            _tournamentAnouncementToken = ActionDispatcher.Bind<TournamentAnouncementAction>(OnTournamentAnouncement);
            _tournamentShotAnnouncementToken = ActionDispatcher.Bind<ShowTieBreakPointAction>(OnTournamentShotAnouncement);
            _finishGamePlay = ActionDispatcher.Bind<OnFinishGameAction>(OnFinishGamePlay);
            _onWarningTimeUp = ActionDispatcher.Bind<WarningShotAction>(OnWarningTimeUp);
        }

        private void OnWarningTimeUp(WarningShotAction action)
        {
            var isOn = action.TurnOn;

            if (isOn)
            {
                gameplayEffectsUIComponent.ShowWarningText(GlobalSO.RemoteConfigData.CommonSettings.TimerWarningThreshold);
            }
            else
            {
                gameplayEffectsUIComponent.StopWarningText();
            }
        }

        private void OnFinishGamePlay(OnFinishGameAction _)
        {
            gameplayEffectsUIComponent.SetSpectate(true);
            gameplayEffectsUIComponent.OnFinish();
        }
        
        private void OnCameraOrbitHole(CameraOrbitHoleToken token)
        {
            gameplayEffectsUIComponent.TurnOnBlackFilter(0.8f);
        }

        private void CameraOrbitHoleFinished(CameraOrbitHoleFinishedToken token)
        {
            gameplayEffectsUIComponent.TurnOffBlackFilter();
        }

        private void OnSpectateClick(OnChangeBallSpectateToken token)
        {
            if (MasterManager.Instance.CurrentBallInCamera == LocalPlayerIndex)
            {
                // back to spectate local
                gameplayEffectsUIComponent.SetSpectate(isLocal: true);
            }
            else
            {
                // go to spectate opponent
                gameplayEffectsUIComponent.SetSpectate(isLocal: false);
            }            
        }

        private void OnTournamentShotAnouncement(ShowTieBreakPointAction action)
        {
            gameplayEffectsUIComponent.ShowTieBreakPoint(action.TieBreakPoint);
        }

        private void OnTournamentAnouncement(TournamentAnouncementAction action)
        {
            gameplayEffectsUIComponent.ShowCustomAnnoucementText(action.Message);
        }

        private void OnOrbitHolePhase2(CameraOrbitHolePhase2StartToken token)
        {
            gameplayEffectsUIComponent.SetGolfShotPar();
        }

        private void OnPreviewCourseInit(PreviewCourseInitToken obj)
        {
            gameplayEffectsUIComponent.ShowCoursePreview(obj.CourseName, obj.Par);
        }

        //

        public override void ComponentDisposeReference()
        {
            
        }

        public override void ComponentDisposeListener()
        {
            ActionDispatcher.Unbind(_previewCourseToken);
            ActionDispatcher.Unbind(_previewCourseFinishedToken);
            ActionDispatcher.Unbind(_onGolfShotRatedToken);
            ActionDispatcher.Unbind(_ballLandedToken);
            ActionDispatcher.Unbind(_onTieBreak);
            ActionDispatcher.Unbind(_changeCameraMode);
            ActionDispatcher.Unbind(_continueShotAction);
            ActionDispatcher.Unbind(_previewCourseSetUpToken);
            ActionDispatcher.Unbind(_strokeTimerUp);
            ActionDispatcher.Unbind(_cameraOrbitHoleToken);
            ActionDispatcher.Unbind(_orbitHolePhase2Token);
            ActionDispatcher.Unbind(onCameraOrbitHoleFinishedToken);
            ActionDispatcher.Unbind(_onSpectateButtonClick);

            ActionDispatcher.Unbind(_tournamentAnouncementToken);
            ActionDispatcher.Unbind(_tournamentShotAnnouncementToken);
            ActionDispatcher.Unbind(_finishGamePlay);
            ActionDispatcher.Unbind(_onWarningTimeUp);
        }

        public override ModuleManager ComponentGetManager()
        {
            throw new System.NotImplementedException();
        }

        public override ModuleProxy ComponentGetProxy()
        {
            throw new System.NotImplementedException();
        }

        private void GolfShotRated(OnGolfShotRated rated)
        {
            gameplayEffectsUIComponent.StopWarningText();
            gameplayEffectsUIComponent.SetGolfShotResult(rated.result);
        }

        private void OnTieBreak(HeadToHeadTieBreakAction action)
        {
            gameplayEffectsUIComponent.OnTieBreak();
        }

        public void SetBall(List<Ball> balls)
        {
            ballList = balls;
            gameplayEffectsUIComponent.SetBall(ballList);
        }

        public void SetHolePosition(GameObject hole)
        {
            gameplayEffectsUIComponent.SetHolePosition(hole);
        }

        private void OnPreviewCourseStart(PreviewCourseToken token)
        {
            
            
        }

        private void OnPreviewCourseFinished(PreviewCourseFinishedToken token)
        {
            gameplayEffectsUIComponent.HideCoursePreview();
            gameplayEffectsUIComponent.BlackFadeOut();
        }

        private void OnCameraChangeMode(ChangeCameraModeAction action)
        {
            int index = action.id;
            if (index == 1)
            {
                // bird eye
                gameplayEffectsUIComponent.BlackFadeOut();
            }
        }

        private void OnStrokeTimerUp(StrokeTimerTimeUp up)
        {
            // gameplayEffectsUIComponent.SetGolfShotText("OUT OF TIME", "");
            gameplayEffectsUIComponent.SetTimeOutText();

        }

        private void OnContinueShot(MoveCameraForNextShotAction action)
        {
            if (action.ball.localPlayer.GetId() != ballList[MasterManager.Instance.CurrentBallInCamera].localPlayer.GetId())
            {
                return;
            }

            gameplayEffectsUIComponent.OnContinueShot(action.ballTrajectory, action.ball);
        }

        private void BallLanded(BallLandedAction action)
        {
            if (action.ball.localPlayer.GetId() != ballList[MasterManager.Instance.CurrentBallInCamera].localPlayer.GetId())
            {
                return;
            }

            if (action.ball.localPlayer.GetId()
                == GlobalSO.GameplayBus.localPlayer.GetId())
            {
                gameplayEffectsUIComponent.OnBallLanded(action.ballTrajectory, action.ball);
            }
        }

        public void OnFinish()
        {
            gameplayEffectsUIComponent.OnFinish();
        }
    }
}
