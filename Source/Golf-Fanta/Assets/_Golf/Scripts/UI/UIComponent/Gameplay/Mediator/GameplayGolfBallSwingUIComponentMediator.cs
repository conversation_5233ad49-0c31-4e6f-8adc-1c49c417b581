using _Golf.Physics.Data;
using _Golf.Scripts.Common;
using GolfGame;
using GolfGame.UIMediators.Base;
using GolfPhysics;
using GolfPhysics.UnitySurface;
using System.Collections.Generic;
using _Golf.Scripts.ScriptableObjects;
using TinyMessenger;
using UnityEngine;
using UnityEngine.Rendering.Universal;
using System;
using _Golf.Scripts.Lobby;

namespace _Golf.Scripts.UI
{
    public class GameplayGolfBallSwingUIComponentMediator : UIComponentMediator
    {
        GameplayGolfBallSwingUIComponent gameplayGolfBallSwingUIComponent;

        private GolfBallSwingDragUI golfBallSwingDragUI;
        private GolfBallSwingDragUIVersion2 golfBallSwingDragUIVersion2;
        private GolfBallSwingDragPuttingUI golfBallSwingDragPuttingUI;
        private LineRenderer predictionLine;

        private List<Ball> ballList;
        private int LocalPlayerIndex => ballList.IndexOf(ballList.Find(ball => ball.IsLocalBall()));
        private Ball LocalBall => ballList[LocalPlayerIndex];

        private GameObject hole;
        private DecalProjector pin;
        private GolfBallSwingUIProxy proxy;
        private GolfBallSwingUIManager manager;

        private TinyMessageSubscriptionToken _gameStartToken;
        private TinyMessageSubscriptionToken _pinChangeToken;
        private TinyMessageSubscriptionToken _ctpEnableToken;
        private TinyMessageSubscriptionToken _tournamentEnableToken;
        private TinyMessageSubscriptionToken _rearViewCameraTransistionFinishToken;
        private TinyMessageSubscriptionToken _continueShotAction;

        private TinyMessageSubscriptionToken _strokeTimerUp;
        private TinyMessageSubscriptionToken _onSpectateButtonClick;
        private TinyMessageSubscriptionToken _onReceiveOpponentCommand;

        private TinyMessageSubscriptionToken _opponentHitBallPrepToken;

        private TinyMessageSubscriptionToken _onBallChangedToken;
        private TinyMessageSubscriptionToken _onClubChangedToken;

        private TinyMessageSubscriptionToken _sideSpinChangedToken;
        private TinyMessageSubscriptionToken _backSpinChangedToken;

        private int dragVersion;

        public GameplayGolfBallSwingUIComponentMediator(UIComponentBase uIComponent) : base(uIComponent)
        {
            gameplayGolfBallSwingUIComponent = (GameplayGolfBallSwingUIComponent)_uiComponent;
        }

        public override void InitDependency(Dictionary<UIComponentEnum, UIComponentMediator> uiComponentMediators, Dictionary<EScreenEnum, UIScreenMediator> uiScreenMediators)
        {

        }

        public override void ComponentInitReference()
        {
            proxy = new GolfBallSwingUIProxy();
            manager = new GolfBallSwingUIManager(); manager.Init(this);

            gameplayGolfBallSwingUIComponent.InitRef();

            golfBallSwingDragUI = gameplayGolfBallSwingUIComponent.GetDragUI();
            golfBallSwingDragUIVersion2 = gameplayGolfBallSwingUIComponent.GetDragUIVersion2();
            golfBallSwingDragPuttingUI = gameplayGolfBallSwingUIComponent.GetGolfBallSwingDragPuttingUI();

            dragVersion = (int)gameplayGolfBallSwingUIComponent.GetVersion();
        }

        public override void ComponentInitListener()
        {
            _gameStartToken = ActionDispatcher.Bind<ReadyToPlayAction>(OnGameStart);
            _pinChangeToken = ActionDispatcher.Bind<PinChangeToken>(OnPinChange);
            _ctpEnableToken = ActionDispatcher.Bind<ClosestToPinEnableAction>(OnCtpEnable);
            _tournamentEnableToken = ActionDispatcher.Bind<TournamentEnableAction>(OnTournamentEnable);
            _rearViewCameraTransistionFinishToken = ActionDispatcher.Bind<CameraRearViewTransistionFinishAction>(ActivateHitUI);
            _continueShotAction = ActionDispatcher.Bind<ContinueShotAction>(ContinueActivateHitUI);
            _strokeTimerUp = ActionDispatcher.Bind<StrokeTimerTimeUp>(OnStrokeTimerUp);
            _onSpectateButtonClick = ActionDispatcher.Bind<OnChangeBallSpectateToken>(OnSpectateClick);
            _onReceiveOpponentCommand = ActionDispatcher.Bind<SyncOpponentAction>(OnReceiveOpponentCommand);
            _opponentHitBallPrepToken = ActionDispatcher.Bind<OpponentHitBallPrepAction>(OnOpponentHitBall);
            _onBallChangedToken = ActionDispatcher.Bind<ChangeBallAction>(OnBallChanged);
            _onClubChangedToken = ActionDispatcher.Bind<ChangeClubAction>(OnClubChanged);

            _sideSpinChangedToken = ActionDispatcher.Bind<SideSpinChanged>(OnSideSpinChanged);
            _backSpinChangedToken = ActionDispatcher.Bind<BackSpinChanged>(OnBackSpinChanged);
        }

        public override void ComponentDisposeReference()
        {
            gameplayGolfBallSwingUIComponent.DisposeRef();

            proxy = null;
            manager = null;

            golfBallSwingDragUIVersion2.Dispose();
            golfBallSwingDragPuttingUI.Dispose();
        }

        public override void ComponentDisposeListener()
        {
            ActionDispatcher.Unbind(_gameStartToken);
            ActionDispatcher.Unbind(_pinChangeToken);
            ActionDispatcher.Unbind(_rearViewCameraTransistionFinishToken);
            ActionDispatcher.Unbind(_ctpEnableToken);
            ActionDispatcher.Unbind(_continueShotAction);
            ActionDispatcher.Unbind(_strokeTimerUp);
            ActionDispatcher.Unbind(_tournamentEnableToken);
            ActionDispatcher.Unbind(_onSpectateButtonClick);
            ActionDispatcher.Unbind(_onReceiveOpponentCommand);
            ActionDispatcher.Unbind(_opponentHitBallPrepToken);
            ActionDispatcher.Unbind(_onBallChangedToken);
            ActionDispatcher.Unbind(_onClubChangedToken);
            ActionDispatcher.Unbind(_sideSpinChangedToken);
            ActionDispatcher.Unbind(_backSpinChangedToken);
        }

        private void OnSideSpinChanged(SideSpinChanged sideSpinChanged)
        {
            manager.SetSideSpinInput(sideSpinChanged.SideSpin);
        }

        private void OnBackSpinChanged(BackSpinChanged backSpinChanged)
        {
            manager.SetBackSpinInput(backSpinChanged.BackSpin);
        }

        private void OnReceiveOpponentCommand(SyncOpponentAction action)
        {
            if (MasterManager.Instance.CurrentBallInCamera == LocalPlayerIndex)
            {
                return;
            }

            PlayerCommand opponentCommand = action.playerCommand;

            if (opponentCommand is DragBallCommand)
            {
                // go to rear view if needed
                // then drag
                DragBallCommand opponentDragBallCommand = (DragBallCommand) opponentCommand;

                golfBallSwingDragUIVersion2.ExecuteOpponentDragBall(opponentDragBallCommand.dragPercentage, opponentDragBallCommand.angle);
            }
            else if (opponentCommand is PuttBallCommand)
            {
                // go to rear view if needed
                // then putt
                PuttBallCommand opponentPuttBallCommand = (PuttBallCommand) opponentCommand;

                golfBallSwingDragPuttingUI.ExecuteOpponentPuttBall(new Vector3(opponentPuttBallCommand.ballImagePositionX,
                    opponentPuttBallCommand.ballImagePositionY, opponentPuttBallCommand.ballImagePositionZ));
            }
            else if (opponentCommand is GoToRearViewCommand)
            {
                // go to rear view
            }
            else if (opponentCommand is GoToTopViewCommand)
            {
                // go to top view, stop opponent drag ball
            }

            // else if opponent hits ball, hide all uis, stop drad and putt from opponent if in process
        }

        private void OnOpponentHitBall(OpponentHitBallPrepAction action)
        {
            if (MasterManager.Instance.CurrentBallInCamera == LocalPlayerIndex)
            {
                ActionDispatcher.Dispatch(new OpponentHitBallAction(action.Player, action.Input, action.Ball, false));
            }
            else
            {
                if (IsOpponentPutting(
                    new Vector3((float)action.Input.BallPositionX, (float)action.Input.BallPositionY, (float)action.Input.BallPositionZ)))
                {
                    golfBallSwingDragPuttingUI.StopOpponentDragBall();
                    ActionDispatcher.Dispatch(new OpponentHitBallAction(action.Player, action.Input, action.Ball, false));
                }
                else
                {
                    golfBallSwingDragUIVersion2.WaitTilNeedleFinish(action);
                }
            }
        }

        private void OnClubChanged(ChangeClubAction action)
        {
            if (IsPutting())
            {
                golfBallSwingDragPuttingUI.SetDataForGadgetPickerButton();
            }
            else
            {
                golfBallSwingDragUIVersion2.SetDataForGadgetPickerButton();
            }
        }

        private void OnBallChanged(ChangeBallAction action)
        {
            if (IsPutting())
            {
                golfBallSwingDragPuttingUI.SetDataForGadgetPickerButton();
            }
            else
            {
                golfBallSwingDragUIVersion2.SetDataForGadgetPickerButton();
            }
        }

        private void OnSpectateClick(OnChangeBallSpectateToken token)
        {
            if (MasterManager.Instance.CurrentBallInCamera == LocalPlayerIndex)
            {
                // back to spectate local
                if (IsPutting())
                {
                    golfBallSwingDragPuttingUI.SetActiveDrag(true);
                    golfBallSwingDragPuttingUI.SetSpectateButton(true);
                }
                else
                {
                    golfBallSwingDragUIVersion2.SetActiveDrag(true);
                    golfBallSwingDragUIVersion2.SetActiveForSpectateButton(true);
                }
                golfBallSwingDragPuttingUI.StopOpponentDragBall();
                golfBallSwingDragUIVersion2.StopOpponentDragBall();
            }
            else
            {
                // go to spectate opponent
                if (IsPutting())
                {
                    golfBallSwingDragPuttingUI.SetActiveDrag(false);
                    golfBallSwingDragPuttingUI.SetSpectateButton(true);
                }
                else
                {
                    golfBallSwingDragUIVersion2.SetActiveDrag(false);
                    golfBallSwingDragUIVersion2.SetActiveForSpectateButton(true);
                }
            }
        }

        private void OnTournamentEnable(TournamentEnableAction obj)
        {
            if (!obj.IsEnable) return;
            // ALso Need Get Chosen Tournament, Get First For Testing 
            var needleMultiplier = GlobalSO.TournamentPlaySO.CurrentParticipation.RankAtParticipate.Info.needleSpeed;
            GlobalSO.NeedleProperties.GameModeModifier = needleMultiplier - 1;
        }

        private void OnCtpEnable(ClosestToPinEnableAction obj)
        {
            if (!obj.IsEnable) return;
            var configs = GlobalSO.RemoteConfigData.ClosestToPinConfig;
            var difficult = configs.CurrentDifficult;
            var needleCondition = configs.closesToPinConditionConfigData.difficulties[difficult].needleSpeed;
            var increaseSpeed = needleCondition.increase ? needleCondition.multiplier : 1f;
            GlobalSO.NeedleProperties.GameModeModifier = increaseSpeed - 1;
        }



        // <<<< EXTERNAL INJECTION
        public void SetCanvas(Canvas uiCanvas)
        {
            golfBallSwingDragUI.SetCanvas(uiCanvas);
            golfBallSwingDragUIVersion2.SetCanvas(uiCanvas);
            golfBallSwingDragPuttingUI.SetCanvas(uiCanvas);
        }

        public void SetPredictionLineRenderer(LineRenderer _lineRenderer)
        {
            predictionLine = _lineRenderer;
        }

        public void SetPin(DecalProjector _decalProjector)
        {
            pin = _decalProjector;
        }

        public void SetBall(List<Ball> balls)
        {
            ballList = balls;
        }

        public void SetHolePosition(GameObject _hole)
        {
            hole = _hole;
        }

        public Ball GetBall()
        {
            return LocalBall;
        }

        public Ball GetCurrentInSpectateBall()
        {
            return ballList[MasterManager.Instance.CurrentBallInCamera];
        }

        public GameObject GetHole()
        {
            return hole;
        }

        public LineRenderer GetPredicLine()
        {
            return predictionLine;
        }
        // >>>> EXTERNAL INJECTION

        public void FinalizeInitialization()
        {
            if (dragVersion == 1)
            {
                golfBallSwingDragUI.Init(this);
                golfBallSwingDragUI.SetPredictionLineRenderer(predictionLine);
                golfBallSwingDragUI.SetBall(LocalBall);
                golfBallSwingDragUI.SetPin(pin);
            }
            else if (dragVersion == 2)
            {
                golfBallSwingDragUIVersion2.Init(this);
                golfBallSwingDragPuttingUI.Init(this);
            }
        }

        public void ActivateHitUI(CameraRearViewTransistionFinishAction ctx)
        {
            if (IsPutting())
            {
                golfBallSwingDragPuttingUI.SetActiveDrag(true);
            }
            else
            {
                golfBallSwingDragUIVersion2.SetActiveDrag(true);
            }
            gameplayGolfBallSwingUIComponent.OnCheckCamera();
        }

        private void OnStrokeTimerUp(StrokeTimerTimeUp up)
        {
            gameplayGolfBallSwingUIComponent.SetActiveSpectateButton(false);

            if (IsPutting())
            {
                golfBallSwingDragPuttingUI.ExternalForceStopDragging();
                golfBallSwingDragPuttingUI.SetActiveDrag(false);
            }
            else
            {
                golfBallSwingDragUIVersion2.ExternalForceStopDragging();
                golfBallSwingDragUIVersion2.SetActiveDrag(false);
            }
        }

        public void ContinueActivateHitUI(ContinueShotAction ctx)
        {
            gameplayGolfBallSwingUIComponent.OnContinueHitting();
        }

        private void OnPinChange(PinChangeToken token)
        {
            manager.SetVelocity(token.position, token.velocity, token.overPoweredVelocity, token.launchAngle, token.finalDistance, token.isManual, token.hashedSideAngle);
            if (IsPutting())
            {

            }
            else
            {
                golfBallSwingDragUIVersion2.OnHashedSideAngleChanged();
            }
        }
        
        public override ModuleProxy ComponentGetProxy()
        {
            return proxy;
        }

        public override ModuleManager ComponentGetManager()
        {
            return manager;
        }

        private void OnGameStart(ReadyToPlayAction token)
        {
            gameplayGolfBallSwingUIComponent.StartGameSetup();
        }

        public bool IsPutting()
        {
            ColliderSurface colliderSurface = new ColliderSurface(Constant.TerrainLayerMask);
            return colliderSurface.IsPutting(LocalBall.gameObject.transform.position.ToDouble3());
        }

        public bool IsOpponentPutting(Vector3 initShotPosition)
        {
            //Ball opponentBall = ballList.Find(ball => ball.localPlayer.GetId() != LocalBall.localPlayer.GetId());

            ColliderSurface colliderSurface = new ColliderSurface(Constant.TerrainLayerMask);
            return colliderSurface.IsPutting(initShotPosition.ToDouble3());
        }
    }
}
