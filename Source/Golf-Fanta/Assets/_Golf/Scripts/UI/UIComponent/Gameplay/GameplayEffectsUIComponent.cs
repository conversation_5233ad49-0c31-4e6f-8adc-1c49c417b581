using _Golf.Physics.Data;
using _Golf.Scripts.Common;
using _Golf.Scripts.ScriptableObjects;
using DG.Tweening;
using System.Collections;
using System.Collections.Generic;
using GolfGame;
using GolfPhysics;
using TMPro;
using UnityEngine;
// using UnityEngine.UI;
using _Golf.Scripts.Core;
using System;

using UnityEngine.UIElements;
using Kamgam.UIToolkitParticles;
using _Golf.Scripts.GlobalManagers;
using UnityEngine.Localization;
using Cysharp.Threading.Tasks;

namespace _Golf.Scripts.UI
{
    public class GameplayEffectsUIComponent : UIComponentBase<GameplayEffectsUIComponentMediator>
    {
        GameplayEffectsUIComponentMediator gameplayEffectsUIComponentMediator;

        [SerializeField] private GameObject announcementTextGo;
        [SerializeField] private TMP_Text announcementTextShadow1;
        [SerializeField] private TMP_Text announcementTextShadow2;
        [SerializeField] private TMP_Text announcementText;

        [SerializeField] private GameObject ballLandedTextGo;
        [SerializeField] private TMP_Text ballLandedTerrainTextShadow;
        [SerializeField] private TMP_Text ballLandedTerrainText;
        [SerializeField] private TMP_Text ballLandedDistanceTextShadow;
        [SerializeField] private TMP_Text ballLandedDistanceText;

        [SerializeField] private GameObject tiebreakAnnouncementText;

        [SerializeField] private GameObject coursePreview;
        [SerializeField] private TMP_Text courseNameText;
        [SerializeField] private TMP_Text parText;

        [SerializeField] private GameObject spectateGo;
        [SerializeField] private TMP_Text spectateActionText;
        [SerializeField] private TMP_Text opponentNameText;
        [SerializeField] private TMP_Text opponentNameShadowText;

        // [SerializeField] private UnityEngine.UI.Button skipCoursePreviewButton;

        [SerializeField] private UnityEngine.UI.Image blackFadeOut;

        private List<Ball> ballList;
        private float? _tieBreakPoint;
        private int LocalPlayerIndex => ballList.IndexOf(ballList.Find(ball => ball.IsLocalBall()));
        private Ball LocalBall => ballList[LocalPlayerIndex];
        private bool _isTieBreak;

        //toolkit test
        UIDocument document;
        VisualElement root;
        VisualElement shotDisplayZone;
        VisualElement shotResultDisplayZone;
        VisualElement shotViewDisplayZone;
        VisualElement shotViewResultZone;
        VisualElement coursePreviewZone;
        VisualElement tieBreakZone;
        VisualElement notificationZone;
        VisualElement warningZone;
        float _warningTime;
        Coroutine warningScheduler;
        [SerializeField] ParticleSystemForImage textInnerEffect;
        [SerializeField] ParticleSystemForImage textOuterEffect;
        //

        public override UIComponentMediator CreateMediator()
        {
            gameplayEffectsUIComponentMediator = new GameplayEffectsUIComponentMediator(this);
            return gameplayEffectsUIComponentMediator;
        }

        public override void Init()
        {
            // skipCoursePreviewButton.onClick.AddListener(OnSkipPreview);
            _isTieBreak = false;

            document = GetComponent<UIDocument>();
            root = document.rootVisualElement;
            shotDisplayZone = root.Q<VisualElement>("ShotDisplayZone");
            shotResultDisplayZone = root.Q<VisualElement>("ShotResultDisplayZone");
            shotViewDisplayZone = root.Q<VisualElement>("ShotViewDisplayZone");
            shotViewResultZone = root.Q<VisualElement>("ShotViewResultZone");
            coursePreviewZone = root.Q<VisualElement>("CoursePreviewZone");
            tieBreakZone = root.Q<VisualElement>("TieBreakZone");
            notificationZone = root.Q<VisualElement>("NotificationZone");
            warningZone = root.Q<VisualElement>("WarningZone");

            coursePreviewZone.Q<Button>().clickable = new Clickable(OnSkipPreview);
        }

        public override void OnOpen(object[] data = null)
        {
            root.style.display = DisplayStyle.Flex;

            textInnerEffect.Play();
            textOuterEffect.Play();
        }


        public override void OnHide(object[] data = null)
        {
            root.style.display = DisplayStyle.None;

            textInnerEffect.Stop();
            textOuterEffect.Stop();
        }

        public void SetBall(List<Ball> balls)
        {
            ballList = balls;
        }

        #region Golf Shot Rated & Result
        private IEnumerator showTextCoroutine;
        bool isShowingText;

        public void SetGolfShotPar()
        {
            if (isShowingText)
            {
                StopCoroutine(showTextCoroutine);
            }

            int par = GlobalSO.PlayFieldSO.HoleInfo.Par;
            int strokeCount = ballList[MasterManager.Instance.CurrentBallInCamera].localPlayer.GetStrokeCount();

            // sandbox
            if (GlobalSO.GameplayBus.currentLogic is SinglePlayerNormalLogic)
            {
                SetAnnouncementText("'ELLO 'HERE GOV'NER");
            }

            if (strokeCount == 1)
            {
                var localizedString = LocalizationManager.Instance.GetString("game_play_hole_in_one");
                SetAnnouncementText(localizedString);
            }
            else if (strokeCount == par)
            {
                var localizedString = LocalizationManager.Instance.GetString("game_play_par");
                SetAnnouncementText(localizedString);
            }
            else if (strokeCount == par - 1)
            {
                var localizedString = LocalizationManager.Instance.GetString("game_play_birdie");
                SetAnnouncementText(localizedString);
            }
            else if (strokeCount == par - 2)
            {
                var localizedString = LocalizationManager.Instance.GetString("game_play_eagle");
                SetAnnouncementText(localizedString);
            }
            else if (strokeCount == par - 3)
            {
                var localizedString = LocalizationManager.Instance.GetString("game_play_albatross");
                SetAnnouncementText(localizedString);
            }
            else if (strokeCount == par - 4)
            {
                var localizedString = LocalizationManager.Instance.GetString("game_play_condor");
                SetAnnouncementText(localizedString);
            }
            else if (strokeCount == par + 1)
            {
                var localizedString = LocalizationManager.Instance.GetString("game_play_bogey");
                SetAnnouncementText(localizedString);
            }
            else if (strokeCount == par + 2)
            {
                var localizedString = LocalizationManager.Instance.GetString("game_play_2_bogey");
                SetAnnouncementText(localizedString);
            }
            else if (strokeCount == par + 3)
            {
                var localizedString = LocalizationManager.Instance.GetString("game_play_3_bogey");
                SetAnnouncementText(localizedString);
            }
        }

        public void SetGolfShotResult(ShotRating result)
        {
            if (isShowingText)
            {
                StopCoroutine(showTextCoroutine);
            }

            // sandbox
            if (GlobalSO.GameplayBus.currentLogic is SinglePlayerNormalLogic)
            {
                SetShotText("'ELLO 'HERE GOV'NER");
            }

            if (result == ShotRating.Perfect)
            {
                var localizedString = LocalizationManager.Instance.GetString("game_play_perfect");
                SetShotText(localizedString, true);
            }
            else if (result == ShotRating.Great)
            {
                var localizedString = LocalizationManager.Instance.GetString("game_play_great");
                SetShotText(localizedString);
            }
        }

        private void SetAnnouncementText(string text)
        {
            textInnerEffect.Play();
            textOuterEffect.Play();
            //for particle effect, it must set flex before play or error will occur

            shotResultDisplayZone.Q<Label>("BlurText").text = text;
            shotResultDisplayZone.Q<Label>("ShadowText").text = text;
            shotResultDisplayZone.Q<Label>("DisplayText").text = text;

            shotResultDisplayZone.AddToClassList("show");

            shotResultDisplayZone.RegisterCallbackOnce<TransitionEndEvent>((e) =>
            {
                shotResultDisplayZone.RemoveFromClassList("show");

                shotResultDisplayZone.RegisterCallbackOnce<TransitionStartEvent>((e) =>
                {
                    shotResultDisplayZone.RegisterCallbackOnce<TransitionEndEvent>((e) =>
                    {
                        textInnerEffect.Stop();
                        textOuterEffect.Stop();
                    });
                });
            });
        }

        private async void SetNotificationText(string text)
        {
            await UniTask.WaitForEndOfFrame(); //make sure it not call in init frame

            notificationZone.Q<Label>("ShadowText").text = text;
            notificationZone.Q<Label>("DisplayText").text = text;

            notificationZone.AddToClassList("show");

            notificationZone.RegisterCallbackOnce<TransitionEndEvent>((e) =>
            {
                notificationZone.RemoveFromClassList("show");
            });
        }

        private void SetShotText(string text, bool higlight = false)
        {
            textInnerEffect.Play();
            textOuterEffect.Play();

            shotDisplayZone.Q<Label>("BlurText").text = text;
            shotDisplayZone.Q<Label>("ShadowText").text = text;
            shotDisplayZone.Q<Label>("DisplayText").text = text;

            shotDisplayZone.Q<VisualElement>("PerfectShotBG").style.display = higlight ? DisplayStyle.Flex : DisplayStyle.None;

            shotDisplayZone.AddToClassList("show");
            shotDisplayZone.RegisterCallbackOnce<TransitionEndEvent>((e) =>
            {
                shotDisplayZone.RemoveFromClassList("show");

                shotDisplayZone.RegisterCallbackOnce<TransitionStartEvent>((e) =>
                {
                    shotDisplayZone.RegisterCallbackOnce<TransitionEndEvent>((e) =>
                    {
                        textInnerEffect.Stop();
                        textOuterEffect.Stop();
                    });
                });
            });
        }

        public void ShowCustomAnnoucementText(string text)
        {
            SetNotificationText(text);
        }
        #endregion

        #region Ball Landed Text

        private void SetBallLandedText(string terrainText, string distanceText)
        {
            shotViewDisplayZone.Q<Label>("DisplayText").text = terrainText;
            shotViewDisplayZone.Q<Label>("InnerText").text = terrainText;

            shotViewDisplayZone.Q<Label>("YardText").text = distanceText;
            shotViewDisplayZone.Q<Label>("YardInnerText").text = distanceText;

            shotViewDisplayZone.AddToClassList("show");

            shotViewDisplayZone.RegisterCallbackOnce<TransitionEndEvent>((e) =>
            {
                shotViewDisplayZone.RemoveFromClassList("show");
            });
        }

        private void SetContinnueShotText(string text)
        {
            shotViewResultZone.Q<Label>("DisplayText").text = text;
            shotViewResultZone.Q<Label>("ShadowText").text = text;

            shotViewResultZone.AddToClassList("show");

            shotViewResultZone.RegisterCallbackOnce<TransitionEndEvent>((e) =>
            {
                shotViewResultZone.RemoveFromClassList("show");
            });
        }

        public void SetGolfShotText(string terrainText, string distanceText)
        {
            SetBallLandedText(terrainText, distanceText);
        }

        public void OnBallLanded(BallTrajectory ballTrajectory, Ball ball)
        {
            int strokeCount = ball.localPlayer.GetStrokeCount();
            int par = GlobalSO.PlayFieldSO.HoleInfo.Par;

            var currentLogic = GlobalSO.GameplayBus.currentLogic;

            float distance = (Hole.transform.position - ball.transform.position).magnitude;

            if (_isTieBreak)
            {
                var localizedString = LocalizationManager.Instance.GetString("game_play_tie_break");
                var localizedString2 = LocalizationManager.Instance.GetLocalizedString("game_play_from_hole");

                localizedString2.Arguments = new object[] { ShowUnitText(distance, UnitType.Feet) };

                SetGolfShotText(localizedString, localizedString2.GetLocalizedString());
                return;
            }

            switch (ballTrajectory.RestingSurfaceType)
            {
                case GolfPhysics.UnitySurface.SurfaceType.Water:
                    {
                        // SetGolfShotText("WATER HAZARD", "RESET SHOT");
                        var localizedString = LocalizationManager.Instance.GetString("game_play_water_hazard");
                        var localizedString2 = LocalizationManager.Instance.GetString("game_play_reset_shot");

                        SetGolfShotText(localizedString, localizedString2);
                        break;
                    }
                case GolfPhysics.UnitySurface.SurfaceType.HoleCup:
                    {
                        // SetGolfShotText("IN HOLE", "FINISH");
                        break;
                    }
                case GolfPhysics.UnitySurface.SurfaceType.Others:
                    {
                        // SetGolfShotText("OUT OF BOUND", "RESET SHOT");
                        var localizedString = LocalizationManager.Instance.GetString("game_play_out_of_bound");
                        var localizedString2 = LocalizationManager.Instance.GetString("game_play_reset_shot");

                        SetGolfShotText(localizedString, localizedString2);
                        break;
                    }
                case GolfPhysics.UnitySurface.SurfaceType.Fairway:
                    {
                        if (par - strokeCount < 2)
                        {
                            // SetGolfShotText("ON FAIRWAY\n", ShowUnitText(distance, UnitType.Yard) + " FROM HOLE ");
                            var localizedString = LocalizationManager.Instance.GetString("game_play_on_fairway");
                            var localizedString2 = LocalizationManager.Instance.GetLocalizedString("game_play_from_hole");

                            localizedString2.Arguments = new object[] { ShowUnitText(distance, UnitType.Yard) };
                            SetGolfShotText(localizedString, localizedString2.GetLocalizedString());
                        }
                        else
                        {
                            // SetGolfShotText("FAIRWAY IN REGULATION\n", ShowUnitText(distance, UnitType.Yard) + " FROM HOLE ");
                            var localizedString = LocalizationManager.Instance.GetString("game_play_fairway_in");
                            var localizedString2 = LocalizationManager.Instance.GetLocalizedString("game_play_from_hole");

                            localizedString2.Arguments = new object[] { ShowUnitText(distance, UnitType.Yard) };
                            SetGolfShotText(localizedString, localizedString2.GetLocalizedString());
                        }
                        break;
                    }
                case GolfPhysics.UnitySurface.SurfaceType.Green:
                    {
                        string description = "";

                        if (currentLogic is TournamentLogic && _tieBreakPoint != null)
                        {
                            var localizedString = LocalizationManager.Instance.GetLocalizedString("game_play_tie_break_point");
                            localizedString.Arguments = new object[] { ShowUnitText(distance, UnitType.Feet).ToLower(), _tieBreakPoint };

                            description = localizedString.GetLocalizedString();
                            _tieBreakPoint = null;
                        }
                        else
                        {
                            var localizedString = LocalizationManager.Instance.GetLocalizedString("game_play_from_hole");
                            localizedString.Arguments = new object[] { ShowUnitText(distance, UnitType.Feet) };

                            description = localizedString.GetLocalizedString();
                        }

                        if (par - strokeCount < 2)
                        {
                            var localizedString = LocalizationManager.Instance.GetString("game_play_on_green");
                            SetGolfShotText(localizedString, description);
                        }
                        else
                        {
                            var localizedString = LocalizationManager.Instance.GetString("game_play_green_in");
                            SetGolfShotText(localizedString, description);
                        }
                        break;
                    }
                case GolfPhysics.UnitySurface.SurfaceType.FairwayFringe:
                    {
                        var localizedString = LocalizationManager.Instance.GetString("game_play_on_fairway_fr");
                        var localizedString2 = LocalizationManager.Instance.GetLocalizedString("game_play_from_hole");

                        localizedString2.Arguments = new object[] { ShowUnitText(distance, UnitType.Yard) };
                        SetGolfShotText(localizedString, localizedString2.GetLocalizedString());
                        break;
                    }
                case GolfPhysics.UnitySurface.SurfaceType.GreenFringe:
                    {
                        var localizedString = LocalizationManager.Instance.GetString("game_play_on_green_fr");
                        var localizedString2 = LocalizationManager.Instance.GetLocalizedString("game_play_from_hole");

                        localizedString2.Arguments = new object[] { ShowUnitText(distance, UnitType.Feet) };
                        SetGolfShotText(localizedString, localizedString2.GetLocalizedString());
                        break;
                    }
                case GolfPhysics.UnitySurface.SurfaceType.Bunker:
                    {
                        var localizedString = LocalizationManager.Instance.GetString("game_play_bunker");
                        var localizedString2 = LocalizationManager.Instance.GetLocalizedString("game_play_from_hole");

                        localizedString2.Arguments = new object[] { ShowUnitText(distance, UnitType.Yard) };
                        SetGolfShotText(localizedString, localizedString2.GetLocalizedString());
                        break;
                    }
                default:
                    {
                        var localizedString = LocalizationManager.Instance.GetLocalizedString("game_play_from_hole");
                        localizedString.Arguments = new object[] { ShowUnitText(distance, UnitType.Yard) };

                        SetGolfShotText(localizedString.GetLocalizedString(), "[" + ballTrajectory.RestingSurfaceType.ToString() + "]");
                        break;
                    }
            }
        }
        #endregion

        public void OnContinueShot(BallTrajectory ballTrajectory, Ball ball)
        {
            BlackFadeOut();

            if (_isTieBreak)
            {
                return;
            }

            int strokeCount = ball.localPlayer.GetStrokeCount();
            int par = GlobalSO.PlayFieldSO.HoleInfo.Par;

            if (ball.RestingSurfaceType == GolfPhysics.UnitySurface.SurfaceType.Green)
            {
                var localizedString = GolfUtility.IsOverDefineBogey(par, strokeCount + 1) ? LocalizationManager.Instance.GetLocalizedString("game_play_put_for_over") : LocalizationManager.Instance.GetLocalizedString("game_play_put_for");
                localizedString.Arguments = new object[] { GolfUtility.GetPoint(par, strokeCount + 1).ToString().ToUpper() };

                SetContinnueShotText(localizedString.GetLocalizedString());
            }
            else
            {
                var localizedString = GolfUtility.IsOverDefineBogey(par, strokeCount + 1) ? LocalizationManager.Instance.GetLocalizedString("game_play_hole_out_for_over") : LocalizationManager.Instance.GetLocalizedString("game_play_hole_out_for");
                localizedString.Arguments = new object[] { GolfUtility.GetPoint(par, strokeCount + 1).ToString().ToUpper() };

                SetContinnueShotText(localizedString.GetLocalizedString());
            }
        }

        GameObject Hole;

        public void SetHolePosition(GameObject hole)
        {
            Hole = hole;
        }

        public void OnTieBreak()
        {
            _isTieBreak = true;
            ShowTieBreakerText();
        }

        void ShowTieBreakerText()
        {
            tieBreakZone.AddToClassList("show");

            tieBreakZone.RegisterCallbackOnce<TransitionEndEvent>((e) =>
            {
                tieBreakZone.RemoveFromClassList("show");
            });
        }

        public void ShowWarningText(float duration)
        {
            var timeleftString = LocalizationManager.Instance.GetString("game_play_time_left");
            warningZone.AddToClassList("show");
            warningZone.Q<Label>("DisplayText").text = timeleftString;
            warningZone.Q<Label>("ShadowText").text = timeleftString;

            warningZone.Q<Label>("TimeText").text = duration.ToString();
            warningZone.Q<Label>("ShadowTimeText").text = duration.ToString();

            _warningTime = duration;

            if (warningScheduler != null)
                StopCoroutine(warningScheduler);

            warningScheduler = StartCoroutine(CoutdownWarningText());
        }

        public void StopWarningText()
        {
            warningZone.RemoveFromClassList("show");

            if (warningScheduler != null)
                StopCoroutine(warningScheduler);
            warningScheduler = null;
        }

        IEnumerator CoutdownWarningText()
        {
            while (_warningTime > 0)
            {
                _warningTime--;

                if (_warningTime > 0)
                {
                    warningZone.Q<Label>("TimeText").text = _warningTime.ToString("F0");
                    warningZone.Q<Label>("ShadowTimeText").text = _warningTime.ToString("F0");
                }

                yield return new WaitForSeconds(1f);
            }
        }

        public void SetTimeOutText()
        {
            warningZone.Q<Label>("DisplayText").text = "";
            warningZone.Q<Label>("ShadowText").text = "";

            var timeleftString = LocalizationManager.Instance.GetString("game_play_time_out");
            warningZone.Q<Label>("TimeText").text = timeleftString;
            warningZone.Q<Label>("ShadowTimeText").text = timeleftString;

            StopWarningText();
        }

        bool hasSkippedPreview;

        public void ShowCoursePreview(string courseName, string par)
        {
            string displayText = courseName.Replace("-", "\n");

            hasSkippedPreview = false;
            coursePreviewZone.AddToClassList("show");

            coursePreviewZone.Q<Label>("DisplayText").text = displayText;
            coursePreviewZone.Q<Label>("InnerText").text = displayText;

            coursePreviewZone.Q<Label>("InfoText").text = par;
        }

        public void HideCoursePreview()
        {
            // coursePreview.SetActive(false);
            coursePreviewZone.RemoveFromClassList("show");
        }

        private void OnSkipPreview()
        {
            if (!hasSkippedPreview)
            {
                ActionDispatcher.Dispatch(new PreviewCourseSkipToken());
                hasSkippedPreview = true;
            }
        }

        public void BlackFadeOut()
        {
            blackFadeOut.gameObject.SetActive(true);
            Color temp = blackFadeOut.color;
            temp.a = 1f;
            blackFadeOut.color = temp;
            Reset();
        }

        public void BlackFadeWaitingScene()
        {
            blackFadeOut.gameObject.SetActive(true);
            Color temp = blackFadeOut.color;
            temp.a = 1f;
            blackFadeOut.color = temp;
            blackFadeOut.DOColor(temp, Constant.DelayTimeToChangeStage).OnComplete(() =>
            {
                blackFadeOut.gameObject.SetActive(false);
            });
        }

        private void Reset()
        {
            blackFadeOut.DOFade(0, 0.75f).OnComplete(() => { blackFadeOut.gameObject.SetActive(false); ; }).SetEase(Ease.InOutBack);
        }

        public void TurnOnBlackFilter(float opacity)
        {
            //Color temp = blackFadeOut.color;
            //temp.a = opacity;
            //blackFadeOut.color = temp;

            //blackFadeOut.gameObject.SetActive(true);
        }

        public void TurnOffBlackFilter()
        {
            //blackFadeOut.gameObject.SetActive(false);
        }

        public void SetSpectate(bool isLocal)
        {
            if (isLocal)
            {
                spectateGo.SetActive(false);
            }
            else
            {
                spectateGo.SetActive(true);
                int index = MasterManager.Instance.CurrentBallInCamera;
                Ball opponentBall = ballList[index];

                if (LocalBall.IsBallInHole)
                {
                    spectateActionText.text = "WAITING";
                }
                else
                {
                    spectateActionText.text = "SPECTATING";
                }

                opponentNameText.text = opponentBall.localPlayer.GetDisplayName();
                opponentNameShadowText.text = opponentBall.localPlayer.GetDisplayName();
            }
        }

        public void ShowTieBreakPoint(float tieBreakPoint)
        {
            _tieBreakPoint = tieBreakPoint;
        }

        private string ShowUnitText(float distanceInMeter, UnitType type)
        {
            var value = type switch
            {
                UnitType.Feet => UnitsConverter.MetersToFeet(distanceInMeter),
                UnitType.Yard => UnitsConverter.MetersToYard(distanceInMeter),
                _ => distanceInMeter,
            };
            var unit = UnitsConverter.GetUnit(value, type);
            return $"{value:F2} {unit.ToUpper()}";
        }

        public void OnFinish()
        {
            _isTieBreak = false;
            OnHide();
        }
    }
}
