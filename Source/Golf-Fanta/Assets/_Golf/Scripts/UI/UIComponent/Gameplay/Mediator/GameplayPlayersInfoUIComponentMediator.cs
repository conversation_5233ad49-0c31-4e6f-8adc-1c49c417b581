using _Golf.Scripts.Common;
using _Golf.Scripts.UI.GameplayUI;
using GolfGame;
using GolfGame.UIMediators.Base;
using System.Collections.Generic;
using _Golf.Scripts.Core;
using _Golf.Scripts.ScriptableObjects;
using TinyMessenger;

namespace _Golf.Scripts.UI
{
    public class GameplayPlayersInfoUIComponentMediator : UIComponentMediator
    {
        GameplayPlayersInfoUIComponent gameplayPlayersInfoUIComponent;

        private TinyMessageSubscriptionToken _changeBallAction;
        private TinyMessageSubscriptionToken _changeGearAction;

        private TinyMessageSubscriptionToken _setShotClockToken;
        private TinyMessageSubscriptionToken _onLocalPlayerHitToken;
        private TinyMessageSubscriptionToken _onShotClockTimeOut;
        private TinyMessageSubscriptionToken _onSetWindToken;
        private TinyMessageSubscriptionToken _turnOffCountDown;

        private TinyMessageSubscriptionToken onLocalLobbyPlayerStrokeChangedAction;
        private TinyMessageSubscriptionToken _continueShotAction;
        private TinyMessageSubscriptionToken _onSpectateButtonClick;
        private TinyMessageSubscriptionToken _onTieBreak;
        private TinyMessageSubscriptionToken _onFinishGamePlay;

        private List<Ball> ballList;
        private int LocalPlayerIndex => ballList.IndexOf(ballList.Find(ball => ball.IsLocalBall()));
        private Ball LocalBall => ballList[LocalPlayerIndex];

        public GameplayPlayersInfoUIComponentMediator(UIComponentBase uIComponent) : base(uIComponent)
        {
            gameplayPlayersInfoUIComponent = (GameplayPlayersInfoUIComponent)_uiComponent;
        }

        public override void InitDependency(Dictionary<UIComponentEnum, UIComponentMediator> uiComponentMediators, Dictionary<EScreenEnum, UIScreenMediator> uiScreenMediators)
        {
            
        }

        public override void ComponentInitReference()
        {
            
        }

        public override void ComponentInitListener()
        {
            _changeBallAction = ActionDispatcher.Bind<ChangeBallAction>(OnBallChange);
            _changeGearAction = ActionDispatcher.Bind<ChangeGearAction>(OnGearChange);
            _setShotClockToken = ActionDispatcher.Bind<SetShotClockAction>(SetShotClockText);
            _onLocalPlayerHitToken = ActionDispatcher.Bind<LocalPlayerHitBall>(OnBallHit);
            _onShotClockTimeOut = ActionDispatcher.Bind<ShotClockTimeOut>(OnShotClockTimeOut);
            _onSetWindToken = ActionDispatcher.Bind<SetWindAction>(OnSetWind);
            _turnOffCountDown = ActionDispatcher.Bind<TurnOffCountDownAction>(OnTurnOffCountDown);
            onLocalLobbyPlayerStrokeChangedAction = ActionDispatcher.Bind<LocalLobbyPlayerStrokeCountChangedAction>(OnPlayerStrokeCountChange);
            _continueShotAction = ActionDispatcher.Bind<MoveCameraForNextShotAction>(OnContinueShot);
            _onSpectateButtonClick = ActionDispatcher.Bind<OnChangeBallSpectateToken>(OnSpectateClick);
            _onTieBreak = ActionDispatcher.Bind<HeadToHeadTieBreakAction>(OnTieBreak);
            _onFinishGamePlay = ActionDispatcher.Bind<OnFinishGameAction>(OnFinishGame);
        }

        private void OnFinishGame(OnFinishGameAction _)
        {
            gameplayPlayersInfoUIComponent.OnFinishGameAction();
        }

        private void OnTieBreak(HeadToHeadTieBreakAction obj)
        {
            gameplayPlayersInfoUIComponent.OnTieBreak();
        }
        
        private void OnSpectateClick(OnChangeBallSpectateToken token)
        {
            if (MasterManager.Instance.CurrentBallInCamera == LocalPlayerIndex)
            {
                // back to spectate local
                gameplayPlayersInfoUIComponent.OnSpectate(isLocal: true);
            }
            else
            {
                // go to spectate opponent
                gameplayPlayersInfoUIComponent.OnSpectate(isLocal: false);
            }
        }

        private void OnTurnOffCountDown(TurnOffCountDownAction obj)
        {
            gameplayPlayersInfoUIComponent.TurnOffCountDown();
        }

        private void OnSetWind(SetWindAction obj)
        {
            gameplayPlayersInfoUIComponent.SetWind(obj.WeatherData);
            gameplayPlayersInfoUIComponent.SetWindDirection(obj.WeatherData.WindDirection);
        }

        public override void ComponentDisposeReference()
        {
            
        }

        public override void ComponentDisposeListener()
        {
            ActionDispatcher.Unbind(_changeBallAction);
            ActionDispatcher.Unbind(_changeGearAction);
            ActionDispatcher.Unbind(_setShotClockToken);
            ActionDispatcher.Unbind(_onLocalPlayerHitToken);
            ActionDispatcher.Unbind(_onShotClockTimeOut);
            ActionDispatcher.Unbind(_onSetWindToken);
            ActionDispatcher.Unbind(onLocalLobbyPlayerStrokeChangedAction);
            ActionDispatcher.Unbind(_turnOffCountDown);

            ActionDispatcher.Unbind(_continueShotAction);
            ActionDispatcher.Unbind(_onSpectateButtonClick);
        }

        public override ModuleManager ComponentGetManager()
        {
            throw new System.NotImplementedException();
        }

        public override ModuleProxy ComponentGetProxy()
        {
            throw new System.NotImplementedException();
        }

        private void OnBallChange(ChangeBallAction action)
        {
            gameplayPlayersInfoUIComponent.OnBallChange(action.ball);
        }

        private void OnGearChange(ChangeGearAction action)
        {
            gameplayPlayersInfoUIComponent.OnGearChange(action.gear);
        }

        private void OnShotClockTimeOut(ShotClockTimeOut action)
        {
            gameplayPlayersInfoUIComponent.OnShotClockTimeOut();
        }

        private void OnBallHit(LocalPlayerHitBall action)
        {
            gameplayPlayersInfoUIComponent.OnBallHit();
            gameplayPlayersInfoUIComponent.OnHide();
        }

        private void OnContinueShot(MoveCameraForNextShotAction action)
        {
            gameplayPlayersInfoUIComponent.OnOpen();
        }

        private void SetShotClockText(SetShotClockAction action)
        {
            gameplayPlayersInfoUIComponent.SetShotClockText(remainTime: action.RemainTime, totalTime: action.TotalTime, strokeCount: action.StrokeCount);
        }

        private void OnPlayerStrokeCountChange(LocalLobbyPlayerStrokeCountChangedAction action)
        {
            switch (GlobalSO.GameplayBus.currentLogic.logicData)
            {
                case MultiplayerNormalLogicSO:
                case SinglePlayerClosestToPinSO:
                    gameplayPlayersInfoUIComponent.SetOtherPlayerStroke(strokeCount: action.StrokeCount);
                    break;
                default:
                    gameplayPlayersInfoUIComponent.SetShotClockText(remainTime: 0, totalTime: 0, strokeCount: action.StrokeCount);
                    break;
            }
        }

        public void SetBall(List<Ball> balls)
        {
            ballList = balls;
        }
    }
}
