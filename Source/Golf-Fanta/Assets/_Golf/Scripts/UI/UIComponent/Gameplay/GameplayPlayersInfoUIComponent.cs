using _Golf.Scripts.Common;
using _Golf.Scripts.Core;
using _Golf.Scripts.ScriptableObjects;
using _Golf.Scripts.ScriptableObjects.Item;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using _Golf.Scripts.PlayField;
using TinyMessenger;
using UnityEngine;
using GolfGame;

namespace _Golf.Scripts.UI
{
    public class GameplayPlayersInfoUIComponent : UIComponentBase<GameplayPlayersInfoUIComponentMediator>
    {
        GameplayPlayersInfoUIComponentMediator gameplayPlayersInfoUIComponentMediator;

        [SerializeField] private PlayersInfoUIComponent playersInfoUIComponent;
        private PlayerGadget playerGadget;

        private GameplayBus _gameplayBus;
        private Camera _camera;
        private float _windDirection;
        private float _windSpeed;

        public override UIComponentMediator CreateMediator()
        {
            gameplayPlayersInfoUIComponentMediator = new GameplayPlayersInfoUIComponentMediator(this);
            return gameplayPlayersInfoUIComponentMediator;
        }

        public override void Init()
        {
            _gameplayBus = GlobalSO.GameplayBus;
            playerGadget = GlobalSO.PlayerGadget;
        }

        public override void OnOpen(object[] data = null)
        {
            _camera = Camera.main;

            playerGadget = GlobalSO.PlayerGadget;
            gameObject.SetActive(true);
            playersInfoUIComponent.gameObject.SetActive(true);

            //set ball info
            if (playerGadget.ChosenBall != null)
            {
                BallInfo ballInfo = playerGadget.GetPlayerBallData(playerGadget.ChosenBall);

                LocalPlayer otherPlayer = null;
                foreach (var player in _gameplayBus.localLobby.LocalPlayers)
                {
                    if (player.GetId() != _gameplayBus.localPlayer.GetId())
                    {
                        otherPlayer = player;
                    }
                }
                
                playersInfoUIComponent.SetPlayersInfo(
                    localName: _gameplayBus.localPlayer.GetDisplayName(),
                    localElo: GlobalSO.PlayerInfoSO.Info.H2hRank.Elo,
                    otherName: otherPlayer != null ? otherPlayer.GetDisplayName() : "null",
                    otherElo: (otherPlayer != null && otherPlayer.GetPlayerRankInfo() != null) ? otherPlayer.GetPlayerRankInfo().currentPoint : 0,
                    otherAvatar:  (otherPlayer != null && otherPlayer.GetPlayerAvatarInfo() != null) ? otherPlayer.UserAvt : null
                );

                playersInfoUIComponent.SetBallInfo(
                    extraDistanceValue: ballInfo.GetExtraDistance(),
                    windResistanceValue: ballInfo.GetWindResistance(),
                    bouncingValue: ballInfo.GetBouncing(),
                    sideSpinValue: ballInfo.GetSideSpin()
                );
                
                playersInfoUIComponent.SetRewardAmount(_gameplayBus.currentLobbyProperty.WinningAmount);

                if (otherPlayer != null)
                {
                    playersInfoUIComponent.DisplayOtherContent(true);
                    playersInfoUIComponent.SetOtherPlayerStroke(otherPlayer.GetStrokeCount());
                }
                else
                {
                    playersInfoUIComponent.DisplayOtherContent(false);
                }
            }

            //set gear info
            if (playerGadget.ChosenChip != null)
            {
                GearInfo gearInfo = playerGadget.GetPlayerGearData(playerGadget.ChosenChip);
                playersInfoUIComponent.SetGearInfo(ResourcesManager.Instance.GetGearSprite(gearInfo.GetGearId()));
            }
            else
            {
                playersInfoUIComponent.SetGearInfo(null);
            }
        }

        public void OnTieBreak()
        {
            playersInfoUIComponent.OnTieBreak(true);
        }

        public void SetWind(WeatherData weatherData)
        {
            _windSpeed = weatherData.WindSpeed;
            playersInfoUIComponent.SetWind(weatherData);
        }

        public override void OnHide(object[] data = null)
        {
            playersInfoUIComponent.gameObject.SetActive(false);
            gameObject.SetActive(false);
        }

        public void OnBallChange(BallSO ball)
        {
            //set ball info
            if (playerGadget.ChosenBall != null)
            {
                BallInfo ballInfo = playerGadget.GetPlayerBallData(ball);

                playersInfoUIComponent.SetBallInfo(
                    extraDistanceValue: ballInfo.GetExtraDistance(),
                    windResistanceValue: ballInfo.GetWindResistance(),
                    bouncingValue: ballInfo.GetBouncing(),
                    sideSpinValue: ballInfo.GetSideSpin()
                );
            }
        }

        public void OnGearChange(ChipSO gear)
        {
            //set gear info
            if (playerGadget.ChosenChip != null)
            {
                GearInfo gearInfo = playerGadget.GetPlayerGearData(gear);
                playersInfoUIComponent.SetGearInfo(ResourcesManager.Instance.GetGearSprite(gearInfo.GetGearId()));
            }
            else
            {
                playersInfoUIComponent.SetGearInfo(null);
            }
        }

        public void SetShotClockText(float remainTime, float totalTime, int strokeCount)
        {
            playersInfoUIComponent.SetAvatarCountDown(remainTime, totalTime);
            playersInfoUIComponent.SetStrokeImages(remainTime, totalTime, strokeCount);
        }

        public void OnBallHit()
        {
            playersInfoUIComponent.TurnOffAvatarCountDown();
        }

        public void TurnOffCountDown()
        {
            playersInfoUIComponent.TurnOffAvatarCountDown();
        }

        public void OnShotClockTimeOut()
        {
            playersInfoUIComponent.TurnOffAvatarCountDown();
        }

        public void SetOtherPlayerStroke(int strokeCount)
        {
            UnityEngine.Debug.Log("other stroke count: " + strokeCount);
            playersInfoUIComponent.SetOtherPlayerStroke(strokeCount);
        }

        public void OnSpectate(bool isLocal)
        {
            playersInfoUIComponent.OnSpectate(isLocal);
        }

        public void OnFinishGameAction()
        {
            playersInfoUIComponent.OnTieBreak(false);
            OnHide();
        }

        public void SetWindDirection(float wind)
        {
            _windDirection = wind;
        }

        void Update()
        {
            if (gameObject.activeSelf)
            {
                if (_camera == null || _windSpeed <= 0) return;

                playersInfoUIComponent.UpdateWinDirection(_windDirection, _camera.transform);
            }
        }
    }
}
