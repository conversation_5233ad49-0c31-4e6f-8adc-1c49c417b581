using _Golf.Scripts.Common;
using _Golf.Scripts.Core;
using _Golf.Scripts.ScriptableObjects;
using GolfGame;
using System;
using UnityEngine;
using UnityEngine.UI;

namespace _Golf.Scripts.UI
{
    public class GameplayGolfBallSwingUIComponent : UIComponentBase<GameplayGolfBallSwingUIComponentMediator>
    {
        GameplayGolfBallSwingUIComponentMediator gameplayGolfBallSwingUIComponentMediator;

        [SerializeField]
        private GolfBallSwingVersion dragVersion;

        [SerializeField]
        private GolfBallSwingDragUI golfBallSwingDragUI;

        [SerializeField]
        private GolfBallSwingDragUIVersion2 golfBallSwingDragVersion2;

        [SerializeField]
        private GolfBallSwingDragPuttingUI golfBallSwingDragPuttingUI;

        [SerializeField] private Button SpectateButton;

        private GolfBallSwingDragUISample _currentLogicPanel;

        public override UIComponentMediator CreateMediator()
        {
            gameplayGolfBallSwingUIComponentMediator = new GameplayGolfBallSwingUIComponentMediator(this);
            return gameplayGolfBallSwingUIComponentMediator;
        }

        public override void Init()
        {
        }

        public void InitRef()
        {
            SpectateButton.onClick.AddListener(SpectateButtonClick);
        }

        private void SpectateButtonClick()
        {
            if (GlobalSO.GameplayBus.currentLogic is MultiplayerNormalLogic)
            {
                MultiplayerNormalLogic gameLogic = (MultiplayerNormalLogic)GlobalSO.GameplayBus.currentLogic;

                float currentCDTimer = gameLogic.GetCurrentCountdownTime();
                if (currentCDTimer > 5f)
                {
                    MasterManager.Instance.ChangeCurrentBallInCamera();
                    ActionDispatcher.Dispatch(new OnChangeBallSpectateToken(true));
                }
            }
        }

        public void DisposeRef()
        {
            SpectateButton.onClick.RemoveAllListeners();
        }

        public void OnCheckCamera()
        {
            _currentLogicPanel.OnHide();
            if(!gameplayGolfBallSwingUIComponentMediator.IsPutting())
            {
                _currentLogicPanel = golfBallSwingDragVersion2;
            }
            else{
                _currentLogicPanel = golfBallSwingDragPuttingUI;
            }
            _currentLogicPanel.OnOpen();
        }

        public void HideDrag()
        {
            if (!gameplayGolfBallSwingUIComponentMediator.IsPutting())
            {
                _currentLogicPanel = golfBallSwingDragVersion2;
            }
            else
            {
                _currentLogicPanel = golfBallSwingDragPuttingUI;
            }
            _currentLogicPanel.OnHide();
            gameObject.SetActive(false);
        }

        public override void OnOpen(object[] data = null)
        {
            gameObject.SetActive(true);
            if(!gameplayGolfBallSwingUIComponentMediator.IsPutting())
            {
                _currentLogicPanel = golfBallSwingDragVersion2;
            }
            else{
                _currentLogicPanel = golfBallSwingDragPuttingUI;
            }
            _currentLogicPanel.OnOpen();
        }

        public override void OnHide(object[] data = null)
        {
            gameObject.SetActive(false);

            _currentLogicPanel.OnHide();
        }

        public void StartGameSetup()
        {
            _currentLogicPanel = golfBallSwingDragVersion2;
            golfBallSwingDragVersion2.StartGameSetup();
            golfBallSwingDragPuttingUI.StartGameSetUp();
        }

        public GolfBallSwingVersion GetVersion()
        {
            return dragVersion;
        }

        public GolfBallSwingDragUI GetDragUI()
        {
            return golfBallSwingDragUI;
        }

        public GolfBallSwingDragUIVersion2 GetDragUIVersion2()
        {
            return golfBallSwingDragVersion2;
        }

        public GolfBallSwingDragPuttingUI GetGolfBallSwingDragPuttingUI()
        {
            return golfBallSwingDragPuttingUI;
        }

        public void OnContinueHitting()
        {
            if (!gameplayGolfBallSwingUIComponentMediator.IsPutting())
            {
                _currentLogicPanel = golfBallSwingDragVersion2;
            }
            else
            {
                _currentLogicPanel = golfBallSwingDragPuttingUI;
            }
            _currentLogicPanel.OnGoingTop();
        }

        public void SetActiveSpectateButton(bool active)
        {
            SpectateButton.gameObject.SetActive(active);
        }
    }

    public enum GolfBallSwingVersion
    {
        VersionOne = 1,
        VersionTwo = 2,
    }
}
