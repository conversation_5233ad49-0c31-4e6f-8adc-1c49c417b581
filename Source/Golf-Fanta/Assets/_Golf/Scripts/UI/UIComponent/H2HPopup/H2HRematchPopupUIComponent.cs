using _Golf.Scripts.Common;
using _Golf.Scripts.Core;
using _Golf.Scripts.Networking;
using _Golf.Scripts.ScriptableObjects;
using _Golf.Scripts.UI;
using DG.Tweening;
using GolfGame;
using UnityEngine.UIElements;

public class H2HRematchPopupUIComponent : PopupUIComponentBase<H2HRematchPopupUIComponentMediator>
{
    private H2HRematchPopupUIComponentMediator _mediator;
    private Button _cancelButton;
    private Button _acceptButton;
    private Tweener _countdownTween;
    private GameplayBus _bus;
    public override UIComponentMediator CreateMediator()
    {
        _mediator = new H2HRematchPopupUIComponentMediator(this);
        return _mediator;
    }

    public override void BeforeOpen(object[] data = null)
    {
        var lobbyProperty = GlobalSO.GameplayBus.currentLobbyProperty;
        if (data != null)
        {
            var opponentPlayer = (LocalPlayer)data[0];
            var infoText = _root.Q<Label>("DescriptionText");
            infoText.text = opponentPlayer.GetDisplayName() + " wants to rematch for " + lobbyProperty.WinningAmount + " coins!\nThe request will disappear in 10 seconds";
        }
    }

    public override void AfterOpen(object[] data = null)
    {
        var lobbyProperty = GlobalSO.GameplayBus.currentLobbyProperty;
        _countdownTween = DOVirtual.Int(10, 0, 10, newValue =>
        {
            if (data != null)
            {
                var opponentPlayer = (LocalPlayer)data[0];
                var infoText = _root.Q<Label>("DescriptionText");
                infoText.text = opponentPlayer.GetDisplayName() + " wants to rematch for " + lobbyProperty.WinningAmount + " coins!\nThe request will disappear in " + newValue +" seconds";
            }
        }).SetEase(Ease.Linear).OnComplete(() =>
        {
            MasterManager.Instance.HideUIComponent(UIComponentEnum.H2HRematchPopupUIComponent);
        });
    }

    public override void AfterHide(object[] data = null)
    {
        base.AfterHide(data);
        _countdownTween.Kill();
    }

    public override void OnInit()
    {
        _bus = GlobalSO.GameplayBus;
        
        Button closeButton = _root.Q<Button>("CloseButton");

        closeButton.clicked += () =>
        {
            MasterManager.Instance.HideUIComponent(UIComponentEnum.H2HRematchPopupUIComponent);
        };

        _cancelButton = _root.Q<Button>("cancelBtn");
        _cancelButton.clicked += () =>
        {
            MasterManager.Instance.HideUIComponent(UIComponentEnum.H2HRematchPopupUIComponent);
            ActionDispatcher.Dispatch(new ExitGameplayAction());
        };
        
        _acceptButton = _root.Q<Button>("acceptBtn");
        _acceptButton.clicked += () =>
        {
            var coin = UGSEconomy.CoinBalance;
            if (coin < _bus.currentLobbyProperty.TourInfo.GetEntryFee())
            {
                GeneralPopupContainer popupContainer = new GeneralPopupContainer
                (
                    title: "Rematch Failed",
                    description: "You or your opponent don’t have enough coins to rematch.",
                    continueButtonText: "OK",
                    continueAction: () =>
                    {
                        MasterManager.Instance.HideUIComponent(UIComponentEnum.GeneralPopupUIComponent);
                    }
                );

                MasterManager.Instance.OpenUIComponent(UIComponentEnum.GeneralPopupUIComponent, new object[] { popupContainer });
            }
            else
            {
                // Accept Rematch
                MasterManager.Instance.HideUIComponent(UIComponentEnum.H2HRematchPopupUIComponent);
                var localPlayer =
                    _bus.localLobby.LocalPlayers.Find((player => player.GetId() == _bus.localPlayer.GetId()));
                    
                // Reset Stroke
                localPlayer.ResetPlayer();
                localPlayer.SetUserStatus(PlayerStatus.ReadyForRematch);
                _ = _bus.lobbyHandler.SyncPlayerData(localPlayer);
            }
        };
    }
}
