using _Golf.Scripts.Common;
using _Golf.Scripts.Core;
using _Golf.Scripts.GlobalManagers;
using _Golf.Scripts.ScriptableObjects;
using Game.UI;
using GolfGame;
using UnityEngine.Localization;
using UnityEngine.UIElements;

namespace _Golf.Scripts.UI
{
    public class H2HSettingUIComponent : PopupUIComponentBase<H2HSettingUIComponentMediator>
    {
        private H2HSettingUIComponentMediator _mediator;
        private Button _forfeitButton;
        private Button _closeButton;
        private SlideToggle _soundToggle;
        private LocalizedString _forfeitString;
        private LocalizedString _forfeitDescriptionString;

        public override UIComponentMediator CreateMediator()
        {
            _mediator = new H2HSettingUIComponentMediator(this);
            return _mediator;
        }

        public override void OnInit()
        {
            _closeButton = _root.Q<Button>("CloseButton");

            _closeButton.clicked += () =>
            {
                MasterManager.Instance.HideUIComponent(UIComponentEnum.H2HSettingsComponentUI);
            };

            _forfeitButton = _root.Q<Button>("ExitButton");
            _forfeitButton.clicked += () =>
            {
                var container = new GeneralPopupContainer(
                    title: _forfeitString.GetLocalizedString(),
                    description: _forfeitDescriptionString.GetLocalizedString(),
                    continueButtonText: _forfeitString.GetLocalizedString(),
                    continueButtonColor: GolfUIToolKit.Utility.ButtonColor.Red,
                    continueAction: OnSurrender);
                
                MasterManager.Instance.OpenUIComponent(UIComponentEnum.GeneralPopupUIComponent, new object[] { container });

            };

            _soundToggle = _root.Q<SlideToggle>();

            _soundToggle.RegisterValueChangedCallback(evt =>
            {
                var isMuted = !evt.newValue;
                ActionDispatcher.Dispatch(new ControlAudioAction(isMuted));
            });

            _forfeitString = LocalizationManager.Instance.GetLocalizedString("game_play_forfeit");
            _forfeitDescriptionString = LocalizationManager.Instance.GetLocalizedString("game_play_forfeit_des");
        }

        public override void BeforeOpen(object[] data = null)
        {
            _soundToggle.SetValueWithoutNotify(!AudioManager.Instance.IsMuted);
        }

        private void OnSurrender()
        {
            
            MasterManager.Instance.HideUIComponent(UIComponentEnum.GeneralPopupUIComponent);
            
            MasterManager.Instance.HideUIComponent(UIComponentEnum.H2HSettingsComponentUI);

            var localPlayer = GlobalSO.GameplayBus.localLobby.LocalPlayers.Find(player =>
                player.GetId() == GlobalSO.GameplayBus.localPlayer.GetId());
            
            localPlayer.SetUserStatus(PlayerStatus.Forfeit);
            
            _ = GlobalSO.GameplayBus.lobbyHandler.SyncPlayerData(localPlayer);
        }
        
    }
}
