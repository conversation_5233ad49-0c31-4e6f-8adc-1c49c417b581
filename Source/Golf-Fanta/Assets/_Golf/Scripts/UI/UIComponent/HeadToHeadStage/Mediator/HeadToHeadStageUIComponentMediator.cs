using System.Collections.Generic;
using _Golf.Scripts.Common;
using _Golf.Scripts.Lobby;
using _Golf.Scripts.Networking;
using GolfGame;
using GolfGame.UIMediators.Base;

namespace _Golf.Scripts.UI
{
    public class HeadToHeadStageUIComponentMediator : UIComponentMediator
    {
        public HeadToHeadStageUIComponentMediator(UIComponentBase uIComponent) : base(uIComponent) { }

        public override void ComponentInitReference() { }

        public override void ComponentInitListener() { }

        public override void ComponentDisposeReference() { }

        public override void ComponentDisposeListener() { }

        public override void InitDependency(Dictionary<UIComponentEnum, UIComponentMediator> uiComponentMediators, Dictionary<EScreenEnum, UIScreenMediator> uiScreenMediators) {}

        public void RequestMultiplayer(LobbyProperty lobby)
        {
            ActionDispatcher.Dispatch(new QueueStartButtonClickAction(lobby));
        }

        public override ModuleProxy ComponentGetProxy()
        {
            throw new System.NotImplementedException();
        }

        public override ModuleManager ComponentGetManager()
        {
            throw new System.NotImplementedException();
        }
    }
}