using System.Collections.Generic;
using _Golf.Scripts.ScriptableObjects.HeadToHead;
using UnityEngine;
using UnityEngine.UIElements;
using Kamgam.UIToolkitScrollViewPro;
using GolfGame;
using Button = UnityEngine.UIElements.Button;
using _Golf.Scripts.Common;
using _Golf.Scripts.Core;
using _Golf.Scripts.Lobby;
using _Golf.Scripts.Networking;
using _Golf.Scripts.ScriptableObjects;
using Game.UI;
using GolfPhysics;
using Cysharp.Threading.Tasks;
using UnityEngine.Localization;
using _Golf.Scripts.GlobalManagers;
using AYellowpaper.SerializedCollections;
using _Golf.Scripts.ScriptableObjects.Tours;
using GolfUIToolKit;

namespace _Golf.Scripts.UI
{
	public class HeadToHeadStageUIComponent : UIComponentBase<HeadToHeadStageUIComponentMediator>
	{
		[SerializeField] private VisualTreeAsset _availableVisualTree;
		[SerializeField] private VisualTreeAsset _unavailableVisualTree;
		[SerializeField] private StyleSheet _itemStyle;

		private HeadToHeadProperties _properties;
		private PlayerInfo _playerInfo;
		private HeadToHeadStageUIComponentMediator _mediator;
		private ScrollViewPro _scrollViewPro;
		private List<VisualElement> _listStages = new List<VisualElement>();
		private float _cacheScrollViewWidth;
		private int _currentSnapIndex = -1;
		private int _cachedStageIndex = 0;
		private bool _handleSnapEvent;

		#region Resources
		[SerializeField] private List<Sprite> _bg;
		[SerializeField] private List<Sprite> _bgLock;
		[SerializeField] private SerializedDictionary<string, Sprite> _backgroundMap;
		[SerializeField] private SerializedDictionary<string, Sprite> _lockedBackgroundMap;
		
		LocalizedString _entryFee;
		LocalizedString _notiLabel;
		LocalizedString _okLabel;
		LocalizedString _notEnoughEntryFee;
		#endregion

		public override UIComponentMediator CreateMediator()
		{
			_mediator = new HeadToHeadStageUIComponentMediator(this);
			_properties = GlobalSO.HeadToHeadProperties;
			_playerInfo = GlobalSO.PlayerInfoSO.Info;
			return _mediator;
		}

		private void SnapToIndex(TransitionEndEvent evt)
		{
			if (!evt.stylePropertyNames.Contains("transform-origin"))
			{
				return;
			}

			foreach (var element in _listStages)
			{
				element.UnregisterCallback<TransitionEndEvent>(SnapToIndex);
			}

			_scrollViewPro.Snap();
		}

		private void UpdateUI(int index)
		{
			if (index == _currentSnapIndex || !_handleSnapEvent)
            {
                return;
            }

			if (index < 0)
			{
				index = 0;
			}
			else if (index >= _listStages.Count)
			{
				index = _listStages.Count - 1;
			}

			_currentSnapIndex = index;

			for (int i = 0; i < index; i++)
			{
				var element = _listStages[i];
				var H2HContent = element.Q<VisualElement>("H2HContent");

				H2HContent.AddToClassList("stage-hide-left");
				H2HContent.RemoveFromClassList("stage-hide-right");
				H2HContent.RemoveFromClassList("stage-show");
			}

			for (int i = index + 1; i < _listStages.Count; i++)
			{
				var element = _listStages[i];
				var H2HContent = element.Q<VisualElement>("H2HContent");

				H2HContent.AddToClassList("stage-hide-right");
				H2HContent.RemoveFromClassList("stage-hide-left");
				H2HContent.RemoveFromClassList("stage-show");
			}

			var focusElement = _listStages[index];
			var focusH2HContent = focusElement.Q<VisualElement>("H2HContent");

			focusH2HContent.RemoveFromClassList("stage-hide-left");
			focusH2HContent.RemoveFromClassList("stage-hide-right");
			focusH2HContent.AddToClassList("stage-show");
		}

		public override void OnHide(object[] data = null)
		{
			// _scrollViewPro.Clear();
			// _listStages.Clear();
			// _cachedPlayButton.Clear();

			// panel.style.visibility = Visibility.Hidden;
			BackButton.style.visibility = Visibility.Hidden;
			_scrollViewPro.OnScrollToElement -= UpdateUI;
			_cachedStageIndex = _currentSnapIndex;
			_currentSnapIndex = -1;

			_scrollViewPro.RemoveFromClassList("show");
			_scrollViewPro.RegisterCallbackOnce<TransitionEndEvent>(evt =>
			{
				document.rootVisualElement.style.display = DisplayStyle.None;
			});
		}

		public override void OnOpen(object[] data = null)
		{
			// panel.style.visibility = Visibility.Visible;
			document.rootVisualElement.style.display = DisplayStyle.Flex;
			_scrollViewPro.OnScrollToElement += UpdateUI;

			_scrollViewPro.AddToClassList("show");
			_handleSnapEvent = false;
			_scrollViewPro.RegisterCallbackOnce<TransitionStartEvent>(evt =>
            {
                InitThread();
				BackButton.style.visibility = Visibility.Visible;
                _scrollViewPro.schedule.Execute(() =>
                {
                    SnapTo(_cachedStageIndex);
                }).StartingIn(10);
            });
		}

		UIDocument document;
		// VisualElement panel;
		Button BackButton;

		public override void Init()
		{
			document = GetComponent<UIDocument>();
			document.rootVisualElement.styleSheets.Add(_itemStyle);
			_scrollViewPro = document.rootVisualElement.Q<ScrollViewPro>("StageScrollView");
			_scrollViewPro.RegisterCallback<GeometryChangedEvent>(evt =>
			{
				if (_scrollViewPro.resolvedStyle.width > 0f)
				{
					_cacheScrollViewWidth = _scrollViewPro.resolvedStyle.width;
				}
			});

			//localization
			_entryFee = LocalizationManager.Instance.GetLocalizedString("tournament_entry_fee");
			_notiLabel = LocalizationManager.Instance.GetLocalizedString("common_notification");
			_okLabel = LocalizationManager.Instance.GetLocalizedString("common_ok");
			_notEnoughEntryFee = LocalizationManager.Instance.GetLocalizedString("common_not_enough_entry_fee_des");
		}

		List<VisualElement> _cachedPlayButton = new List<VisualElement>();
		private void InitThread()
		{
			document = GetComponent<UIDocument>();

			VisualElement root = document.rootVisualElement;

			// panel = root.Q<VisualElement>("HeadToHeadStagePanel");

			BackButton = root.Q<Button>("BackButton");
			BackButton.pickingMode = PickingMode.Position;
			BackButton.style.unityBackgroundImageTintColor = Color.white;

			BackButton.clickable = new Clickable(() =>
			{
				MasterManager.Instance.OnChangeScreen(EScreenEnum.GameMode).Forget();
			});

			_scrollViewPro.Clear();
			_cachedPlayButton.Clear();
			_listStages.Clear();

			VisualElement leftPadding = new VisualElement();
			leftPadding.style.width = _cacheScrollViewWidth * Constant.ContentWitdhSizeSmoothDrag / 2f;
			_scrollViewPro.Add(leftPadding);

			for (var index = 0; index < _properties.Lobbies.Count; index++)
			{
				var lobby = _properties.Lobbies[index];
				var eloCondition = lobby.TourInfo.GetTourConditionByType(TourConditionType.RequiredElo);
				bool isAvailable = eloCondition == null || eloCondition.EvaluateCondition(_playerInfo.H2hRank.Elo);

				var visualTree = isAvailable ? _availableVisualTree : _unavailableVisualTree;
				VisualElement item = visualTree.Instantiate();
				item.style.width = _cacheScrollViewWidth * Constant.ContentWitdhSizeSmoothDrag;
				item.style.height = Length.Percent(100);

				var info = _playerInfo.H2hRank.Info;
				var rank = _playerInfo.H2hRank;
				item.Q<Label>("WindText").DisplayWindSpeed(lobby.TourInfo.GetWindSpeedMin(), lobby.TourInfo.GetWindSpeedMax());
				item.Q<Label>("NeedleText").text = $"{info.needleSpeed:F1} %";

				var key = $"tee_position_{(int)_playerInfo.H2hRank.MajorRank + 1}";
				item.Q<Label>("FrontText").text = LocalizationManager.Instance.GetString(key);

				if (isAvailable)
				{
					var bannerIndex = lobby.TourInfo.GetH2HBannerIndex();
					if (bannerIndex < 0 || bannerIndex >= _bg.Count)
					{
						UnityEngine.Debug.LogError("Banner Index out of range: " + bannerIndex);
						bannerIndex = 0;
					}

					item.Q<VisualElement>("H2HContent").style.backgroundImage =
						new StyleBackground(_bg[bannerIndex]);
					item.Q<Label>("CoinQuantity").text = lobby.WinningAmount.ToString();

					_entryFee.Arguments = new object[] { lobby.TourInfo.GetEntryFee() };
					item.Q<Label>("EntryFeeLabel").text = _entryFee.GetLocalizedString();
					var playButton = item.Q<Button>("PlayBtn");
					playButton.style.unityBackgroundImageTintColor = Color.white;
					playButton.pickingMode = PickingMode.Position;
					playButton.style.visibility = Visibility.Visible;
					playButton.clicked += () =>
					{
						if (UGSEconomy.CoinBalance < lobby.TourInfo.GetEntryFee())
						{
							var container = new GeneralPopupContainer(
								title: _notiLabel.GetLocalizedString(),
								description: _notEnoughEntryFee.GetLocalizedString(),
								continueButtonText: _okLabel.GetLocalizedString(),
								() => MasterManager.Instance.HideUIComponent(UIComponentEnum.GeneralPopupUIComponent)
							);
							MasterManager.Instance.OpenUIComponent(UIComponentEnum.GeneralPopupUIComponent, new object[] { container });
							return;
						}

						if (HeadToHeadStageScreenMediator.OnRequestCheckAndDownload.Lobby != null)
						{
							return;
						}

						HeadToHeadStageScreenMediator.OnRequestCheckAndDownload.Lobby = lobby;
						HeadToHeadStageScreenMediator.OnRequestCheckAndDownload.UiPanel = this;
						HeadToHeadStageScreenMediator.OnRequestCheckAndDownload.Item = item;

						ActionDispatcher.Dispatch(HeadToHeadStageScreenMediator.OnRequestCheckAndDownload);
					};
					item.Q<Label>("WinTrophyNumber").text = $"+{info.headToHeadWin}";
					item.Q<Label>("LoseTrophyNumber").text = $"-{info.headToHeadLose}";

					_scrollViewPro.Add(item);
					_listStages.Add(item);
					_cachedPlayButton.Add(playButton);
				}
				else
				{
					var bannerIndex = lobby.TourInfo.GetH2HBannerIndex();
					if (bannerIndex < 0 || bannerIndex >= _bgLock.Count)
					{
						UnityEngine.Debug.LogError("Banner Index out of range: " + bannerIndex);
						bannerIndex = 0;
					}

					item.Q<VisualElement>("H2HContent").style.backgroundImage =
						new StyleBackground(_bgLock[bannerIndex]);
					item.Q<Label>("MinRankLabel").text = lobby.TourInfo.GetRequiredElo().ToString();
					_scrollViewPro.Add(item);
					_listStages.Add(item);
				}
			}

			VisualElement rightPadding = new VisualElement();
			rightPadding.style.width = _cacheScrollViewWidth * Constant.ContentWitdhSizeSmoothDrag / 2f;
			_scrollViewPro.Add(rightPadding);

			UpdateUI(_cachedStageIndex);
		}

		private void SnapTo(int index)
        {
            _handleSnapEvent = true;

            float targetPosition = index * _cacheScrollViewWidth * Constant.ContentWitdhSizeSmoothDrag;
            _scrollViewPro.horizontalScroller.value = targetPosition;
            var cachedSnap = _scrollViewPro.snapDurationSec;
            _scrollViewPro.snapDurationSec = 0.01f;

            ExcuteSnap(cachedSnap).Forget();
        }

        private async UniTask ExcuteSnap(float cachedSnapDuration)
        {
            _scrollViewPro.Snap();
            await UniTask.DelayFrame(2);
            _scrollViewPro.snapDurationSec = cachedSnapDuration;
            UpdateUI(_cachedStageIndex);
        }

		public void SetUpRequestMatchUp(LobbyProperty lobby)
		{
			BackButton.clickable = null;
			_mediator.RequestMultiplayer(lobby);
		}

		public void SetUpAssetLoading(float loadProgress, VisualElement item)
		{
			var loadingPanel = item.Q<VisualElement>("LoadingAsset");
			var button = item.Q<Button>("PlayBtn");
			var loadingBarUXML = item.Q<LoadingBarUXML>();
			var loadingText = item.Q<Label>("LoadingText");

			button.style.display = DisplayStyle.None;
			loadingPanel.style.display = DisplayStyle.Flex;
			loadingText.text = "LOADING ASSET...";
			loadingBarUXML.SetValue(loadProgress);
		}
	}
}