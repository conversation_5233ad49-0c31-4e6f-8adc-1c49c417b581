using System;
using _Golf.Scripts.Common;
using _Golf.Scripts.Networking;
using _Golf.Scripts.UI;
using Game.UI;
using GolfPhysics;
using UnityEngine;
using UnityEngine.Localization;
using UnityEngine.UIElements;

namespace _Golf.Scripts.UI
{
    public class LoadingComponentUI : UIComponentBase<LoadingComponentUIMediator>
    {
        UIDocument document;
        VisualElement root;
        private LoadingBarUXML _progressBar;
        private LoadingUIContainer _loadingHandler;
        private Label _loadingText;
        private Label _versionText;

        private VisualElement _bg;
        private VisualElement _bg1;


        public override UIComponentMediator CreateMediator()
        {
            return new LoadingComponentUIMediator(this);
        }

        public override void Init()
        {
            document = GetComponent<UIDocument>();
            root = document.rootVisualElement;
            _progressBar = root.Q<LoadingBarUXML>();
            _progressBar.style.display = DisplayStyle.Flex;
            _progressBar.SetValue(0);

            _versionText = root.Q<Label>("VersionText");
            _versionText.text = Constant.AppVersion;

            _loadingText = root.Q<Label>("LoadingText");
            _loadingText.text = "LOADING ...";

            root.Q<Label>("DescriptionText").text = "";

            _bg = root.Q<VisualElement>("BG");
            _bg1 = root.Q<VisualElement>("BG1");
        }

        public override void OnHide(object[] data = null)
        {
            _loadingHandler.OnProgress -= UpdateProgressBar;
            _loadingHandler.OnProgressWithMessage -= UpdateProgressBar;

            _loadingHandler = null;
            root.style.display = DisplayStyle.None;
        }

        public override void OnOpen(object[] data = null)
        {
            _progressBar.SetValue(0);
            _loadingHandler = data[0] as LoadingUIContainer;
            _loadingHandler.OnProgress += UpdateProgressBar;
            _loadingHandler.OnProgressWithMessage += UpdateProgressBar;

            var mode = _loadingHandler.Mode;
            var binding = _loadingText.GetBinding("text") as LocalizedString;
            binding.Arguments = new object[] { 1 };

            switch (mode)
            {
                case LoadingMode.NoBackGround:
                    _bg.style.display = DisplayStyle.None;
                    _bg1.style.display = DisplayStyle.None;
                    _versionText.style.display = DisplayStyle.None;
                    break;
                case LoadingMode.NoLogo:
                    _bg.style.display = DisplayStyle.None;
                    _bg1.style.display = DisplayStyle.Flex;
                    _versionText.style.display = DisplayStyle.Flex;
                    break;
                default:
                    _bg.style.display = DisplayStyle.Flex;
                    _bg1.style.display = DisplayStyle.None;
                    _versionText.style.display = DisplayStyle.Flex;
                    break;
            }

            root.style.display = DisplayStyle.Flex;
        }

        public void UpdateProgressBar(float progress)
        {
            _progressBar.SetValue(progress);
        }

        public void UpdateProgressBar(float progress, string message)
        {
            _progressBar.SetValue(progress);

            if (string.IsNullOrEmpty(message)) return;
            // _loadingText.text = message;
            var binding = _loadingText.GetBinding("text") as LocalizedString;
            binding.Arguments = new object[] { 2, message };
            binding.RefreshString();

        }
    }

    public class LoadingUIContainer
    {
        public Action<float> OnProgress;
        public Action<float, string> OnProgressWithMessage;
        public LoadingMode Mode;
    }

    public enum LoadingMode
    {
        Default = 0,
        NoBackGround = 1,
        NoLogo = 2,
    }
}
