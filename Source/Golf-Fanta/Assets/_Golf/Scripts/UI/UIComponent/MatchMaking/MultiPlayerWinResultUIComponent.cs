using System.Collections.Generic;
using System.Threading.Tasks;
using _Golf.Physics.Data;
using _Golf.Scripts.Common;
using _Golf.Scripts.Core;
using _Golf.Scripts.GlobalManagers;
using _Golf.Scripts.Lobby;
using _Golf.Scripts.Networking;
using _Golf.Scripts.ScriptableObjects;
using _Golf.Scripts.ScriptableObjects.HeadToHead;
using _Golf.UIToolKit.Scripts.Common;
using Cysharp.Threading.Tasks;
using GolfGame;
using GolfGame.UIMediators.Base;
using Kamgam.UIToolkitWorldImage.Examples;
using UnityEngine;
using UnityEngine.UIElements;

namespace _Golf.Scripts.UI.UIComponent.MatchMaking
{
    public class MultiPlayerWinUIResultData
    {
        public Ball Winner;
        public Ball Loser;
        public LobbyProperty CurrentLobbyProperty;
        public bool isTieBreak;
        public bool isTieBreakTie;
        public float localTieBreakDistanceToHole;
        public float oppoTieBreakDistanceToHole;
    }
    public class MultiPlayerWinResultUIComponent : UIComponentBase<MultiPlayerResultUIComponentMediator>
    {
        [SerializeField] private AvatarSettings _avatarSettings;
        private VisualElement _root;
        private VisualElement _playerInfoRoot;
        private VisualElement _opponentInfoRoot;
        private VisualElement _playerAvatar;
        private Label _playerName;
        private Label _playerResult;
        private List<Label> _tourLabel;
        private Label _parLabel;
        private Label _courseNameLabel;
        private Button _exitBtn;
        private Button _rematchBtn;
        private MultiPlayerWinUIResultData _data;
        private MultiPlayerResultUIComponentMediator _mediator;

        #region  --- Animation data ---
        private List<VisualElement> _animationElements;
        private SequenceTransition _openTransition;
        private SequenceTransition _closeTransition;
        #endregion
        public override UIComponentMediator CreateMediator()
        {
            _mediator = new MultiPlayerResultUIComponentMediator(this);
            return _mediator;
        }
        public override void Init()
        {
            _root = GetComponent<UIDocument>().rootVisualElement;
            _playerInfoRoot = _root.Q<VisualElement>("Player");
            _opponentInfoRoot = _root.Q<VisualElement>("Opponent");
            
            _courseNameLabel = _root.Q<Label>("HoleLabel");
            _exitBtn = _root.Q<Button>("Exit");
            GolfUIToolKit.Utility.SetButtonValue(_exitBtn, null, GolfUIToolKit.Utility.ButtonColor.Yellow);
            _rematchBtn = _root.Q<Button>("RematchBtn");
            GolfUIToolKit.Utility.SetButtonValue(_rematchBtn, null, GolfUIToolKit.Utility.ButtonColor.Blue);
            InitInfoPanel();
        }
        public override void OnOpen(object[] data = null)
        {
            if (data != null)
                _data = (MultiPlayerWinUIResultData)data[0];

            _exitBtn.clicked += OnExit;
            _rematchBtn.clicked += OnReMatch;

            SetupData().Forget();
            _root.style.display = DisplayStyle.Flex;
            _openTransition.Play();
        }

        private async UniTask SetupData()
        {
            ConfigInfoPanel(_data.CurrentLobbyProperty);
            ConfigurePlayer(_playerInfoRoot, _data.Winner, _data.CurrentLobbyProperty, true);
            ConfigurePlayer(_opponentInfoRoot, _data.Loser, _data.CurrentLobbyProperty, false);
        }
        public override void OnHide(object[] data = null)
        {
            _exitBtn.clicked -= OnExit;
            _rematchBtn.clicked -= OnReMatch;
            _root.style.display = DisplayStyle.None;
        }

        public override async UniTask OnHideWithAnimation(object[] data = null)
        {
            _exitBtn.clicked -= OnExit;
            _rematchBtn.clicked -= OnReMatch;
            CanClose = false;
            _closeTransition.Play().OnFinish((e) =>
            {
                CanClose = true;
                _root.style.display = DisplayStyle.None;
            });
            await UniTask.WaitUntil(() => CanClose);
        }

        private void OpenAnimation(VisualElement element)
        {
            foreach (var animationElement in _animationElements)
            {
                UnityEngine.Debug.Log("Open Animation" + animationElement.name);
                animationElement.AddToClassList("show");
            }
        }

        private void CloseAnimation(VisualElement element)
        {
            foreach (var animationElement in _animationElements)
            {
                animationElement.RemoveFromClassList("show");
            }
        }

        private void InitInfoPanel()
        {
            _courseNameLabel = _root.Q<Label>("HoleLabel");
            _parLabel = _root.Q<Label>("ParLabel");

            _animationElements = new List<VisualElement>();
            var rankBG = _root.Q<VisualElement>("RankBG");
            _animationElements.Add(rankBG);
            var ranking = _root.Q<VisualElement>("Ranking");
            _animationElements.Add(ranking);

            var rank = ranking.Q<VisualElement>("Rank");
            _tourLabel = rank.Query<Label>().ToList();


            var matchResult = _root.Q<VisualElement>("MatchResult");
            _animationElements.Add(matchResult);
            var player = _root.Q<VisualElement>("Player");
            _animationElements.Add(player);
            var opponent = _root.Q<VisualElement>("Opponent");
            _animationElements.Add(opponent);
            var rematchZone = _root.Q<VisualElement>("RematchZone");
            _animationElements.Add(rematchZone);
            var footer = _root.Q<VisualElement>("Footer");
            _animationElements.Add(footer);
            var lightSlash = _root.Q<VisualElement>("LightSlash");
            _animationElements.Add(lightSlash);
            var bagAwarded = _root.Q<VisualElement>("BagAwarded");
            _animationElements.Add(bagAwarded);

            player.Q<VisualElement>("RoundHighLight").style.display = DisplayStyle.Flex;

            _openTransition = new SequenceTransition(
                footer,
                new SequenceTransition.Step("translate", footer.name, OpenAnimation)
            );

            _closeTransition = new SequenceTransition(
                footer,
                new SequenceTransition.Step("translate", footer.name, CloseAnimation)
            );

            var winBanner = matchResult.Q<VisualElement>("ResultPanel");
            GolfUIToolKit.Utility.SetupYoyo(winBanner);

            var highlightEffect = player.Q<VisualElement>("HighlightEffect");
            highlightEffect.style.display = DisplayStyle.Flex;
            GolfUIToolKit.Utility.SetupYoyo(highlightEffect);
        }

        private void ConfigInfoPanel(LobbyProperty lobbyProperty)
        {
            var tourName = LocalizationManager.Instance.GetString(lobbyProperty.TourInfo.GetNameKey()).ToUpper();
            _tourLabel.ForEach(label => label.text = tourName);

            _courseNameLabel.text = lobbyProperty.CourseInfo.CurrentHole.Name.ToUpper();

            var localizePar = LocalizationManager.Instance.GetLocalizedString("game_play_par_num");
            if (localizePar == null)
            {
                _parLabel.text = "PAR " + lobbyProperty.CourseInfo.CurrentHole.Par;
            }
            else
            {
                localizePar.Arguments = new object[] { lobbyProperty.CourseInfo.CurrentHole.Par };
                _parLabel.text = localizePar.GetLocalizedString();
            }
        }

        private void ConfigurePlayer(VisualElement root, Ball ballPlayer, LobbyProperty lobbyProperty, bool isWinner)
        {
            var playerAvatar = root.Q<VisualElement>("Avatar");
            var playerName = root.Q<Label>("Name");
            var playerResult = root.Q<Label>("ShotResultLabel");
            var playerRankLabel = root.Q<Label>("RankLabel");
            var shotResultLabel = root.Q<Label>("ShotResultLabel");
            shotResultLabel.style.display = DisplayStyle.Flex;
            var entryFeeLabel = root.Q<Label>("EntryFeeLabel");

            var player = ballPlayer.localPlayer;

            var avatarSprite = player.UserAvt;
            playerAvatar.SetBackgroundImage(avatarSprite);

            //var playerRank = player.GetPlayerRankInfo().currentPoint;

            var playerRank = -1;

            foreach (var localPlayer in GlobalSO.GameplayBus.localLobby.LocalPlayers)
            {
                if (localPlayer.GetId() == player.GetId())
                {
                    playerRank = localPlayer.GetPlayerRankInfo().currentPoint;
                }
            }

            var rankConfig = GolfUtility.GetRankConfig(player.GetPlayerRankInfo().rankId,
                GlobalSO.RemoteConfigData.RankingConfigs);

            var winRankReward = rankConfig.headToHeadWin;
            var loseRankReward = rankConfig.headToHeadLose;
            var playerCurrentPoint = isWinner ? playerRank + winRankReward : playerRank - loseRankReward;
            playerCurrentPoint = playerCurrentPoint > 0 ? playerCurrentPoint : 0;
            playerRankLabel.text = playerCurrentPoint.ToString();
            if (!_data.isTieBreak)
            {
                shotResultLabel.text = ballPlayer.IsBallInHole
                    ? GolfUtility.GetPoint(lobbyProperty.CourseInfo.CurrentHole.Par, player.GetStrokeCount()).ToString()
                    : "";
            }
            else
            {
                var localDistance = UnitsConverter.MetersToFeet(_data.localTieBreakDistanceToHole);
                var opponentDistance = UnitsConverter.MetersToFeet(_data.oppoTieBreakDistanceToHole);
                if (isWinner)
                    shotResultLabel.text = localDistance.ToString(Mathf.Approximately((float)localDistance, (int)localDistance) ? "N0" : "F2") + " Feet To Hole";
                else
                    shotResultLabel.text = opponentDistance.ToString(Mathf.Approximately((float)opponentDistance, (int)opponentDistance) ? "N0" : "F2") + " Feet To Hole";
            }
            playerName.text = player.GetDisplayName();
            entryFeeLabel.text = isWinner ? (lobbyProperty.TourInfo.GetEntryFee() * 2).ToString() : "0";
        }

        private void OnReMatch()
        {
            _mediator.OnRematch(_data.CurrentLobbyProperty.TourInfo.GetEntryFee());
        }

        private void OnExit()
        {
            _exitBtn.clickable = new Clickable(() => { });
            _mediator.OnExit();
        }
    }

    public class MultiPlayerResultUIComponentMediator : UIComponentMediator
    {
        public MultiPlayerResultUIComponentMediator(UIComponentBase uIComponent) : base(uIComponent) { }
        public override void InitDependency(Dictionary<UIComponentEnum, UIComponentMediator> uiComponentMediators, Dictionary<EScreenEnum, UIScreenMediator> uiScreenMediators) { }
        public override void ComponentInitReference() { }
        private GameplayBus _bus;
        public override void ComponentInitListener()
        {
            _bus = GlobalSO.GameplayBus;
        }

        public override void ComponentDisposeReference() { }

        public override void ComponentDisposeListener()
        {
        }

        public void OnExit()
        {
            ActionDispatcher.Dispatch(new ExitGameplayAction());
        }

        public void OnRematch(int coinRequire)
        {
            var coin = UGSEconomy.CoinBalance;
            if (coin < coinRequire)
                NotEnoughResourcesShow();
            else
            {
                var localLobby = _bus.localLobby;
                var playerCount = localLobby.LocalPlayers.Count;

                if (playerCount < localLobby.GetMaxPlayerCount())
                    NotEnoughPlayerShow();
                else
                {
                    var localPlayer =
                        _bus.localLobby.LocalPlayers.Find((player => player.GetId() == _bus.localPlayer.GetId()));

                    // Reset Stroke
                    localPlayer.ResetPlayer();

                    localPlayer.SetUserStatus(PlayerStatus.ReadyForRematch);

                    _ = _bus.lobbyHandler.SyncPlayerData(localPlayer);
                }
            }
        }

        #region Popup Action

        private void NotEnoughResourcesShow()
        {
            // Not Enough Money State
            GeneralPopupContainer popupContainer = new GeneralPopupContainer
            (
                title: "Rematch Failed",
                description: "You or your opponent don’t have enough coins to rematch.",
                continueButtonText: "OK",
                continueAction: () =>
                {
                    MasterManager.Instance.HideUIComponent(UIComponentEnum.GeneralPopupUIComponent);
                }
            );

            MasterManager.Instance.OpenUIComponent(UIComponentEnum.GeneralPopupUIComponent, new object[] { popupContainer });
        }

        private void NotEnoughPlayerShow()
        {
            var localLobby = _bus.localLobby;
            var playerCount = localLobby.LocalPlayers.Count;

            // Not Enough Player To Rematch => Display Popup Notify
            if (playerCount < localLobby.GetMaxPlayerCount())
            {
                GeneralPopupContainer popupContainer = new GeneralPopupContainer
                (
                    title: "Unable to Play",
                    description: "Your opponent is unavailable to join at this time. Please try again later.",
                    continueButtonText: "OK",
                    continueAction: () =>
                    {
                        MasterManager.Instance.HideUIComponent(UIComponentEnum.GeneralPopupUIComponent);
                    }
                );

                MasterManager.Instance.OpenUIComponent(UIComponentEnum.GeneralPopupUIComponent, new object[] { popupContainer });
            }
        }

        #endregion

        public override ModuleProxy ComponentGetProxy()
        {
            throw new System.NotImplementedException();
        }

        public override ModuleManager ComponentGetManager()
        {
            throw new System.NotImplementedException();
        }
    }
}