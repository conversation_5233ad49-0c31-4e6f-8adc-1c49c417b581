using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using _Golf.Scripts.ScriptableObjects;
using Cysharp.Threading.Tasks;
using GolfGame;
using GolfPhysics;
using Kamgam.UIToolkitScrollViewPro;
using Unity.Mathematics;
using UnityEngine;
using UnityEngine.UIElements;

namespace _Golf.Scripts.UI
{
    public class MiniGameMatchingUIComponent : UIComponentBase<MiniGameMatchingUIComponentMediator>
    {
        private MiniGameMatchingUIComponentMediator _mediator;

        #region Effect
        private VisualElement _topMask;
        private VisualElement _bottomMask;
        private VisualElement _tourBG;
        #endregion

        #region Data field
        private VisualElement _playerZone;
        private VisualElement _holeSlectionZone;
        private ScrollViewPro _stageScrollView;
        private List<VisualElement> _playerElements;
        private List<bool> _isPlayerReady;

        private Label _titleText;
        private Label _subtitleText;
        private int _maxNumberOfPlayer;
        #endregion
        [SerializeField] private VisualTreeAsset _playerInfoElement;
        [SerializeField] private VisualTreeAsset _tourBannerElement;
        [SerializeField] private List<Sprite> _tourBannerImages;
        private List<string> _tourNames =
        new List<string>()
        {
            "GRAND HORIZON",
            "DRIVE TO VICTORY",
            "GALATIC MASTER",
            "HYPERFLUX SHOWDOWN",
            "PREMIER PATHWAY",
        };

        private Coroutine _autoCloseCoroutine;
        private float _cacheScrollViewWidth;
        private float _cacheScrollViewHeight;
        private List<VisualElement> _holeElements = new List<VisualElement>();
        private int _selectedHoleIndex = 0;
        private int _currentSelectHoleIndex;
        private bool _startScroll;
        private float _scrollSpeed = 1f;
        private float _slowScrollSpeed = 5f;
        private float _maxScrollSpeed = 20f;
        private const float MinStepScroll = 0.3f;
        private Coroutine _autoScrollCoroutine;

        private readonly float _tourBannerWidth = 575;
        private readonly float _snapDurationSec = 3f;

        public override UIComponentMediator CreateMediator()
        {
            _mediator = new MiniGameMatchingUIComponentMediator(this);
            return _mediator;
        }

        public override void Init()
        {
            _root = GetComponent<UIDocument>().rootVisualElement;

            _topMask = _root.Q<VisualElement>("TopMask");
            _bottomMask = _root.Q<VisualElement>("BottomMask");

            _playerZone = _root.Q<VisualElement>("PlayerZone");
            _playerElements = _playerZone.Children().ToList();

            _titleText = _root.Q<Label>("TitleText");
            _subtitleText = _root.Q<Label>("SubTitleText");

            _tourBG = _root.Q<VisualElement>("TourBG");

            InitHoleSelectionZone();
        }

        public override void OnOpen(object[] data = null)
        {
            _root.style.display = DisplayStyle.Flex;
            _topMask.AddToClassList("top-mask-in");
            _bottomMask.AddToClassList("bottom-mask-in");
            _tourBG.style.display = DisplayStyle.None;

            if (data != null && data.Length > 0)
            {
                var miniGame = (MiniGame)data[0];
                _maxNumberOfPlayer = 8;
                _isPlayerReady = new List<bool>(_maxNumberOfPlayer);
                for (int i = 0; i < _maxNumberOfPlayer; i++)
                {
                    _isPlayerReady.Add(false);
                }
                _subtitleText.text = $"{_isPlayerReady.Count}/{_maxNumberOfPlayer}";

                for (int i = 0; i < _playerElements.Count; i++)
                {
                    if (i < _maxNumberOfPlayer)
                    {
                        _playerElements[i].style.display = DisplayStyle.Flex;
                    }
                    else
                    {
                        _playerElements[i].style.display = DisplayStyle.None;
                    }
                }

                StartCoroutine(FakeSetUp());
            }
        }

        public override void OnHide(object[] data = null)
        {
            _root.style.display = DisplayStyle.None;
            if (_autoCloseCoroutine != null)
            {
                StopCoroutine(_autoCloseCoroutine);
                _autoCloseCoroutine = null;
            }

            if (_autoScrollCoroutine != null)
            {
                StopCoroutine(_autoScrollCoroutine);
                _autoScrollCoroutine = null;
            }

            _startScroll = false;
        }

        #region --- Testing function ----
        private IEnumerator AutoClose()
        {
            _topMask.RemoveFromClassList("top-mask-in");
            _bottomMask.RemoveFromClassList("bottom-mask-in");

            // Hide player zone and show hole selection
            _playerZone.style.display = DisplayStyle.None;
            ShowHoleSelectionZone();

            // The transition to next screen is now handled in AutoScrollHoles
            yield return null;
        }

        private IEnumerator FakeSetUp()
        {
            _playerZone.style.display = DisplayStyle.Flex;
            _holeSlectionZone.style.display = DisplayStyle.None;
            foreach (var element in _playerElements)
            {
                SetUpWaitingState(element);
            }

            SetUpPlayerInfo(0, GlobalSO.PlayerInfoSO.Info.DisplayName);

            for (int i = 1; i < _maxNumberOfPlayer; i++)
            {
                yield return new WaitForSeconds(0.5f);
                SetUpPlayerInfo(i, "Player_" + i);
            }

            yield return new WaitForSeconds(1);
        }
        #endregion

        private void SetUpWaitingState(VisualElement element)
        {
            element.Q<Label>().text = "WAITING...";
        }

        internal void SetUpPlayerInfo(int index, string playerName)
        {
            if (index < 0 || index >= _playerElements.Count)
            {
                return;
            }

            var element = _playerElements[index];
            element.Q<Label>().text = playerName;

            _isPlayerReady[index] = true;

            _subtitleText.text = $"{_isPlayerReady.Count(x => x)}/{_maxNumberOfPlayer}";

            if (CheckAllReady())
            {
                ShowHoleSelectionZone();
            }
        }

        private bool CheckAllReady()
        {
            return _isPlayerReady.All(x => x);
        }

        private void InitHoleSelectionZone()
        {
            _holeSlectionZone = _root.Q<VisualElement>("HoleSelectionZone");
            _stageScrollView = _root.Q<ScrollViewPro>("StageScrollView");

            _stageScrollView.RegisterCallback<GeometryChangedEvent>(evt =>
            {
                if (_stageScrollView.resolvedStyle.width > 0f)
                {
                    _cacheScrollViewWidth = _stageScrollView.resolvedStyle.width;
                    _cacheScrollViewHeight = _stageScrollView.resolvedStyle.height;
                }
            });

            _holeSlectionZone.style.display = DisplayStyle.None;
        }

        private void ShowHoleSelectionZone()
        {
            _topMask.RemoveFromClassList("top-mask-in");
            _bottomMask.RemoveFromClassList("bottom-mask-in");

            UnityEngine.Debug.Log("ShowHoleSelectionZone");
            _tourBG.style.display = DisplayStyle.Flex;
            _holeSlectionZone.style.display = DisplayStyle.Flex;
            _playerZone.style.display = DisplayStyle.None;
            SetupHoleElements();
            _stageScrollView.OnScrollToElement += ScrollToHole;
            _autoScrollCoroutine = StartCoroutine(FakeScrollStepTour());
        }

        private void SetupHoleElements()
        {
            _stageScrollView.Clear();
            _holeElements.Clear();
            for (int j = 0; j < 20; j++)
            {
                for (int i = 0; i < _tourBannerImages.Count; i++)
                {
                    var holeElement = _tourBannerElement.Instantiate();
                    holeElement.style.width = Length.Percent(100);
                    holeElement.style.height = 575;

                    var bannerImage = holeElement.Q<VisualElement>("Image");
                    bannerImage.style.backgroundImage = new StyleBackground(_tourBannerImages[i]);
                    holeElement.AddToClassList("init");

                    _stageScrollView.Add(holeElement);
                    _holeElements.Add(holeElement);
                }
            }

            _stageScrollView.RefreshAfterHierarchyChange();
        }

        private IEnumerator FakeScrollStepTour()
        {
            //wait for sync tour
            StartScrollWheel();
            yield return new WaitForSeconds(3f);

            
            var index = UnityEngine.Random.Range(0, _tourBannerImages.Count);
            //receive sync tour
            SetupChosenTour(index);
            StopScrollWheel();

            //get relative index in simulate scroll
            var simulateIndex = GetNearestSimulateIndex(index);
            
            // Final selection, snap to this tour
             SnapTo(simulateIndex);
            
            yield return new WaitForSeconds(_snapDurationSec);

            SetTourSelected();

            yield return new WaitForSeconds(3f);

            //Play game
            MasterManager.Instance.OnChangeScreen(EScreenEnum.MiniGameStage).Forget();
        }

        private void SetTourSelected()
        {
            _subtitleText.text = $"{_tourNames[_selectedHoleIndex]}";
            _stageScrollView.OnScrollToElement -= ScrollToHole;
        }

        private void StartScrollWheel()
        {
            _startScroll = true;
            _scrollSpeed = _maxScrollSpeed;

            _titleText.text = $"ROUND {UnityEngine.Random.Range(1, 10)}";
            _subtitleText.text = "SELECTING TOUR...";
        }

        private void StopScrollWheel()
        {
            _startScroll = false;
        }

        private int GetNearestSimulateIndex(int index)
        {
            int nearestIndex = _currentSelectHoleIndex / _tourBannerImages.Count;
            return nearestIndex * _tourBannerImages.Count + index;
        }

        internal void SetupChosenTour(int index)
        {
            _selectedHoleIndex = index;
        }

        private void SnapTo(int index)
        {
            float targetPosition = index * _tourBannerWidth - _tourBannerWidth / 2;
            _stageScrollView.verticalScroller.value = targetPosition;
            var cachedSnap = _stageScrollView.snapDurationSec;
            _stageScrollView.snapDurationSec = _snapDurationSec;

            ExcuteSnap(cachedSnap).Forget();
        }

        private async UniTask ExcuteSnap(float cachedSnapDuration)
        {
            _stageScrollView.Snap();
            await UniTask.Delay((int)(_snapDurationSec * 1000));
            _stageScrollView.snapDurationSec = cachedSnapDuration;
        }

        private void ScrollToHole(int index)
        {
            int realIndex = index + 1;
            _currentSelectHoleIndex = realIndex;
            if (realIndex < 0)
            {
                realIndex = 0;
            }
            else if (realIndex >= _holeElements.Count)
            {
                realIndex = _holeElements.Count - 1;
            }
            UnityEngine.Debug.Log("ScrollToHole: " + realIndex);

            // Highlight selected hole
            for (int i = 0; i < _holeElements.Count; i++)
            {
                var element = _holeElements[i];
                if (i == realIndex)
                {
                    element.AddToClassList("show");
                    element.Q<VisualElement>("Glow").style.display = DisplayStyle.Flex;
                    element.Q<VisualElement>("Border").style.display = DisplayStyle.Flex;
                }
                else
                {
                    element.RemoveFromClassList("show");
                    element.Q<VisualElement>("Glow").style.display = DisplayStyle.None;
                    element.Q<VisualElement>("Border").style.display = DisplayStyle.None;
                }
            }
        }
        
        private void FixedUpdate()
		{
			if(_startScroll)
				RandomAvatarScroller();
		}

		private void RandomAvatarScroller()
		{
			if (_scrollSpeed < MinStepScroll) return;

            _stageScrollView.scrollOffset += new Vector2(0, _scrollSpeed);			
		}
    }
}
