using System.Collections.Generic;
using _Golf.Scripts.Common;
using _Golf.Scripts.Networking;
using GolfGame;
using GolfGame.UIMediators.Base;
using TinyMessenger;

namespace _Golf.Scripts.UI
{
    public class MiniGameMatchingUIComponentMediator : UIComponentMediator
    {

        public MiniGameMatchingUIComponentMediator(UIComponentBase uIComponent) : base(uIComponent)
        {
            
        }

        public new MiniGameMatchingUIComponent UIComponent => (MiniGameMatchingUIComponent)_uiComponent;

        public override void ComponentInitListener()
        {

        }

        public override void ComponentInitReference()
        {
            
        }

        public override void InitDependency(Dictionary<UIComponentEnum, UIComponentMediator> uiComponentMediators, Dictionary<EScreenEnum, UIScreenMediator> uiScreenMediators)
        {
            
        }

        public void OnUpdateProgressUI(AssetsCheckAndDownload ctx)
        {
            //UIComponent.SetUpAssetLoading((int)(ctx.Progress * 100));
        }

        public override void ComponentDisposeListener()
        {
            
        }

        public override void ComponentDisposeReference()
        {
            
        }

        public override ModuleProxy ComponentGetProxy()
        {
            return null;
        }

        public override ModuleManager ComponentGetManager()
        {
            return null;
        }
    }
}