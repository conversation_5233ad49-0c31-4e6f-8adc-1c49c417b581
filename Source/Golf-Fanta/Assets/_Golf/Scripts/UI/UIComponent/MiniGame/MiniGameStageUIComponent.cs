using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using _Golf.Scripts.Common;
using _Golf.Scripts.Core;
using _Golf.Scripts.GlobalManagers;
using _Golf.Scripts.Networking;
using _Golf.Scripts.ScriptableObjects;
using Cysharp.Threading.Tasks;
using Game.UI;
using GolfGame;
using GolfPhysics;
using Kamgam.UIToolkitScrollViewPro;
using UnityEngine;
using UnityEngine.UIElements;

namespace _Golf.Scripts.UI
{
    public class RaceMiniGameStageUIComponent : UIComponentBase<RaceMiniGameStageUIComponentMediator>
    {
        private RaceMiniGameStageUIComponentMediator _mediator;
        private PlayerInfo _playerInfo;

        #region ----General UI----

        UIDocument document;
        VisualElement root;
        Button BackButton;
        private ScrollViewPro _stageScrollView;

        #endregion

        #region ----Local Var----
        [SerializeField] private List<MiniGame> _RaceMiniGames;
        private List<MiniGame> _activeRaceMiniGames;
        private float _cacheScrollViewWidth;
        private float _cacheScrollViewHeight;
        private List<VisualElement> _listStages = new List<VisualElement>();
        private int _currentSnapIndex = -1;
        private int _cachedStageIndex = 0;
        private bool _handleSnapEvent;

        private Coroutine _countDownCoroutine;
        private List<Label> _countDownLabels = new List<Label>();

        #endregion

        [SerializeField] private VisualTreeAsset _RaceMiniGameBanner;
        [SerializeField] private List<Sprite> _RaceMiniGameBannerImages;
        [SerializeField] private List<Gradient> _RaceMiniGameBannerGradients;

        public override UIComponentMediator CreateMediator()
        {
            _mediator = new RaceMiniGameStageUIComponentMediator(this);
            return _mediator;
        }

        public override void Init()
        {
            _playerInfo = GlobalSO.PlayerInfoSO.Info;
            _RaceMiniGameBannerGradients = new List<Gradient>(Constant.BannerGradients);

            document = GetComponent<UIDocument>();
            root = document.rootVisualElement;

            BackButton = root.Q<Button>("BackButton");

            _RaceMiniGames = new List<MiniGame>();
            _activeRaceMiniGames = new List<MiniGame>();

            _stageScrollView = root.Q<ScrollViewPro>("StageScrollView");
            _stageScrollView.RegisterCallback<GeometryChangedEvent>(evt =>
            {
                if (_stageScrollView.resolvedStyle.width > 0f)
                {
                    _cacheScrollViewWidth = _stageScrollView.resolvedStyle.width;
                    _cacheScrollViewHeight = _stageScrollView.resolvedStyle.height;
                }
            });

            root.style.display = DisplayStyle.None;
        }

        public override void OnOpen(object[] data = null)
        {
            root.style.display = DisplayStyle.Flex;
            _handleSnapEvent = false;

            _RaceMiniGames.Clear();
            _activeRaceMiniGames.Clear();
            foreach (var RaceMiniGame in GlobalSO.RemoteConfigData.MiniGameConfig.miniGames)
            {
                _RaceMiniGames.Add(new MiniGame(RaceMiniGame));
            }
            _activeRaceMiniGames = _RaceMiniGames.Where(RaceMiniGame => RaceMiniGame.IsEnabled).ToList();

            _stageScrollView.RegisterCallbackOnce<TransitionStartEvent>(evt =>
            {
                SetupRaceMiniGames();
                _stageScrollView.schedule.Execute(() =>
                {
                    SnapTo(_cachedStageIndex);
                }).StartingIn(10);
            });

            BackButton.clickable = new(() =>
            {
                RaceMiniGameStageScreenMediator.ClickPlayMiniGameEvent.ClearReference();
                MasterManager.Instance.OnChangeScreen(EScreenEnum.GameMode).Forget();
            });

            _stageScrollView.OnScrollToElement += UpdateUI;

            _stageScrollView.AddToClassList("show");
        }

        private void SetupRaceMiniGames()
        {
            _stageScrollView.Clear();
            _listStages.Clear();
            _countDownLabels.Clear();

            VisualElement leftPadding = new VisualElement();
            leftPadding.style.width = _cacheScrollViewWidth * Constant.ContentWitdhSizeSmoothDrag / 2;
            _stageScrollView.Add(leftPadding);

            for (int i = 0; i < _activeRaceMiniGames.Count; i++)
            {
                var RaceMiniGame = _RaceMiniGameBanner.Instantiate();
                RaceMiniGame.style.width = _cacheScrollViewWidth * Constant.ContentWitdhSizeSmoothDrag;
                RaceMiniGame.style.height = _cacheScrollViewHeight;
                RaceMiniGame.style.bottom = 0;
                RaceMiniGame.style.flexGrow = 0;

                _stageScrollView.Add(RaceMiniGame);
                _listStages.Add(RaceMiniGame);
                int index = i;
                SetUpContentUI(index);
            }

            VisualElement internalPadding = new VisualElement();
            internalPadding.style.width = _cacheScrollViewWidth * Constant.ContentWitdhSizeSmoothDrag / 2;
            _stageScrollView.Add(internalPadding);

            SetUpRPass();
        }

        private void SnapTo(int index)
        {
            _handleSnapEvent = true;

            float targetPosition = index * _cacheScrollViewWidth * Constant.ContentWitdhSizeSmoothDrag;
            _stageScrollView.horizontalScroller.value = targetPosition;
            var cachedSnap = _stageScrollView.snapDurationSec;
            _stageScrollView.snapDurationSec = 0.01f;

            ExcuteSnap(cachedSnap).Forget();
        }

        private async UniTask ExcuteSnap(float cachedSnapDuration)
        {
            _stageScrollView.Snap();
            await UniTask.DelayFrame(2);
            _stageScrollView.snapDurationSec = cachedSnapDuration;
            UpdateUI(_cachedStageIndex);
        }

        private void SetUpContentUI(int index)
        {
            var RaceMiniGame = _activeRaceMiniGames[index];

            var bannerImage = _listStages[index].Q<VisualElement>("BannerImage");
            var playerNumText = _listStages[index].Q<Label>("PlayerNumText");
            var WhiteBG = _listStages[index].Q<VisualElement>("WhiteBG");
            var progressText = _listStages[index].Q<Label>("ProgressText");

            var watchAdsButton = _listStages[index].Q<Button>("WatchAdsButton");
            var playButton = _listStages[index].Q<Button>("PlayButton");

            playButton.clickable = new Clickable(() =>
            {
                MasterManager.Instance.OnChangeScreen(EScreenEnum.MatchingMiniGame, new object[] { RaceMiniGame }).Forget();
            });


            bannerImage.style.backgroundImage = _RaceMiniGameBannerImages[index].texture;
            playerNumText.text = $"NUMBER OF PLAYERS  {GolfUIToolKit.Utility.MakeTextColored(RaceMiniGame.NumberOfPlayer.ToString(), Constant.HighlightTextColor)}";

            var state = GetRaceMiniGameState(RaceMiniGame);
            float cost = 0f;

            bool canWatchAds = state.HasFlag(RaceMiniGameState.AbleToWatchAds);
            watchAdsButton.style.display = canWatchAds ? DisplayStyle.Flex : DisplayStyle.None;

            bool needToBuyRPass = state.HasFlag(RaceMiniGameState.NeedToBuyRPass);
            playButton.Q<Label>().text = LocalizationManager.Instance.GetString(needToBuyRPass ? "common_buy" : "common_play");

            bool isFreeToPlay = state.HasFlag(RaceMiniGameState.FreeToPlay);
            var passIcon = playButton.Q<VisualElement>("PassIcon");
            if (isFreeToPlay)
            {
                playButton.Q<Label>().text = "PLAY NOW";
                progressText.text = $"FREE PLAY TODAY: {UGSEconomy.FreeRPassBalance}/{2}";
                passIcon.style.display = DisplayStyle.None;
            }
            else
            {
                _countDownLabels.Add(progressText);
                if (_countDownCoroutine == null)
                {
                    _countDownCoroutine = StartCoroutine(CountDown());
                }
   
                passIcon.style.display = DisplayStyle.Flex;
            }

            bool isLocked = state.HasFlag(RaceMiniGameState.Locked);
            if (isLocked)
            {
                GolfUIToolKit.Utility.SetButtonValue(playButton, null, GolfUIToolKit.Utility.ButtonColor.Grey);
                GolfUIToolKit.Utility.SetButtonValue(watchAdsButton, null, GolfUIToolKit.Utility.ButtonColor.Grey);
            }
            else
            {
                GolfUIToolKit.Utility.SetButtonValue(playButton, null, GolfUIToolKit.Utility.ButtonColor.Yellow);
                GolfUIToolKit.Utility.SetButtonValue(watchAdsButton, null, GolfUIToolKit.Utility.ButtonColor.Blue);
            }


            // playButton.clickable = new Clickable(() => { });
            // if (IsAbleToPlay(state))
            // {
            //     int currentIndex = index;

            //     playButton.clicked += () =>
            //     {
            //         if (UGSEconomy.CoinBalance < cost)
            //         {
            //             var container = new GeneralPopupContainer(
            //                 "Notification",
            //                 "Unfortunately, you do not have enough Entry Fee.",
            //                 "OK",
            //                 () => MasterManager.Instance.HideUIComponent(UIComponentEnum.GeneralPopupUIComponent)
            //             );

            //             MasterManager.Instance.OpenUIComponent(UIComponentEnum.GeneralPopupUIComponent, new object[] { container }).Forget();
            //         }
            //         else
            //         {
            //             if (RaceMiniGameStageScreenMediator.ClickPlayMiniGameEvent.MiniGame != null)
            //             {
            //                 return;
            //             }

            //             var actionRaceMiniGameRef = RaceMiniGameStageScreenMediator.ClickPlayMiniGameEvent;
            //             actionRaceMiniGameRef.MiniGame = RaceMiniGame;
            //             actionRaceMiniGameRef.OnLoadingProgress = (progressValue) =>
            //             {
            //                 SetUpAssetLoading(progressValue, currentIndex);
            //             };
            //             // ActionDispatcher.Dispatch(actionRaceMiniGameRef);
            //         }
            //     };
            // }
        }

        private IEnumerator CountDown()
        {
            DateTime now = DateTime.UtcNow;
            DateTime nextReset = now.Date.AddDays(1);
            var secondsLeft = nextReset.Subtract(now).TotalSeconds;

            while (secondsLeft > 0)
            {
                secondsLeft = nextReset.Subtract(DateTime.UtcNow).TotalSeconds;
                //to hour, minute and secound
                int totalMinutes = (int)(secondsLeft / 60);
                int hours = totalMinutes / 60;
                int minutes = totalMinutes % 60;
                int seconds = (int)secondsLeft % 60;

                for (int i = 0; i < _countDownLabels.Count; i++)
                {
                    _countDownLabels[i].text = $"Refresh in: {hours}h {minutes}m {seconds}s".ToUpper();
                }

                yield return new WaitForSeconds(1);
            }

            RefreshUI();
        }

        private void UpdateUI(int index)
        {
            if (index == _currentSnapIndex || !_handleSnapEvent)
            {
                return;
            }

            if (index < 0)
            {
                index = 0;
            }
            else if (index >= _listStages.Count)
            {
                index = _listStages.Count - 1;
            }

            //Animate the stage
            if (index == _currentSnapIndex)
            {
                return;
            }

            _currentSnapIndex = index;

            for (int i = 0; i < index; i++)
            {
                var element = _listStages[i];
                element.pickingMode = PickingMode.Ignore;
                var bG = element.Q<VisualElement>("BG");

                bG.AddToClassList("stage-hide-left");
                bG.RemoveFromClassList("stage-hide-right");
                bG.RemoveFromClassList("stage-show");
            }

            for (int i = index + 1; i < _listStages.Count; i++)
            {
                var element = _listStages[i];
                element.pickingMode = PickingMode.Ignore;
                var bG = element.Q<VisualElement>("BG");

                bG.AddToClassList("stage-hide-right");
                bG.RemoveFromClassList("stage-hide-left");
                bG.RemoveFromClassList("stage-show");
            }

            var focusElement = _listStages[index];
            var focusBG = focusElement.Q<VisualElement>("BG");

            focusElement.pickingMode = PickingMode.Position;
            focusBG.RemoveFromClassList("stage-hide-left");
            focusBG.RemoveFromClassList("stage-hide-right");
            focusBG.AddToClassList("stage-show");
        }

        private void SetUpRPass()
        {
            var ticket = root.Q<Label>("ReplayTicketText");

            var rRpass = UGSEconomy.RPassBalance;
            if (rRpass <= 0)
            {
                ticket.style.color = Constant.WarningTextColor;
            }
            else
            {
                ticket.style.color = Constant.DefaultTextColor;
            }

            ticket.text = rRpass.ToString();
            
        }

        bool IsAbleToPlay(RaceMiniGameState state)
        {
            // return state == RaceMiniGameState.Available;
            return true;
        }

        internal void UpdateGold(long balance)
        {
            var goldText = root.Q<Label>("GoldAmount");
            goldText.text = balance.ToString();
        }

        internal void UpdateGem(long balance)
        {
            var gemText = root.Q<Label>("GemAmount");
            gemText.text = balance.ToString();
        }

        public void SetUpAssetLoading(float loadProgress, int index)
        {
            var loadingPanel = _listStages[index].Q<VisualElement>("LoadingAsset");
            var buttonContainer = _listStages[index].Q<VisualElement>("ButtonContainer");
            var loadingBarUXML = loadingPanel.Q<LoadingBarUXML>();

            if (loadProgress < 0f)
            {
                loadingPanel.style.display = DisplayStyle.None;
                buttonContainer.style.display = DisplayStyle.Flex;
                return;
            }

            buttonContainer.style.display = DisplayStyle.None;
            loadingPanel.style.display = DisplayStyle.Flex;

            loadingBarUXML.SetValue(loadProgress);
            UnityEngine.Debug.Log("Loading: " + loadProgress * 100 + "%");
        }

        public void RefreshUI()
        {
            for (int i = 0; i < _listStages.Count; i++)
            {
                SetUpContentUI(i);
            }

            UpdateUI(_currentSnapIndex);
        }

        private RaceMiniGameState GetRaceMiniGameState(MiniGame RaceMiniGame)
        {
            RaceMiniGameState state = RaceMiniGameState.None;
            if (UGSEconomy.FreeRPassBalance > 0)
            {
                state |= RaceMiniGameState.FreeToPlay;
            }

            if (GlobalSO.RemoteConfigData.MiniGameConfig.MaxWatchingAdsPerDay > 0)
            {
                state |= RaceMiniGameState.AbleToWatchAds;
            }

            if (RaceMiniGame.IsLock)
            {
                state |= RaceMiniGameState.Locked;
            }

            if (UGSEconomy.RPassBalance <= 0)
            {
                state |= RaceMiniGameState.NeedToBuyRPass;
            }

            return state;
        }

        public void DisableBackButton()
        {
            BackButton.clickable = null;
        }

        public override void OnHide(object[] data = null)
        {
            _cachedStageIndex = _currentSnapIndex;
            _currentSnapIndex = -1;
            root.style.display = DisplayStyle.None;
            _stageScrollView.OnScrollToElement -= UpdateUI;

            _stageScrollView.RemoveFromClassList("show");
            if (_countDownCoroutine != null)
            {
                StopCoroutine(_countDownCoroutine);
                _countDownCoroutine = null;
            }
        }
    }

    [Flags]
    internal enum RaceMiniGameState
    {
        None = 0,
        FreeToPlay = 1,
        AbleToWatchAds = 2,
        Locked = 4,
        NeedToBuyRPass = 8
    }
}