using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using _Golf.Scripts.Common;
using _Golf.Scripts.GlobalManagers;
using _Golf.Scripts.ScriptableObjects;
using Cysharp.Threading.Tasks;
using GolfGame;
using UnityEngine;
using UnityEngine.Localization;
using UnityEngine.UIElements;

namespace _Golf.Scripts.UI
{
    public class GameModeUIComponent : UIComponentBase<GameModeUIComponentMediator>
    {
        private ChangeTitleAction _changeTitleAction = new("GAME MODE");
        private GameModeUIComponentMediator _mediator;
        private List<Tournament> _tournaments;
        private IEnumerator _updateTournamentTimeCoroutine;
        private VisualElement _root;
        private VisualElement _mainTab;
        private VisualElement _miniGameTab;

        public override UIComponentMediator CreateMediator()
        {
            _mediator = new GameModeUIComponentMediator(this);
            return _mediator;
        }

        public override void OnHide(object[] data = null)
        {
            if (_updateTournamentTimeCoroutine != null)
            {
                StopCoroutine(_updateTournamentTimeCoroutine);
                _updateTournamentTimeCoroutine = null;
            }
            GetComponent<UIDocument>().rootVisualElement.style.visibility = Visibility.Hidden;
        }

        public override void OnOpen(object[] data = null)
        {
            SetupMainTab();
        }

        public override void Init()
        {
            _root = GetComponent<UIDocument>().rootVisualElement;

            var firstStageBtn = _root.Q<Button>("HeadToHeadButton");
            firstStageBtn.clicked += () => { MasterManager.Instance.OnChangeScreen(EScreenEnum.HeadToHeadStage).Forget(); };

            var secondStageBtn = _root.Q<Button>("ClosestToPinButton");
            secondStageBtn.clicked += () => { MasterManager.Instance.OnChangeScreen(EScreenEnum.ClosestToPinStage).Forget(); };

            var raceChallengeBtn = _root.Q<Button>("RaceChallengeButton");
            raceChallengeBtn.clicked += () => { MasterManager.Instance.OnChangeScreen(EScreenEnum.MiniGameStage).Forget(); };

            var miniGameBtn = _root.Q<Button>("MiniGameButton");
            miniGameBtn.clicked += () =>
            {
                SetupMiniGameTab();
            };

            _mainTab = _root.Q<VisualElement>("MainTab");
            _miniGameTab = _root.Q<VisualElement>("MiniGameTab");
        }

        private IEnumerator UpdateTournamentTime(Label label, DateTime endTime)
        {
            var timeLeft = endTime - DateTime.UtcNow;
            var binding = label.GetBinding("text") as LocalizedString;

            while (timeLeft.TotalSeconds > 0)
            {
                if (timeLeft.TotalDays > 1)
                    binding.Arguments = new object[] { $"{timeLeft.Days}d {timeLeft.Hours}h {timeLeft.Minutes}m {timeLeft.Seconds}s" };
                else
                    binding.Arguments = new object[] { $"{timeLeft.Hours}h {timeLeft.Minutes}m {timeLeft.Seconds}s" };

                binding.RefreshString();
                yield return new WaitForSeconds(1);
                timeLeft = endTime - DateTime.UtcNow;
            }

            //Refresh the screen
            MasterManager.Instance.OnChangeScreen(EScreenEnum.GameMode).Forget();
        }

        private IEnumerator UpdateRewardsTime(Label label, DateTime endTime)
        {
            var rewardEndTime = endTime.AddSeconds(GlobalSO.RemoteConfigData.TournamentConfig.receiveRewardDurationSecond);
            var timeLeft = rewardEndTime - DateTime.UtcNow;
            var binding = label.GetBinding("text") as LocalizedString;
            while (timeLeft.TotalSeconds > 0)
            {
                if (timeLeft.TotalDays > 1)
                    binding.Arguments = new object[] { $"{timeLeft.Days}d {timeLeft.Hours}h {timeLeft.Minutes}m {timeLeft.Seconds}s" };
                else
                    binding.Arguments = new object[] { $"{timeLeft.Hours}h {timeLeft.Minutes}m {timeLeft.Seconds}s" };

                binding.RefreshString();
                yield return new WaitForSeconds(1);
                timeLeft = rewardEndTime - DateTime.UtcNow;
            }

            //Refresh the screen
            MasterManager.Instance.OnChangeScreen(EScreenEnum.GameMode).Forget();
        }

        private void SetupMainTab()
        {
            var BackButton = _root.Q<Button>("BackButton");
            BackButton.clickable = new(() => { MasterManager.Instance.OnChangeScreen(EScreenEnum.Home).Forget(); });

            var multiplayerBtn = _root.Q<Button>("TournamentsButton");
            multiplayerBtn.clickable = new Clickable(() => { });

            _tournaments = new List<Tournament>();
            foreach (var tournament in GlobalSO.RemoteConfigData.TournamentConfig.tournamentTours)
            {
                _tournaments.Add(new Tournament(tournament));
            }
            _tournaments = _tournaments.Where(tournament => tournament.IsEnabled).ToList();

            bool canShowTournament = false;

            var playerRank = GlobalSO.PlayerInfoSO.Info.H2hRank;

            var minElo = _tournaments.Min(tournament => tournament.RequiredElo);

            var tournamentEndTime = _tournaments.Max(tournament => tournament.EndTime);

            var timeLeft = tournamentEndTime - DateTime.UtcNow;

            if (_tournaments.Count > 0 && playerRank.Elo >= minElo)
            {
                canShowTournament = true;
            }
            else
            {
                canShowTournament = false;
            }

            if (canShowTournament && timeLeft.TotalSeconds > 0)
            {
                // multiplayerBtn.style.backgroundImage = new StyleBackground(ResourcesManager.Instance.GetSpriteFromAtlas("TournamentMode"));
                multiplayerBtn.Q<VisualElement>("BGImage").style.backgroundImage = new StyleBackground(ResourcesManager.Instance.GetSpriteFromAtlas("TournamentMode"));
                multiplayerBtn.clicked += () => { MasterManager.Instance.OnChangeScreen(EScreenEnum.TournamentsEntry).Forget(); };

                var tournamentEndTimeLocalize = LocalizationManager.Instance.GetLocalizedString("game_mode_tournament_end_time");
                tournamentEndTimeLocalize.Arguments = new object[] { timeLeft.ToString(@"hh\:mm\:ss") };
                multiplayerBtn.Q<Label>("TournamentTimeText").SetBinding("text", tournamentEndTimeLocalize);
                // multiplayerBtn.Q<Label>("TournamentTimeText").text = "All tournament Ends: " + timeLeft.ToString(@"hh\:mm\:ss");
                _updateTournamentTimeCoroutine = UpdateTournamentTime(multiplayerBtn.Q<Label>("TournamentTimeText"), tournamentEndTime);

                StartCoroutine(_updateTournamentTimeCoroutine);
            }
            else if (canShowTournament && timeLeft.TotalSeconds <= 0 && (timeLeft.TotalSeconds + GlobalSO.RemoteConfigData.TournamentConfig.receiveRewardDurationSecond) > 0)
            {
                // multiplayerBtn.style.backgroundImage = new StyleBackground(ResourcesManager.Instance.GetSpriteFromAtlas("TournamentMode"));
                multiplayerBtn.Q<VisualElement>("BGImage").style.backgroundImage = new StyleBackground(ResourcesManager.Instance.GetSpriteFromAtlas("TournamentMode"));
                multiplayerBtn.clicked += () => { MasterManager.Instance.OnChangeScreen(EScreenEnum.TournamentsEntry).Forget(); };

                var rewardLocalize = LocalizationManager.Instance.GetLocalizedString("game_mode_tournament_reward_expire_time");
                rewardLocalize.Arguments = new object[] { (timeLeft.TotalSeconds + GlobalSO.RemoteConfigData.TournamentConfig.receiveRewardDurationSecond).ToString(@"hh\:mm\:ss") };
                multiplayerBtn.Q<Label>("TournamentTimeText").SetBinding("text", rewardLocalize);

                _updateTournamentTimeCoroutine = UpdateRewardsTime(multiplayerBtn.Q<Label>("TournamentTimeText"), tournamentEndTime);
                StartCoroutine(_updateTournamentTimeCoroutine);
            }
            else if (canShowTournament && timeLeft.TotalSeconds <= 0)
            {
                // multiplayerBtn.style.backgroundImage = new StyleBackground(ResourcesManager.Instance.GetSpriteFromAtlas("TournamentLockMode"));
                multiplayerBtn.Q<VisualElement>("BGImage").style.backgroundImage = new StyleBackground(ResourcesManager.Instance.GetSpriteFromAtlas("TournamentLockMode"));
                multiplayerBtn.Q<Label>("TournamentTimeText").text = LocalizationManager.Instance.GetString("game_mode_tournament_ended");
            }
            else
            {
                // multiplayerBtn.style.backgroundImage = new StyleBackground(ResourcesManager.Instance.GetSpriteFromAtlas("TournamentLockMode"));
                multiplayerBtn.Q<VisualElement>("BGImage").style.backgroundImage = new StyleBackground(ResourcesManager.Instance.GetSpriteFromAtlas("TournamentLockMode"));
                var tournamentTimeLocalize = LocalizationManager.Instance.GetLocalizedString("game_mode_tournament_required");
                tournamentTimeLocalize.Arguments = new object[] { minElo };
                multiplayerBtn.Q<Label>("TournamentTimeText").text = tournamentTimeLocalize.GetLocalizedString();
            }

            GetComponent<UIDocument>().rootVisualElement.style.visibility = Visibility.Visible;

            _mainTab.style.display = DisplayStyle.Flex;
            _miniGameTab.style.display = DisplayStyle.None;

            _changeTitleAction.title = LocalizationManager.Instance.GetString("title_game_mode");
            ActionDispatcher.Dispatch(_changeTitleAction);
        }

        private void SetupMiniGameTab()
        {
            var BackButton = _root.Q<Button>("BackButton");
            BackButton.clickable = new(() => { SetupMainTab(); });

            _miniGameTab.style.display = DisplayStyle.Flex;
            _mainTab.style.display = DisplayStyle.None;

            _changeTitleAction.title = LocalizationManager.Instance.GetString("title_mini_game");
            ActionDispatcher.Dispatch(_changeTitleAction);
        }
    }
}
