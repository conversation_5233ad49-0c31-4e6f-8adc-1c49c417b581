using System.Collections.Generic;
using System.Linq;
using System.Threading;
using _Golf.Scripts.API.Models.CloestToPin;
using _Golf.Scripts.Common;
using _Golf.Scripts.Core;
using _Golf.Scripts.Networking;
using _Golf.Scripts.Networking.Courses;
using _Golf.Scripts.ScriptableObjects;
using _Golf.Scripts.ScriptableObjects.Tours;
using _Golf.Scripts.ScriptableObjects.ShopConfig;
using _Golf.Scripts.UI.Screen;
using Cysharp.Threading.Tasks;
using Game.UI;
using GolfGame;
using GolfPhysics;
using Kamgam.UIToolkitScrollViewPro;
using Kamgam.UIToolkitVisualScripting;
using TinyMessenger;
using UnityEngine;
using UnityEngine.UIElements;
using AYellowpaper.SerializedCollections;
using GolfUIToolKit;

namespace _Golf.Scripts.UI
{
    public class ClosestToPinStageUIComponent : UIComponentBase<ClosestToPinStageUIComponentMediator>
    {
        //[SerializeField] private SinglePlayProperties _properties;
        private ClosestToPinCourseList _coursesConfig;
        private PlayerInfo _playerInfo;
        private ClosestToPinConfigSO _closestToPinConfig;
        private ClosestToPinStageUIComponentMediator _mediator;
        List<VisualElement> _cachedPlayButton = new List<VisualElement>();

        #region ----General UI----
        UIDocument _document;
        VisualElement _panel;
        VisualElement _root;
        Button _backButton;
        // private Label _goldText;
        // private Label _gemText;
        private ScrollViewPro _scrollViewPro;

        private Button _playButton;
        VisualElement _loadingPanel;
        VisualElement _finishLoading;
        VisualElement _loading;
        LoadingBarUXML _loadingBarUXML;
        Label _loadingText;
        #endregion

        #region ----UI Element----
        [SerializeField] private VisualTreeAsset unavailableVisualTree;
        [SerializeField] private VisualTreeAsset availableVisualTree;
        private List<VisualElement> _listCources = new List<VisualElement>();
        #endregion

        #region Resources
        [SerializeField] private List<Sprite> _bg;
        [SerializeField] private List<Sprite> _bgLock;
        [SerializeField] private SerializedDictionary<string, Sprite> bgSpriteMap;
        [SerializeField] private SerializedDictionary<string, Sprite> bgLockSpriteMap;
        #endregion

        #region ----Calculate Data----
        List<ClosestToPinAvailableCourse> stageInfos;
        private int[] currentMode = new int[5] { 0, 0, 0, 0, 0 };
        private float _cacheScrollViewWidth;
        private int _currentSnapIndex = -1;
        private int _cachedStageIndex = 0;
        private bool _handleSnapEvent;
        #endregion

        private TinyMessageSubscriptionToken _onPlayerInfoChangedToken;
        private CancellationTokenSource _cancellationTokenSource;

        public override UIComponentMediator CreateMediator()
        {
            _mediator = new ClosestToPinStageUIComponentMediator(this);
            return _mediator;
        }

        public override void OnHide(object[] data = null)
        {
            _scrollViewPro.Clear();
            _listCources.Clear();
            ActionDispatcher.Unbind(_onPlayerInfoChangedToken);
            _panel.style.visibility = Visibility.Hidden;
            _document.rootVisualElement.style.display = DisplayStyle.None;
            _scrollViewPro.OnScrollToElement -= UpdateUI;
            _cachedStageIndex = _currentSnapIndex;
            _currentSnapIndex = -1;

            _scrollViewPro.RemoveFromClassList("show");
        }

        public override void OnOpen(object[] data = null)
        {
            _onPlayerInfoChangedToken = ActionDispatcher.Bind<UpdatePlayerInfoAction>(GetStageInfos);
            _listCources?.Clear();

            _panel.style.visibility = Visibility.Visible;
            _document.rootVisualElement.style.display = DisplayStyle.Flex;
            _scrollViewPro.OnScrollToElement += UpdateUI;

            _handleSnapEvent = false;
            _scrollViewPro.AddToClassList("show");
            _scrollViewPro.RegisterCallbackOnce<TransitionStartEvent>(evt =>
            {
                InitThread();
                _scrollViewPro.schedule.Execute(() =>
                {
                    SnapTo(_cachedStageIndex);
                }).StartingIn(10);
            });
        }

        private void SnapTo(int index)
        {
            _handleSnapEvent = true;

            float targetPosition = index * _cacheScrollViewWidth * Constant.ContentWitdhSizeSmoothDrag;
            _scrollViewPro.horizontalScroller.value = targetPosition;
            var cachedSnap = _scrollViewPro.snapDurationSec;
            _scrollViewPro.snapDurationSec = 0.01f;

            ExcuteSnap(cachedSnap).Forget();
        }

        private async UniTask ExcuteSnap(float cachedSnapDuration)
        {
            _scrollViewPro.Snap();
            await UniTask.DelayFrame(2);
            _scrollViewPro.snapDurationSec = cachedSnapDuration;
            UpdateUI(_cachedStageIndex);
        }

        private void UpdateUI(int index)
        {
            if (index == _currentSnapIndex || !_handleSnapEvent)
            {
                return;
            }

            if (index < 0)
            {
                index = 0;
            }

            if (index >= _listCources.Count)
            {
                index = _listCources.Count - 1;
            }

            _currentSnapIndex = index;

            for (int i = 0; i < index; i++)
            {
                var element = _listCources[i];
                element.pickingMode = PickingMode.Ignore;
                var content = element.Q<VisualElement>("Content"); ;
                content.AddToClassList("stage-hide-left");
                content.RemoveFromClassList("stage-hide-right");
                content.RemoveFromClassList("stage-show");
            }

            for (int i = index + 1; i < _listCources.Count; i++)
            {
                var element = _listCources[i];
                element.pickingMode = PickingMode.Ignore;
                var content = element.Q<VisualElement>("Content");
                content.AddToClassList("stage-hide-right");
                content.RemoveFromClassList("stage-hide-left");
                content.RemoveFromClassList("stage-show");
            }

            var focusElement = _listCources[index];
            focusElement.pickingMode = PickingMode.Position;
            var focusContent = focusElement.Q<VisualElement>("Content");
            focusContent.RemoveFromClassList("stage-hide-left");
            focusContent.RemoveFromClassList("stage-hide-right");
            focusContent.AddToClassList("stage-show");
        }

        public override void Init()
        {
            _closestToPinConfig = GlobalSO.RemoteConfigData.ClosestToPinConfig;
            _coursesConfig = GlobalSO.RemoteConfigData.ClosestToPinHoleList;
            _playerInfo = GlobalSO.PlayerInfoSO.Info;

            _document = GetComponent<UIDocument>();
            _root = _document.rootVisualElement;

            _panel = _root.Q<VisualElement>("ClosestToPinStagePanel");

            _scrollViewPro = _root.Q<ScrollViewPro>("StageScrollView");
            _scrollViewPro.RegisterCallback<GeometryChangedEvent>(evt =>
            {
                if (_scrollViewPro.resolvedStyle.width > 0f)
                {
                    _cacheScrollViewWidth = _scrollViewPro.resolvedStyle.width;
                }
            });

        }

        private void InitThread()
        {

            stageInfos = new List<ClosestToPinAvailableCourse>();

            currentMode = new int[5] { 0, 0, 0, 0, 0 };
            _closestToPinConfig.CurrentMode = 0;

            _backButton = _root.Q<Button>("BackButton");
            _backButton.pickingMode = PickingMode.Position;
            _backButton.style.unityBackgroundImageTintColor = Color.white;
            _backButton.clickable = new Clickable(OnBackButton);


            _scrollViewPro.Clear();
            _cachedPlayButton.Clear();

            GetStageInfos();

            UpdateUI(_cachedStageIndex);
        }

        private void OnBackButton()
        {
            MasterManager.Instance.OnChangeScreen(EScreenEnum.GameMode);
        }

        private int CheckUnClockMode()
        {
            return (int)_playerInfo.H2hRank.MajorRank;
        }


        private void GetStageInfos(UpdatePlayerInfoAction action = null)
        {
            if (stageInfos != null)
                stageInfos.Clear();

            _scrollViewPro.Clear();

            var unLockMode = false;

            VisualElement leftPadding = new VisualElement();
            leftPadding.style.width = _cacheScrollViewWidth * Constant.ContentWitdhSizeSmoothDrag / 2f;
            _scrollViewPro.Add(leftPadding);

            var tours = _closestToPinConfig.closesToPinConditionConfigData.tours;
            var playerInfo = GlobalSO.PlayerInfoSO.Info;

            for (var index = 0; index < tours.Count; index++)
            {
                if (tours[index].GetRequiredElo() <= playerInfo.H2hRank.Elo)
                {
                    unLockMode = true;
                }

                if (unLockMode)
                {
                    var panelItem = availableVisualTree;

                    VisualElement item = panelItem.Instantiate();

                    item.style.width = _cacheScrollViewWidth * Constant.ContentWitdhSizeSmoothDrag;
                    item.style.height = Length.Percent(100);


                    var bannerImage = item.Q<VisualElement>("CloseToPinContent");
                    if (bgSpriteMap.ContainsKey(tours[index].GetBannerAddress()))
                    {
                        bannerImage.style.backgroundImage = new StyleBackground(bgSpriteMap[tours[index].GetBannerAddress()]);
                    }
                    else
                    {
                        bannerImage.style.backgroundImage = new StyleBackground(_bg[0]);
                    }

                    var goldBtn = item.Q<Button>("goldBtn");
                    var fragmentBtn = item.Q<Button>("fragmentBtn");
                    var enterBtn = item.Q<Button>("enterBtn");
                    var goldPrize = item.Q<VisualElement>("GoldPrize");
                    var fragmentPrize = item.Q<VisualElement>("FragmentPize");
                    var windText = item.Q<Label>("WindText");
                    var needleText = item.Q<Label>("NeedleText");
                    _cachedPlayButton.Add(enterBtn);

                    windText.DisplayWindSpeed(tours[index].GetWindSpeedMin(), tours[index].GetWindSpeedMax());
                    needleText.text = $"{playerInfo.H2hRank.Info.needleSpeed:F1} %";

                    // Display Prize 
                    item.Q<Label>("CoinQuantity").text = tours[index].GetCoinDataByRank(playerInfo.H2hRank).rewards[0].reward.ToString();
                    item.Q<Label>("FragmentQuantity").text = tours[index].GetFragmentDataByRank(playerInfo.H2hRank).rewards[0].reward.ToString();

                    // Init Display Button
                    fragmentBtn.RemoveFromClassList("btn-active");
                    fragmentBtn.RemoveFromClassList("btn-inactive");
                    fragmentBtn.AddToClassList("btn-inactive");

                    goldBtn.RemoveFromClassList("btn-active");
                    goldBtn.RemoveFromClassList("btn-inactive");
                    goldBtn.AddToClassList("btn-active");

                    // Button Listener
                    var localIndex = index;
                    enterBtn.clicked += () =>
                    {
                        _closestToPinConfig.CurrentMode = currentMode[localIndex];
                        _closestToPinConfig.CurrentDifficult = localIndex;
                        if (currentMode[localIndex] == 0)
                        {
                            _closestToPinConfig.CurrentConfig = tours[localIndex].GetCoinDataByRank(GlobalSO.PlayerInfoSO.Info.H2hRank);
                        }
                        else
                        {
                            _closestToPinConfig.CurrentConfig = tours[localIndex].GetFragmentDataByRank(GlobalSO.PlayerInfoSO.Info.H2hRank);
                        }
                        _closestToPinConfig.CurrentTour = tours[localIndex];
                        OnPlayButton(item);
                    };

                    goldBtn.clicked += () =>
                    {
                        if (goldBtn.HasClass("btn-active")) return;

                        goldBtn.AddToClassList("btn-active");
                        goldBtn.RemoveFromClassList("btn-inactive");
                        fragmentBtn.RemoveFromClassList("btn-active");
                        fragmentBtn.AddToClassList("btn-inactive");

                        goldPrize.style.display = DisplayStyle.Flex;
                        fragmentPrize.style.display = DisplayStyle.None;
                        currentMode[localIndex] = 0;
                    };

                    fragmentBtn.clicked += () =>
                    {
                        if (fragmentBtn.HasClass("btn-active")) return;

                        fragmentBtn.AddToClassList("btn-active");
                        fragmentBtn.RemoveFromClassList("btn-inactive");
                        goldBtn.RemoveFromClassList("btn-active");
                        goldBtn.AddToClassList("btn-inactive");

                        fragmentPrize.style.display = DisplayStyle.Flex;
                        goldPrize.style.display = DisplayStyle.None;

                        currentMode[localIndex] = 1;
                    };


                    // Init Prize Display Mode
                    goldPrize.style.display = DisplayStyle.Flex;
                    fragmentPrize.style.display = DisplayStyle.None;

                    _scrollViewPro.Add(item);
                    _listCources.Add(item);
                }
                else
                {
                    var panelItem = unavailableVisualTree;
                    VisualElement item = panelItem.Instantiate();

                    if (bgLockSpriteMap.ContainsKey(tours[index].GetBannerAddress()))
                    {
                        item.Q<VisualElement>("Content").style.backgroundImage =
                            new StyleBackground(bgLockSpriteMap[tours[index].GetBannerAddress()]);
                    }
                    else
                    {
                        item.Q<VisualElement>("Content").style.backgroundImage =
                            new StyleBackground(_bgLock[0]);
                    }

                    item.style.width = _cacheScrollViewWidth * Constant.ContentWitdhSizeSmoothDrag;
                    item.style.height = Length.Percent(100);

                    var windText = item.Q<Label>("WindText");
                    var needleText = item.Q<Label>("NeedleText");

                    windText.text = $"{tours[index].GetWindSpeedMin()} MPH";
                    needleText.text = $"{playerInfo.H2hRank.Info.needleSpeed:F1} %";

                    var descText = item.Q<Label>("DescText").text = $"{tours[index].GetRequiredElo()} to unlock".ToUpper();

                    _scrollViewPro.Add(item);
                    _listCources.Add(item);
                }
            }

            foreach (var stageInfo in _coursesConfig.available_course)
            {
                stageInfos!.Add(stageInfo);
            }

            VisualElement rightPadding = new VisualElement();
            rightPadding.style.width = _cacheScrollViewWidth * Constant.ContentWitdhSizeSmoothDrag / 2f;
            _scrollViewPro.Add(rightPadding);
        }

        private async void OnPlayButton(VisualElement item)
        {
            if (stageInfos != null && stageInfos.Count > 0)
            {
                if (ClosestToPinStageScreenMediator.OnRequestCheckAndDownload.startSinglePlayerAction != null)
                {
                    return;
                }

                var currentTour = _closestToPinConfig.CurrentTour;
                var currentCourse = GlobalSO.RemoteConfigData.CourseList.course_list.FirstOrDefault(course => course.course_id == currentTour.GetCourseUuid());

                var holeList = currentCourse.holes;

                var randomHole = holeList[UnityEngine.Random.Range(0, holeList.Count)];
                var matchingHole = GlobalSO.RemoteConfigData.HoleInfos.FirstOrDefault(hole => hole.Guid == randomHole.hole_guid);
                if (matchingHole == null)
                {
                    UnityEngine.Debug.LogError($"No matching hole info: {randomHole.hole_guid}");
                    return;
                }

                ClosestToPinStageScreenMediator.OnRequestCheckAndDownload.startSinglePlayerAction = new StartSinglePlayerAction(matchingHole);

                Course courseInfo = new Course(currentCourse.course_id, currentCourse.course_name, new List<HoleInfo> { matchingHole });

                GlobalSO.PlayFieldSO.SetCourseInfo(courseInfo);
                GlobalSO.PlayFieldSO.SetHoleInfo(matchingHole);
                _playButton = item.Q<Button>("enterBtn");
                _loadingPanel = item.Q<VisualElement>("LoadingAsset");
                _finishLoading = item.Q<VisualElement>("FinishLoading");
                _loading = item.Q<VisualElement>("Loading");
                _loadingBarUXML = item.Q<LoadingBarUXML>();
                _loadingText = item.Q<Label>("LoadingText");

                SetUpAssetLoading(0f);

                _cancellationTokenSource = new CancellationTokenSource();
                var downloadOperation = MapLoader.Instance.CheckAndDownloadMap(matchingHole, _cancellationTokenSource.Token, SetUpAssetLoading,
                    () =>
                    {

                    });
                await downloadOperation.Task;
                SetUpAssetLoading(1f);
                await UniTask.Delay(Constant.TransitionDelayMs);
                if (ClosestToPinStageScreenMediator.OnRequestCheckAndDownload.startSinglePlayerAction != null)
                {
                    _backButton.clickable = null;
                    ActionDispatcher.Dispatch(ClosestToPinStageScreenMediator.OnRequestCheckAndDownload.startSinglePlayerAction);
                }
            }
        }

        public void SetUpAssetLoading(float loadProgress, string message = "LOADING ASSET...")
        {
            _playButton.style.display = DisplayStyle.None;
            _loadingPanel.style.display = DisplayStyle.Flex;
            _loadingBarUXML.SetValue(loadProgress);
            if (!string.IsNullOrEmpty(message))
            {
                _loadingText.text = message;
            }
        }
    }
}