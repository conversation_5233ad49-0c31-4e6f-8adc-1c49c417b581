using GolfGame;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using _Golf.Scripts.Common.Helper;
using _Golf.Scripts.ScriptableObjects.Item;
using UnityEngine;
using UnityEngine.Playables;
using UnityEngine.UIElements;
using Kamgam.UIToolkitParticles;

namespace _Golf.Scripts.UI
{
    public class BagPrizeShowCaseUIComponent : UIComponentBase<BagPrizeShowCaseUIComponentMediator>
    {
        private BagPrizeShowCaseUIComponentMediator _mediator;
        private BagRewardResponse _bagData;
        public override UIComponentMediator CreateMediator()
        {
            _mediator = new BagPrizeShowCaseUIComponentMediator(this);
            return _mediator;
        }

        public override void OnHide(object[] data = null)
        {
            BagPrizeShowCasePanel.style.display = DisplayStyle.None;
            BagPrizeShowCasePanel.style.visibility = Visibility.Hidden;
            skipButton.style.visibility = Visibility.Hidden;
            ContinueButton.style.visibility = Visibility.Hidden;
            totalCardsNumberArea.style.visibility = Visibility.Hidden;

            //RingImageArea.style.visibility = Visibility.Hidden;

            animationChain.finished -= FinishAnimation;
            animationChain.DisposeChain();
            animationChain = null;

            ps0.Stop();
        }

        public override void OnOpen(object[] data = null)
        {
            _bagData = (BagRewardResponse)data[0];
            BagPrizeShowCasePanel.style.display = DisplayStyle.Flex;
            BagPrizeShowCasePanel.style.visibility = Visibility.Visible;
            totalCardsNumberArea.style.visibility = Visibility.Visible;
            //RingImageArea.style.visibility = Visibility.Visible;
            tapToOpen.style.visibility = Visibility.Visible;

            animationChain = new BagPrizeShowCaseChain();

            animationChain.finished += FinishAnimation;

            BagPrizeShowCaseTapToOpenHandler bagPrizeShowCaseTapToOpenHandler = new BagPrizeShowCaseTapToOpenHandler();
            bagPrizeShowCaseTapToOpenHandler.DataInjection(document, tapToOpenDirector, prizeAnimationDirector, tapToContinueDirector);

            animationChain.AddCombatHandler(bagPrizeShowCaseTapToOpenHandler);

            var totalCards = _bagData.fragment.Count + _bagData.ball.Count;

            int index = 0;

            for (int i = 0; i < _bagData.fragment.Count; i++)
            {
                BagPrizeShowCaseOpenSingleHandler bagPrizeShowCaseOpenSingleHandler = new BagPrizeShowCaseOpenSingleHandler();
                bagPrizeShowCaseOpenSingleHandler.DataInjection(document, tapToOpenDirector, prizeAnimationDirector, _bagData.fragment[i].fragmentId, _bagData.fragment[i].quantity, totalCards - index);

                animationChain.AddCombatHandler(bagPrizeShowCaseOpenSingleHandler);

                index++;
            }

            for (int i = 0; i < _bagData.ball.Count; i++)
            {
                BagPrizeShowCaseOpenSingleHandler bagPrizeShowCaseOpenSingleHandler = new BagPrizeShowCaseOpenSingleHandler();
                bagPrizeShowCaseOpenSingleHandler.DataInjection(document, tapToOpenDirector, prizeAnimationDirector, _bagData.ball[i].ballId, _bagData.ball[i].quantity, totalCards - index);

                animationChain.AddCombatHandler(bagPrizeShowCaseOpenSingleHandler);

                index++;
            }

            BagPrizeShowCaseOpenSingleHandler coinBagPrizeShowCaseOpenSingleHandler = new BagPrizeShowCaseOpenSingleHandler();
            coinBagPrizeShowCaseOpenSingleHandler.DataInjection(document, tapToOpenDirector, prizeAnimationDirector, "NW-COIN", _bagData.coin, totalCards - index);

            animationChain.AddCombatHandler(coinBagPrizeShowCaseOpenSingleHandler);

            animationChain.StartChain();

            ps0.Play();
        }

        private void FinishAnimation()
        {
            tapToContinueDirector.Stop();
            BagRewardResponse[] bagData = new[]
            {
                _bagData
            };
            MasterManager.Instance.OnChangeScreen(EScreenEnum.BagOpenResult, bagData);
        }

        BagPrizeShowCaseChain animationChain;

        [SerializeField]
        PlayableDirector prizeAnimationDirector;
        [SerializeField]
        PlayableDirector tapToOpenDirector;
        [SerializeField]
        PlayableDirector tapToContinueDirector;
        [SerializeField]
        ParticleSystemForImage ps0;

        UIDocument document;

        Button skipButton;
        VisualElement prize;
        VisualElement prizeNumberArea;
        Label ContinueButton;
        VisualElement totalCardsNumberArea;

        Button clickOverlay;
        Label tapToOpen;

        //VisualElement RingImageArea;

        VisualElement BagPrizeShowCasePanel;

        public override void Init()
        {
            document = GetComponent<UIDocument>();

            tapToOpenDirector.extrapolationMode = DirectorWrapMode.Loop;
            tapToContinueDirector.extrapolationMode = DirectorWrapMode.Loop;

            VisualElement root = document.rootVisualElement;

            skipButton = root.Q<Button>("SkipButton");
            prize = root.Q<VisualElement>("Prize");
            prizeNumberArea = root.Q<VisualElement>("PrizeNumberArea");
            ContinueButton = root.Q<Label>("ContinueButton");
            totalCardsNumberArea = root.Q<VisualElement>("TotalCardsNumberArea");

            clickOverlay = root.Q<Button>("ClickOverlay");

            tapToOpen = root.Q<Label>("TapToOpen");

            //RingImageArea = root.Q<VisualElement>("RingImage");

            BagPrizeShowCasePanel = root.Q<VisualElement>("BagPrizeShowCasePanel");

            skipButton.clicked += () =>
            {
                animationChain.SkipChain();
                BagRewardResponse[] bagData = new BagRewardResponse[]
                {
                    _bagData
                };
                MasterManager.Instance.OnChangeScreen(EScreenEnum.BagOpenResult, bagData);
            };
        }
    }
}