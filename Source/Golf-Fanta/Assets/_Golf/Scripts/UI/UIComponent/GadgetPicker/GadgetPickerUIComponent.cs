using _Golf.Scripts.Core;
using _Golf.Scripts.ScriptableObjects.Item;
using Game.UI;
using GolfFantaModule.Models.Economy;
using GolfGame;
using Kamgam.UIToolkitScrollViewPro;
using Kamgam.UIToolkitWorldImage;
using Kamgam.UIToolkitWorldImage.Examples;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using _Golf.Scripts.Common;
using _Golf.Scripts.ScriptableObjects;
using UnityEngine;
using UnityEngine.UIElements;
using _Golf.Scripts.Common.Helper;
using _Golf.Scripts.Core.CourseController;
using _Golf.Scripts.GlobalManagers;
using Cysharp.Threading.Tasks;

namespace _Golf.Scripts.UI
{
    public class GadgetPickerUIComponent : PopupUIComponentBase<GadgetPickerUIComponentMediator>
    {
        GadgetPickerUIComponentMediator gadgetPickerUIComponentMediator;

        [SerializeField]
        bool isDebug;
        PlayerGadget playerGadget;

        ClubType clubType;

        [SerializeField]
        UIDocument gadgetPicker;
        [SerializeField] VisualTreeAsset _availableVisualTree;
        [SerializeField] VisualTreeAsset _ballItemVisualTree;

        InventoryItemsSO inventoryItemsSO;

        VisualElement root;

        List<Button> tabButtons;

        Dictionary<GadgetPickerTab, VisualElement> tabButtonCoveredUpBGs;
        Dictionary<GadgetPickerTab, Label> tabButtonCoveredUpLabels;
        Dictionary<GadgetPickerTab, VisualElement> subPanels;

        CircleSlider2D ballSlider;

        Slider backSpinSlider;
        Slider sideSpinSlider;

        SpinBarUXML topSpinArea;
        SpinBarUXML bottomSpinArea;
        SpinBarUXML rightSpinArea;
        SpinBarUXML leftSpinArea;

        ScrollViewPro ballScrollView; ScrollViewPro gearScrollView;
        VisualElement ballPager; VisualElement gearPager;

        List<VisualElement> ves; List<VisualElement> gearves;
        List<VisualElement> ballPagerItems; List<VisualElement> gearPagerItems;

        VisualElement ExtraDistance;
        VisualElement WindResistance;
        VisualElement Bouncing;
        VisualElement SideSpin;
        private Button _extraDistanceButton;
        private Button _windResistanceButton;
        private Button _bouncingButton;
        private Button _sideSpinButton;
        private Button _ballPanelClickZone;
        private VisualElement ExtraDistanceTooltip;
        private VisualElement WindResistanceTooltip;
        private VisualElement BouncingTooltip;
        private VisualElement SideSpinTooltip;
        private VisualElement _blockingUI;

        Label extraDistanceLabel;
        Label windResistanceLabel;
        Label bouncingLabel;
        Label sideSpinLabel;

        WorldImage ballWorldImage;

        VisualElement currentGearIcon;
        Label currentGearDesc;

        Label clubTypeLabel;
        List<Ball> _balls;

        HoleBehaviour hole;

        // internal data
        const string stylesResource = "GameUI/Styles/GadgetPicker";

        bool ballPanelInit = false; bool gearPanelInit = false;
        int focusedPageBuffer = -1; int focusedGearPageBuffer = -1;
        private int _currentSnapBallIndex = -1;
        private List<VisualElement> _cachedBallItems = new List<VisualElement>();

        bool wholePanelInit = false;

        float ballScrollViewWidth = 0; float ballScrollViewHeight = 0;
        float gearScrollViewWidth = 0; float gearScrollViewHeight = 0;
        float ballPagerWidth = 0; float ballPagerHeight = 0;
        float gearPagerWidth = 0; float gearPagerHeight = 0;

        int ballPages; int gearPages;
        int currentBall = 0; int currentGear = 0; int lastGear = 0;
        string currentBallImage = "null";
        private readonly Vector2 initValue = new Vector2(0.5f, 0.5f);

        ClubSO playerMainClub;
        ClubSO playerSecondaryClub;

        bool isPanelOpen = false;

        Button firstClubButton;
        Button secondClubButton;
        Button gadgetPickerPanelButton;

        List<BallInfo> ballsList;
        List<GearInfo> gearsList;
        VisualElement firstClub;
        VisualElement secondClub;
        private VisualElement _clubImage;
        private VisualElement _ballImage;

        private GadgetPickerTab currentTab;
        private bool spinCached;
        private float sliderX; float sliderY;
        private float ballSliderX;
        private float ballSliderY;
        private int maxBackSpin = 0;
        private int maxSideSpin = 0;

        private CommonSettings _commonSettings;

        private BackSpinChanged _backSpinChangedAction = new BackSpinChanged();
        private SideSpinChanged _sideSpinChangedAction = new SideSpinChanged();

        public ClubType CurrentClubType => clubType;

        public override UIComponentMediator CreateMediator()
        {
            gadgetPickerUIComponentMediator = new GadgetPickerUIComponentMediator(this);
            return gadgetPickerUIComponentMediator;
        }

        public override void OnInit()
        {
            inventoryItemsSO = GlobalSO.InventoryItemSo;
            playerGadget = GlobalSO.PlayerGadget;
            _commonSettings = GlobalSO.RemoteConfigData.CommonSettings;

            ballsList = playerGadget.GetPlayerBallList(inventoryItemsSO.balls);
            gearsList = playerGadget.GetPlayerGearList(inventoryItemsSO.chips);

            root = gadgetPicker.rootVisualElement;

            root.styleSheets.Add(Resources.Load<StyleSheet>(stylesResource));

            firstClub = root.Q<VisualElement>("FirstClub");
            secondClub = root.Q<VisualElement>("SecondClub");

            tabButtons = new List<Button>();

            Button clubButton = root.Q<Button>("ClubButton"); tabButtons.Add(clubButton);
            Button ballButton = root.Q<Button>("BallButton"); tabButtons.Add(ballButton);
            Button gearButton = root.Q<Button>("GearButton"); tabButtons.Add(gearButton);

            _extraDistanceButton = root.Q<Button>("ExtraDistanceButton");
            _windResistanceButton = root.Q<Button>("WindResistanceButton");
            _bouncingButton = root.Q<Button>("BouncingButton");
            _sideSpinButton = root.Q<Button>("SideSpinButton");

            _extraDistanceButton.clickable = new Clickable(() => ShowAttribute(BallAttribute.EXTRA_DISTANCE));
            _windResistanceButton.clickable = new Clickable(() => ShowAttribute(BallAttribute.WIND_RESISTANCE));
            _bouncingButton.clickable = new Clickable(() => ShowAttribute(BallAttribute.BOUNCING));
            _sideSpinButton.clickable = new Clickable(() => ShowAttribute(BallAttribute.SIDE_SPIN));
            _ballPanelClickZone = root.Q<Button>("PanelClickZone");
            _ballPanelClickZone.clickable = new Clickable(() => ShowAttribute(BallAttribute.NONE));

            clubButton.clicked += () => { OpenTab(GadgetPickerTab.CLUB); };
            ballButton.clicked += () => { OpenTab(GadgetPickerTab.BALL); };
            gearButton.clicked += () => { OpenTab(GadgetPickerTab.GEAR); };

            tabButtonCoveredUpBGs = new Dictionary<GadgetPickerTab, VisualElement>();
            VisualElement coverupClubBG = root.Q<VisualElement>("CoverupClubBG"); tabButtonCoveredUpBGs.Add(GadgetPickerTab.CLUB, coverupClubBG);
            VisualElement coverupBallBG = root.Q<VisualElement>("CoverupBallBG"); tabButtonCoveredUpBGs.Add(GadgetPickerTab.BALL, coverupBallBG);
            VisualElement coverupGearBG = root.Q<VisualElement>("CoverupGearBG"); tabButtonCoveredUpBGs.Add(GadgetPickerTab.GEAR, coverupGearBG);

            tabButtonCoveredUpLabels = new Dictionary<GadgetPickerTab, Label>();
            Label coverupClubTxt = root.Q<Label>("CoverupClubTxt"); tabButtonCoveredUpLabels.Add(GadgetPickerTab.CLUB, coverupClubTxt);
            Label coverupBallTxt = root.Q<Label>("CoverupBallTxt"); tabButtonCoveredUpLabels.Add(GadgetPickerTab.BALL, coverupBallTxt);
            Label coverupGearTxt = root.Q<Label>("CoverupGearTxt"); tabButtonCoveredUpLabels.Add(GadgetPickerTab.GEAR, coverupGearTxt);

            subPanels = new Dictionary<GadgetPickerTab, VisualElement>();
            VisualElement clubPanel = root.Q<VisualElement>("ClubPanel"); subPanels.Add(GadgetPickerTab.CLUB, clubPanel);
            VisualElement ballPanel = root.Q<VisualElement>("BallPanel"); subPanels.Add(GadgetPickerTab.BALL, ballPanel);
            VisualElement gearPanel = root.Q<VisualElement>("GearPanel"); subPanels.Add(GadgetPickerTab.GEAR, gearPanel);

            backSpinSlider = root.Q<Slider>("BackSpinSlider");
            backSpinSlider.RegisterValueChangedCallback(x => BackSpinSliderChanged(x));

            sideSpinSlider = root.Q<Slider>("SideSpinSlider");
            sideSpinSlider.RegisterValueChangedCallback(x => SideSpinSliderChanged(x));

            topSpinArea = root.Q<SpinBarUXML>("TopSpinSlider");
            bottomSpinArea = root.Q<SpinBarUXML>("BottomSpinSlider");
            rightSpinArea = root.Q<SpinBarUXML>("RightSpinSlider");
            leftSpinArea = root.Q<SpinBarUXML>("LeftSpinSlider");

            ballSlider = root.Q<CircleSlider2D>("BallSlider2D");
            ballSlider.RegisterValueChangedCallback(x =>
            {
                float upperSideLimit = ScaleDownSliderValue((maxSideSpin / 100f) * 0.5f + 0.5f);
                float bottomSideLimit = 0.5f - (upperSideLimit - 0.5f);

                float upperBackLimit = ScaleDownSliderValue((maxBackSpin / 100f) * 0.5f + 0.5f);
                float bottomBackLimit = 0.5f - (upperBackLimit - 0.5f);

                float finalX = x.newValue.x;
                if (x.newValue.x > upperSideLimit)
                {
                    finalX = upperSideLimit;
                }
                else if (x.newValue.x < bottomSideLimit)
                {
                    finalX = bottomSideLimit;
                }
                float finalY = x.newValue.y;
                if (x.newValue.y > upperBackLimit)
                {
                    finalY = upperBackLimit;
                }
                else if (x.newValue.y < bottomBackLimit)
                {
                    finalY = bottomBackLimit;
                }

                if (finalX != x.newValue.x || finalY != x.newValue.y)
                {
                    ballSlider.SetValueWithoutNotify(new Vector2(
                        finalX,
                        finalY
                    ));
                }

                float scaledUpX = (ScaleUpSliderValue(finalX) - 0.5f) / 0.5f;
                float scaledUpY = (ScaleUpSliderValue(finalY) - 0.5f) / 0.5f;

                topSpinArea.SetValue(0, scaledUpY);
                bottomSpinArea.SetValue(0, scaledUpY);

                leftSpinArea.SetValue(scaledUpX, 0);
                rightSpinArea.SetValue(scaledUpX, 0);

                backSpinSlider.value = scaledUpY * 100f;
                sideSpinSlider.value = scaledUpX * 100f;

                if (!currentBallImage.Equals("null"))
                {
                    ResourcesManager.Instance.Rotate3DModelWithEuler(currentBallImage, scaledUpX * 33f, scaledUpY * 33f);
                }

                sliderX = scaledUpX;
                sliderY = scaledUpY;
                ballSliderX = scaledUpX * 0.5f + 0.5f;
                ballSliderY = scaledUpY * 0.5f + 0.5f;
            });

            //Note: this is a hack to make the ball spin
            // because the default value of the slider is 0.5f
            // and make it ignore the init value callback
            ballSlider.value = Vector2.zero;

            currentTab = GadgetPickerTab.CLUB;
            spinCached = false;
            sliderX = 0f; sliderY = 0f;
            ballSliderX = 0.5f; ballSliderY = 0.5f;

            ExtraDistanceTooltip = root.Q<VisualElement>("ExtraDistanceTooltip");
            WindResistanceTooltip = root.Q<VisualElement>("WindResistanceTooltip");
            BouncingTooltip = root.Q<VisualElement>("BouncingTooltip");
            SideSpinTooltip = root.Q<VisualElement>("SideSpinTooltip");

            ExtraDistance = root.Q<VisualElement>("ExtraDistance");
            WindResistance = root.Q<VisualElement>("WindResistance");
            Bouncing = root.Q<VisualElement>("Bouncing");
            SideSpin = root.Q<VisualElement>("SideSpin");

            extraDistanceLabel = root.Q<Label>("ExtraDistanceLabel");
            windResistanceLabel = root.Q<Label>("WindResistanceLabel");
            bouncingLabel = root.Q<Label>("BouncingLabel");
            sideSpinLabel = root.Q<Label>("BallSideSpinLabel");

            clubTypeLabel = root.Q<Label>("ClubTypeLabel");

            ballWorldImage = root.Q<WorldImage>("BallWorldImage");

            firstClubButton = root.Q<Button>("FirstClubButton");

            secondClubButton = root.Q<Button>("SecondClubButton");

            firstClubButton.clicked += () =>
            {

            };

            secondClubButton.clicked += () =>
            {
                ClubInfo mainClubData = playerGadget.GetPlayerClubData(playerSecondaryClub);
                ClubInfo secondaryClubData = playerGadget.GetPlayerClubData(playerMainClub);
                BallInfo chosenBallData = playerGadget.GetPlayerBallData(playerGadget.ChosenBall);

                SetClub(mainClubData, secondaryClubData, chosenBallData);
                SwitchClubs();
                OpenClubTab();
            };

            gadgetPickerPanelButton = root.Q<Button>("GadgetPickerPanelButton");
            gadgetPickerPanelButton.clicked += () =>
            {
                MasterManager.Instance.HideUIComponent(UIComponentEnum.GadgetPickerComponentUI);
            };
            _clubImage = root.Q<VisualElement>("ClubImage");
            _ballImage = root.Q<VisualElement>("BallImage");

            currentGearIcon = root.Q<VisualElement>("CurrentGearIcon");
            currentGearDesc = root.Q<Label>("CurrentGearDesc");

            InitChosenBall();

            var pickedGear = playerGadget.ChosenChip;
            if (pickedGear == null)
            {
                currentGear = 0;
                playerGadget.ChosenChip = null;
            }
            else
            {
                currentGear = gearsList.FindIndex(x => x.Data.ItemId.Equals(pickedGear.ItemId)) + 1;
            }
            StartCoroutine(InitBallPanelThread());

            var exitTapZone = root.Q<VisualElement>("ExitTapZone");
            exitTapZone.RegisterCallback<ClickEvent>(evt =>
            {
                MasterManager.Instance.HideUIComponent(UIComponentEnum.GadgetPickerComponentUI);
            });

            _blockingUI = root.Q<VisualElement>("BlockingUI");
        }

        private void InitChosenBall()
        {
            playerGadget.ValidateBallData();
            var pickedBall = playerGadget.ChosenBall;
            if (pickedBall == null)
            {
                currentBall = 0;
                playerGadget.ChosenBall = ballsList[0].Data;
            }
            else
            {
                currentBall = ballsList.FindIndex(x => x.Data.ItemId.Equals(pickedBall.ItemId));
            }
        }

        private IEnumerator InitBallPanelThread()
        {
            ballScrollView = root.Q<ScrollViewPro>("BallScrollView");
            ballPager = root.Q<VisualElement>("BallPager");

            gearScrollView = root.Q<ScrollViewPro>("GearScrollView");
            gearPager = root.Q<VisualElement>("GearPager");

            yield return new WaitForEndOfFrame();
            yield return new WaitForEndOfFrame();

            ballScrollViewHeight = ballScrollView.resolvedStyle.height;
            ballScrollViewWidth = ballScrollView.resolvedStyle.width;

            ballPagerHeight = ballPager.resolvedStyle.height;
            ballPagerWidth = ballPager.resolvedStyle.width;

            gearScrollViewHeight = gearScrollView.resolvedStyle.height;
            gearScrollViewWidth = gearScrollView.resolvedStyle.width;

            gearPagerHeight = gearPager.resolvedStyle.height;
            gearPagerWidth = gearPager.resolvedStyle.width;

            wholePanelInit = true;
            root.style.display = DisplayStyle.None;
        }

        public override void BeforeOpen(object[] data = null)
        {
            isPanelOpen = true;

            if (data.Length >= 3)
            {
                if ((int)data[1] == 1)
                {
                    version = 1;
                    SetGolfBallSwingDragUI((GolfBallSwingDragUI)data[0]);

                    clubType = (ClubType)data[2];
                }
                else if ((int)data[1] == 2)
                {
                    version = 2;
                    SetGolfBallSwingDragUIVersion2((GolfBallSwingDragUIVersion2)data[0]);

                    clubType = (ClubType)data[2];
                }
                else if ((int)data[1] == 3)
                {
                    version = 3;
                    SetGolfBallSwingPuttingUI((GolfBallSwingDragPuttingUI)data[0]);

                    clubType = (ClubType)data[2];
                }

                if (data.Length == 4)
                {
                    currentTab = (GadgetPickerTab)data[3];
                }
            }

            root.Q<VisualElement>("MasterPanel").style.display = DisplayStyle.Flex;

            if (wholePanelInit)
            {
                OpenTab(currentTab);
            }
            else
            {
                StartCoroutine(WaitTilFinishedInit());
            }
        }

        private IEnumerator WaitTilFinishedInit()
        {
            yield return new WaitUntil(() => wholePanelInit == true);
            OpenTab(currentTab);
        }

        int version = 0;
        GolfBallSwingDragUI golfBallSwingDragUI;
        GolfBallSwingDragUIVersion2 golfBallSwingDragUIVersion2;
        private GolfBallSwingDragPuttingUI golfBallSwingDragPuttingUI;

        public void SetGolfBallSwingDragUI(GolfBallSwingDragUI _golfBallSwingDragUI)
        {
            golfBallSwingDragUI = _golfBallSwingDragUI;
            golfBallSwingDragUI.SetGadgetPicker(this);
        }

        public void SetGolfBallSwingDragUIVersion2(GolfBallSwingDragUIVersion2 _golfBallSwingDragUI)
        {
            golfBallSwingDragUIVersion2 = _golfBallSwingDragUI;
            golfBallSwingDragUIVersion2.SetGadgetPicker(this);
        }

        public void SetGolfBallSwingPuttingUI(GolfBallSwingDragPuttingUI _golfBallSwingDragPuttingUI)
        {
            golfBallSwingDragPuttingUI = _golfBallSwingDragPuttingUI;
            golfBallSwingDragPuttingUI.SetGadgetPicker(this);
        }

        public void SetToDefaultBall()
        {
            currentBall = 0;
            playerGadget.ChosenBall = ballsList[0].Data;
        }

        public override void AfterHide(object[] data = null)
        {
            isPanelOpen = false;

            root.Q<VisualElement>("MasterPanel").style.display = DisplayStyle.None;
            //currentTab = GadgetPickerTab.CLUB;

            if (golfBallSwingDragUI != null && version == 1)
            {
                golfBallSwingDragUI.GadgetPickerPanelClose();
            }
            else if (golfBallSwingDragUIVersion2 != null && version == 2)
            {
                golfBallSwingDragUIVersion2.GadgetPickerPanelClose();
            }
            else if (golfBallSwingDragPuttingUI != null && version == 3)
            {
                golfBallSwingDragPuttingUI.GadgetPickerPanelClose();
            }

            GlobalSO.PlayerGadget.SaveGadgetData();
        }

        public void OpenTab(GadgetPickerTab tabType)
        {
            currentTab = tabType;

            SetActiveForPanels(tabType);

            if (tabType == GadgetPickerTab.CLUB)
            {
                OpenClubTab();
            }
            else if (tabType == GadgetPickerTab.BALL)
            {
                OpenBallTab();
            }
            else if (tabType == GadgetPickerTab.GEAR)
            {
                OpenGearTab();
            }
        }

        public void SetActiveForPanels(GadgetPickerTab tabType)
        {
            foreach (var coveredUpBG in tabButtonCoveredUpBGs)
            {
                if (coveredUpBG.Key == tabType)
                {
                    coveredUpBG.Value.style.visibility = Visibility.Visible;
                }
                else
                {
                    coveredUpBG.Value.style.visibility = Visibility.Hidden;
                }
            }

            foreach (var coveredUpTxt in tabButtonCoveredUpLabels)
            {
                if (coveredUpTxt.Key == tabType)
                {
                    coveredUpTxt.Value.style.visibility = Visibility.Visible;
                }
                else
                {
                    coveredUpTxt.Value.style.visibility = Visibility.Hidden;
                }
            }

            foreach (var panel in subPanels)
            {
                if (panel.Key == tabType)
                {
                    if (panel.Key == GadgetPickerTab.CLUB)
                    {
                        subPanels[GadgetPickerTab.CLUB].style.display = DisplayStyle.Flex;
                        subPanels[GadgetPickerTab.BALL].style.display = DisplayStyle.None;
                        subPanels[GadgetPickerTab.GEAR].style.display = DisplayStyle.None;
                    }
                    else if (panel.Key == GadgetPickerTab.BALL)
                    {
                        subPanels[GadgetPickerTab.CLUB].style.display = DisplayStyle.Flex;
                        subPanels[GadgetPickerTab.BALL].style.display = DisplayStyle.Flex;
                        subPanels[GadgetPickerTab.GEAR].style.display = DisplayStyle.None;

                    }
                    else if (panel.Key == GadgetPickerTab.GEAR)
                    {
                        subPanels[GadgetPickerTab.CLUB].style.display = DisplayStyle.None;
                        subPanels[GadgetPickerTab.BALL].style.display = DisplayStyle.None;
                        subPanels[GadgetPickerTab.GEAR].style.display = DisplayStyle.Flex;
                    }
                    else
                    {
                        panel.Value.style.display = DisplayStyle.Flex;
                    }

                    break;
                }
                else
                {
                    panel.Value.style.display = DisplayStyle.None;
                }
            }
        }

        public void OpenClubTab()
        {
            clubTypeLabel.text = clubType.ToString();

            playerMainClub = playerGadget.GetClub(clubType, isMain: true);
            playerSecondaryClub = playerGadget.GetClub(clubType, isMain: false);

            ClubInfo mainClub = playerGadget.GetPlayerClubData(playerMainClub);
            ClubInfo secondaryClub = playerGadget.GetPlayerClubData(playerSecondaryClub);

            ClubSO mainClubData = playerMainClub;
            ClubSO secondaryClubData = playerSecondaryClub;

            BallInfo chosenBallData = playerGadget.GetPlayerBallData(playerGadget.ChosenBall);

            if (version == 3)
            {
                //block interaction
                _blockingUI.style.display = DisplayStyle.Flex;
            }
            else
            {
                _blockingUI.style.display = DisplayStyle.None;
            }

            SetClub(mainClub, secondaryClub, chosenBallData);

            if (_currentBallImageCache == currentBall)
            {
                return;
            }

            if (currentBallImage.Equals("null"))
            {
                currentBallImage = ResourcesManager.Instance.Create3DModel(ballsList[currentBall].Data.BallModel, DisplayModelZone.Gadget);
                ballWorldImage.RendererId = currentBallImage;

                _currentBallImageCache = currentBall;
                //
                ballWorldImage.style.display = DisplayStyle.None;
                StartCoroutine(WaitForPainting());
            }
            else
            {
                ResourcesManager.Instance.Remove3DModel(currentBallImage);

                currentBallImage = ResourcesManager.Instance.Create3DModel(ballsList[currentBall].Data.BallModel, DisplayModelZone.Gadget);
                ballWorldImage.RendererId = currentBallImage;

                _currentBallImageCache = currentBall;

                if (!currentBallImage.Equals("null"))
                {
                    ResourcesManager.Instance.Rotate3DModelWithEuler(currentBallImage, sliderX * 33f, sliderY * 33f);
                }
            }
        }

        IEnumerator WaitForPainting()
        {
            ballWorldImage.style.display = DisplayStyle.None;

            while (ballWorldImage.IsDrawn())
            {
                yield return new WaitForEndOfFrame();
            }

            ballWorldImage.style.display = DisplayStyle.Flex;

            if (!currentBallImage.Equals("null"))
            {
                ResourcesManager.Instance.Rotate3DModelWithEuler(currentBallImage, sliderX * 33f, sliderY * 33f);
            }
        }

        int _currentBallImageCache = -1;

        private void OpenBallTab()
        {
            ShowAttribute(BallAttribute.NONE);

            if (ballPanelInit == false)
            {
                ballsList = playerGadget.HandleDisplayBalls();

                ballScrollView.Clear();
                ballPager.Clear();
                _cachedBallItems.Clear();

                var padding = new VisualElement();
                padding.style.width = ballScrollViewWidth;
                padding.style.height = ballScrollViewHeight * 0.33f;
                ballScrollView.Add(padding);

                for (int i = 0; i < ballsList.Count; i++)
                {
                    var index = i;
                    var ballItem = _ballItemVisualTree.Instantiate();

                    ballItem.Q<VisualElement>("ItemIcon").style.backgroundImage = new StyleBackground(ResourcesManager.Instance.GetItemSprite(ballsList[i].GetBallId()));
                    var itemAmount = ballsList[i].GetItemAmount();
                    if (itemAmount == -1)
                    {
                        ballItem.Q<Label>("ItemNumber").text = LocalizationManager.Instance.GetString("common_unlimited");
                    }
                    else
                    {
                        ballItem.Q<Label>("ItemNumber").text = itemAmount.ToString();
                    }
                    ballItem.Q<VisualElement>("Equip").style.display = DisplayStyle.None;
                    ballItem.Q<Button>().clicked += () =>
                    {
                        if (currentBall == index) return;
                        OnBallChosen(index);
                    };

                    ballItem.style.width = ballScrollViewWidth * 0.95f;
                    ballItem.style.height = ballScrollViewHeight * 0.33f;
                    ballScrollView.Add(ballItem);
                    _cachedBallItems.Add(ballItem);
                }

                var padding2 = new VisualElement();
                padding2.style.width = ballScrollViewWidth;
                padding2.style.height = ballScrollViewHeight * 0.33f;
                ballScrollView.Add(padding2);

                ballPanelInit = true;
            }
            ballScrollView.RegisterCallbackOnce<GeometryChangedEvent>(x =>
            {
                SnapToCurrentPickedBall(currentBall);
            });

            OnBallChosen(currentBall, true);
            OpenClubTab(); // OpenClubTab() to update the ball image
        }

        public void OpenGearTab()
        {
            if (gearPanelInit == false)
            {
                gearScrollView.Clear();
                gearPager.Clear();

                gearves = new List<VisualElement>();

                gearPages = (gearsList.Count + 1) / 4 + 1;

                for (int i = 0; i < gearPages; i++)
                {
                    VisualElement ve = _availableVisualTree.Instantiate();
                    ve.style.width = gearScrollViewWidth;
                    ve.style.height = gearScrollViewHeight;

                    gearScrollView.Add(ve);

                    for (int j = 0; j < 4; j++)
                    {
                        int gearIndex = i * 4 + j;

                        if (gearIndex == 0)
                        {
                            VisualElement card = ve.Q<VisualElement>(j.ToString());
                            card.Q<VisualElement>("BagInfoItem").style.backgroundImage = new StyleBackground(ResourcesManager.Instance.GetGearSprite("NotEquipBG"));

                            card.Q<VisualElement>("ItemIcon").style.backgroundImage = new StyleBackground((Sprite)null);
                            card.Q<Label>("ItemNumber").text = "";
                            card.Q<Label>("ItemName").text = "";

                            card.Q<Button>("InfoButton").clicked += () =>
                            {
                                OnGearChosen(gearIndex);
                            };
                            continue;
                        }

                        if (gearIndex <= gearsList.Count)
                        {
                            bool isOneUseConsumedOnHitUsed = false;
                            bool isThisGearAvailableInThisGameMode = false;
                            GearData currentGearData = gearsList[gearIndex - 1].Data.serverData.customData;
                            isOneUseConsumedOnHitUsed = CheckOneUseConsumedOnHitGearUsed(currentGearData);
                            isThisGearAvailableInThisGameMode = CheckIfGearIsUsableInGameMode(currentGearData);

                            VisualElement card = ve.Q<VisualElement>(j.ToString());
                            card.Q<VisualElement>("BagInfoItem").SetBackgroundImage(gearsList[gearIndex - 1].Data.Rarity.Background);
                            card.Q<Label>("ItemName").text = gearsList[gearIndex - 1].Data.ItemName;
                            card.Q<VisualElement>("ItemIcon").SetBackgroundImage(ResourcesManager.Instance.GetGearSprite(gearsList[gearIndex - 1].Data.ItemId));
                            card.Q<Label>("ItemNumber").text = isThisGearAvailableInThisGameMode ? gearsList[gearIndex - 1].Amount.ToString() : "Disabled";

                            card.Q<Button>("InfoButton").clicked += () =>
                            {
                                OnGearChosen(gearIndex);
                            };
                        }
                        else
                        {
                            VisualElement gearItem = ve.Q<VisualElement>(j.ToString());
                            gearItem.style.visibility = Visibility.Hidden;
                        }
                    }

                    gearves.Add(ve);
                }

                var GearPageItem = Resources.Load<VisualTreeAsset>("GameUI/Templates/BallPageItem");

                gearPagerItems = new List<VisualElement>();

                for (int i = 0; i < gearPages; i++)
                {
                    VisualElement ve = GearPageItem.Instantiate(); ve.name = "GearPageItem" + i;
                    ve.style.width = gearScrollViewWidth * 3f / 100f;
                    ve.style.height = gearPagerHeight;
                    gearPager.Add(ve);

                    gearPagerItems.Add(ve);
                }

                gearPanelInit = true;
            }

            OnGearChosen(currentGear, true);
            gearScrollView.ScrollToExtension(gearves[currentGear / 4], ballScrollViewWidth, 0,
                ScrollViewPro.ScrollToAlign.Center, ScrollViewPro.ScrollToAlign.Center);
        }

        // CLUB PANEL
        private void SideSpinSliderChanged(ChangeEvent<float> x)
        {
            float finalValue = x.newValue;

            if (x.newValue > maxSideSpin)
            {
                finalValue = maxSideSpin;
                sideSpinSlider.SetValueWithoutNotify(finalValue);
            }
            else if (x.newValue < maxSideSpin * -1f)
            {
                finalValue = maxSideSpin * -1f;
                sideSpinSlider.SetValueWithoutNotify(finalValue);
            }

            sliderX = (finalValue / 100f);
            ballSliderX = (finalValue / 100f) * 0.5f + 0.5f;

            leftSpinArea.SetValue(finalValue / 100f, 0);
            rightSpinArea.SetValue(finalValue / 100f, 0);

            ballSlider.SetValueWithoutNotify(new Vector2(ScaleDownSliderValue(ballSliderX), ScaleDownSliderValue(ballSliderY)));

            //ResourcesManager.Instance.Rotate3DModelWithEuler(currentBallImage, sliderX * 33f, sliderY * 33f);
            //if (x.newValue >= maxSideSpin * -1f && x.newValue <= maxSideSpin)
            //{
            if (!currentBallImage.Equals("null"))
            {
                ResourcesManager.Instance.Rotate3DModelWithEuler(currentBallImage, sliderX * 33f, sliderY * 33f);
            }
            //}

            ActionDispatcher.Dispatch(_sideSpinChangedAction.UpdateSpin(GetSideSpin()));
        }

        public void BackSpinSliderChanged(ChangeEvent<float> y)
        {
            float finalValue = y.newValue;

            if (y.newValue > maxBackSpin)
            {
                finalValue = maxBackSpin;
                backSpinSlider.SetValueWithoutNotify(finalValue);
            }
            else if (y.newValue < maxBackSpin * -1f)
            {
                finalValue = maxBackSpin * -1f;
                backSpinSlider.SetValueWithoutNotify(finalValue);
            }

            sliderY = (finalValue / 100f);
            ballSliderY = (finalValue / 100f) * 0.5f + 0.5f;

            topSpinArea.SetValue(0, finalValue / 100f);
            bottomSpinArea.SetValue(0, finalValue / 100f);

            ballSlider.SetValueWithoutNotify(new Vector2(ScaleDownSliderValue(ballSliderX), ScaleDownSliderValue(ballSliderY)));

            //ResourcesManager.Instance.Rotate3DModelWithEuler(currentBallImage, sliderX * 33f, sliderY * 33f);
            //if (y.newValue >= maxBackSpin * -1f && y.newValue <= maxBackSpin)
            //{
            if (!currentBallImage.Equals("null"))
            {
                ResourcesManager.Instance.Rotate3DModelWithEuler(currentBallImage, sliderX * 33f, sliderY * 33f);
            }
            //}

            ActionDispatcher.Dispatch(_backSpinChangedAction.UpdateSpin(GetBackSpin()));
        }

        private float ScaleDownSliderValue(float x)
        {
            // transfer from
            // 0.000 --- 0.5 ---- 1.000
            // to
            // 0.175 --- 0.5 ---- 0.825

            float upperLimit = 0.825f;
            float bottomLimit = 0.175f;

            if (x >= 0.5f)
            {
                float ratio = (x - 0.5f) / 0.5f;
                return 0.5f + ratio * (upperLimit - 0.5f);
            }
            else
            {
                float ratio = x / 0.5f;
                return bottomLimit + ratio * (0.5f - bottomLimit);
            }
        }

        private float ScaleUpSliderValue(float y)
        {
            float upperLimit = 0.825f;
            float bottomLimit = 0.175f;

            if (y >= 0.5f)
            {
                return (y - 0.5f) * 0.5f / (upperLimit - 0.5f) + 0.5f;
            }
            else
            {
                return (y - bottomLimit) * 0.5f / (0.5f - bottomLimit);
            }
        }

        private void SetClub(ClubInfo mainClub, ClubInfo secondaryClub, BallInfo chosenBallData)
        {
            ClubSO _mainClub = mainClub.Data;

            if (secondaryClub != null)
            {
                secondClubButton.pickingMode = PickingMode.Position;
                secondClub.style.display = DisplayStyle.Flex;

                var clubImage2 = secondClub.Q<VisualElement>("ClubImage");
                GolfUIToolKit.Utility.SetUpClubImage(this, clubImage2, secondaryClub);
                clubImage2.Q<VisualElement>("Equip").style.display = DisplayStyle.None;
            }
            else
            {
                secondClubButton.pickingMode = PickingMode.Ignore;
                secondClub.style.display = DisplayStyle.None;
            }

            VisualElement clubImage1 = firstClub.Q<VisualElement>("ClubImage");
            GolfUIToolKit.Utility.SetUpClubImage(this, clubImage1, mainClub);
            clubImage1.Q<VisualElement>("Equip").style.display = DisplayStyle.Flex;

            maxBackSpin = mainClub.GetBackSpin();
            //maxSideSpin = mainClub.GetSideSpin(); // club no longer provides side spin
            maxSideSpin = (int)chosenBallData.GetSideSpin();

            // check if current gear affects back and side spin
            GearData currentGearData = GlobalSO.PlayerGadget.ChosenChip == null ? null : GlobalSO.PlayerGadget.ChosenChip.serverData.customData;
            float gearBonusBackSpin = currentGearData == null ? 0f : currentGearData.GetExtraBackSpinIfAny();
            float gearBonusSideSpin = currentGearData == null ? 0f : currentGearData.GetExtraSideSpinIfAny();

            maxBackSpin += (int)gearBonusBackSpin;
            maxSideSpin += (int)gearBonusSideSpin;

            maxBackSpin = Mathf.Clamp(maxBackSpin, 0, 100);
            maxSideSpin = Mathf.Clamp(maxSideSpin, 0, 100);

            StartCoroutine(SetValueOfSlider());

            if (!currentBallImage.Equals("null"))
            {
                ResourcesManager.Instance.Rotate3DModelWithEuler(currentBallImage, sliderX * 33f, sliderY * 33f);
            }
        }

        // wait for ui get resolve style
        IEnumerator SetValueOfSlider()
        {
            topSpinArea.SetMaxValue(0, maxBackSpin / 100f);
            bottomSpinArea.SetMaxValue(0, maxBackSpin / 100f * -1f);
            rightSpinArea.SetMaxValue(maxSideSpin / 100f, 0);
            leftSpinArea.SetMaxValue(maxSideSpin / 100f * -1f, 0);

            yield return new WaitForEndOfFrame();
            yield return new WaitForEndOfFrame();

            if (spinCached == false)
            {
                ResetSpinCache();
                spinCached = true;
            }
            else
            {
                ballSlider.value = new Vector2(ScaleDownSliderValue(ballSliderX), ScaleDownSliderValue(ballSliderY));
                ballSlider.SetValueWithoutNotify(new Vector2(ScaleDownSliderValue(ballSliderX), ScaleDownSliderValue(ballSliderY)));
            }
        }

        private void ShowAttribute(BallAttribute attribute)
        {
            if (attribute == BallAttribute.NONE)
            {
                ExtraDistanceTooltip.style.visibility = Visibility.Hidden;
                WindResistanceTooltip.style.visibility = Visibility.Hidden;
                BouncingTooltip.style.visibility = Visibility.Hidden;
                SideSpinTooltip.style.visibility = Visibility.Hidden;

                _ballPanelClickZone.style.display = DisplayStyle.None;
                return;
            }
            else if (attribute == BallAttribute.EXTRA_DISTANCE)
            {
                ExtraDistanceTooltip.style.visibility = Visibility.Visible;
                WindResistanceTooltip.style.visibility = Visibility.Hidden;
                BouncingTooltip.style.visibility = Visibility.Hidden;
                SideSpinTooltip.style.visibility = Visibility.Hidden;
            }
            else if (attribute == BallAttribute.WIND_RESISTANCE)
            {
                ExtraDistanceTooltip.style.visibility = Visibility.Hidden;
                WindResistanceTooltip.style.visibility = Visibility.Visible;
                BouncingTooltip.style.visibility = Visibility.Hidden;
                SideSpinTooltip.style.visibility = Visibility.Hidden;
            }
            else if (attribute == BallAttribute.BOUNCING)
            {
                ExtraDistanceTooltip.style.visibility = Visibility.Hidden;
                WindResistanceTooltip.style.visibility = Visibility.Hidden;
                BouncingTooltip.style.visibility = Visibility.Visible;
                SideSpinTooltip.style.visibility = Visibility.Hidden;
            }
            else if (attribute == BallAttribute.SIDE_SPIN)
            {
                ExtraDistanceTooltip.style.visibility = Visibility.Hidden;
                WindResistanceTooltip.style.visibility = Visibility.Hidden;
                BouncingTooltip.style.visibility = Visibility.Hidden;
                SideSpinTooltip.style.visibility = Visibility.Visible;
            }

            _ballPanelClickZone.style.display = DisplayStyle.Flex;
        }

        void SwitchClubs()
        {
            playerGadget.SwitchClub(clubType);
            ResetSpinCache();
        }

        // BALL PANEL
        void focusPageHandler(int index)
        {
            if (index == -1)
            {
                return;
            }

            if (focusedPageBuffer == index)
            {
                return;
            }

            for (int i = 0; i < ballPagerItems.Count; i++)
            {
                if (i == index)
                {
                    ballPagerItems[i].Q<VisualElement>("PageImage").SetBackgroundImage((ResourcesManager.Instance.GetSpriteFromAtlas("PagerFull")));
                }
                else
                {
                    ballPagerItems[i].Q<VisualElement>("PageImage").SetBackgroundImage((ResourcesManager.Instance.GetSpriteFromAtlas("PagerEmpty")));
                }
            }

            focusedPageBuffer = index;
        }

        private void OnBallChosen(int ballIndex, bool cached = false)
        {
            for (int i = 0; i < ballsList.Count; i++)
            {
                if (i == ballIndex)
                {
                    _cachedBallItems[i].Q<VisualElement>("Equip").style.display = DisplayStyle.Flex;
                }
                else
                {
                    _cachedBallItems[i].Q<VisualElement>("Equip").style.display = DisplayStyle.None;
                }
            }

            if (currentBall != ballIndex)
            {
                Sync(Lobby.PlayerCommandSignature.ChangeBallCommand, new object[] {
                    ballsList[currentBall].Data.ItemId
                });
            }

            currentBall = ballIndex;

            if (cached == false)
            {
                ResetSpinCache();
            }

            playerGadget.ChosenBall = ballsList[currentBall].Data;
            OpenClubTab(); // OpenClubTab() to update the ball image
        }

        private void OnGearChosen(int gearIndex, bool cached = false)
        {
            UnityEngine.Debug.Log("OnGearChosen: " + gearIndex);
            if (gearIndex == 0)
            {
                currentGearIcon.SetBackgroundImage((Sprite)null);
                currentGearDesc.text = "No gear chosen";
            }
            else
            {
                currentGearIcon.SetBackgroundImage(ResourcesManager.Instance.GetGearSprite(gearsList[gearIndex - 1].Data.ItemId));
                var gearType = gearsList[gearIndex - 1].Data.serverData.customData.type;
                currentGearDesc.text = ResourcesManager.Instance.GetGearDescription(gearType);
            }

            bool isThisGearDisable = false;
            bool isThisGearUsableInGameMode = false;
            bool isThisGearOneUseConsumedOnHitUsed = false;
            bool isUnchangeableUponPickUsed = false;

            if (0 <= gearIndex - 1 && gearIndex - 1 < gearsList.Count)
            {
                GearData currentGearData = gearsList[gearIndex - 1].Data.serverData.customData;
                isThisGearUsableInGameMode = CheckIfGearIsUsableInGameMode(currentGearData);
                isThisGearOneUseConsumedOnHitUsed = CheckOneUseConsumedOnHitGearUsed(currentGearData);
                isUnchangeableUponPickUsed = CheckIfUnchangeableGearCauseDisable(currentGearData);

                isThisGearDisable = !isThisGearUsableInGameMode || isThisGearOneUseConsumedOnHitUsed || isUnchangeableUponPickUsed;
            }
            else if (gearIndex - 1 < 0)
            {
                isUnchangeableUponPickUsed = CheckIfAnyUnchangeableUponPickGearUsed();

                isThisGearDisable = isUnchangeableUponPickUsed;
            }

            if (isThisGearDisable)
            {
                string errorDescription = "";

                if (!isThisGearUsableInGameMode)
                {
                    errorDescription = "This gear is not available in this game mode!";
                }
                if (isThisGearOneUseConsumedOnHitUsed)
                {
                    errorDescription = "This gear can only be used once per match!";
                }
                if (isUnchangeableUponPickUsed)
                {
                    errorDescription = "This gear cannot be unequiped or switched for this shot!";
                }

                GeneralPopupContainer popupContainer = new GeneralPopupContainer
                (
                    title: "GEAR NOTICE",
                    description: errorDescription,
                    continueButtonText: "Got it!",
                    continueAction: () =>
                    {
                        MasterManager.Instance.HideUIComponent(UIComponentEnum.GeneralPopupUIComponent);
                    }
                );

                _ = MasterManager.Instance.OpenUIComponent(UIComponentEnum.GeneralPopupUIComponent, new object[] { popupContainer });
                return;
            }

            for (int i = 0; i < gearPages; i++)
            {
                for (int j = 0; j < 4; j++)
                {
                    int _gearIndex = i * 4 + j;

                    if (_gearIndex == gearIndex)
                    {
                        gearves[i].Q<VisualElement>(j.ToString()).Q<VisualElement>("Equip").style.display = DisplayStyle.Flex;
                        gearves[i].Q<VisualElement>(j.ToString()).Q<VisualElement>("BallInfoItem").style.opacity = 1f;
                    }
                    else
                    {
                        if (0 <= _gearIndex - 1 && _gearIndex - 1 < gearsList.Count)
                        {
                            bool _isOneUseConsumedOnHitUsed = false;
                            bool _isThisGearUsableInGameMode = false;
                            GearData currentGearData = gearsList[_gearIndex - 1].Data.serverData.customData;
                            _isOneUseConsumedOnHitUsed = CheckOneUseConsumedOnHitGearUsed(currentGearData);

                            if (_isOneUseConsumedOnHitUsed)
                            {
                                gearves[i].Q<VisualElement>(j.ToString()).Q<VisualElement>("Equip").style.display = DisplayStyle.Flex;
                                gearves[i].Q<VisualElement>(j.ToString()).Q<VisualElement>("BallInfoItem").style.opacity = 0.4f;

                                gearves[i].Q<VisualElement>(j.ToString()).Q<Label>("ItemNumber").text = "EQUIPPED";

                            }
                            else if (_isThisGearUsableInGameMode)
                            {
                                gearves[i].Q<VisualElement>(j.ToString()).Q<VisualElement>("Equip").style.display = DisplayStyle.None;
                                gearves[i].Q<VisualElement>(j.ToString()).Q<VisualElement>("BallInfoItem").style.opacity = 0.4f;
                            }
                            else
                            {
                                gearves[i].Q<VisualElement>(j.ToString()).Q<VisualElement>("Equip").style.display = DisplayStyle.None;
                                gearves[i].Q<VisualElement>(j.ToString()).Q<VisualElement>("BallInfoItem").style.opacity = 1f;
                            }
                        }
                        else
                        {
                            gearves[i].Q<VisualElement>(j.ToString()).Q<VisualElement>("Equip").style.display = DisplayStyle.None;
                            gearves[i].Q<VisualElement>(j.ToString()).Q<VisualElement>("BallInfoItem").style.opacity = 1f;
                        }
                    }
                }
            }

            currentGear = gearIndex;
            if (gearIndex == 0)
            {
                playerGadget.ChosenChip = null;
            }
            else
            {
                playerGadget.ChosenChip = gearsList[currentGear - 1].Data;
            }

            if (cached == false)
            {
                if (gearIndex - 1 >= 0)
                {
                    GearInfo gearInfo = gearsList[gearIndex - 1];

                    if (gearInfo.DoesAffectBackSpin())
                    {
                        spinCached = false;
                    }
                }
                if (lastGear - 1 >= 0)
                {
                    GearInfo gearInfo = gearsList[lastGear - 1];

                    if (gearInfo.DoesAffectBackSpin())
                    {
                        spinCached = false;
                    }
                }
            }

            lastGear = gearIndex;
        }

        void focusGearPageHandler(int index)
        {
            if (index == -1)
            {
                return;
            }

            if (focusedGearPageBuffer == index)
            {
                return;
            }

            for (int i = 0; i < gearPagerItems.Count; i++)
            {
                if (i == index)
                {
                    gearPagerItems[i].Q<VisualElement>("PageImage").SetBackgroundImage((ResourcesManager.Instance.GetSpriteFromAtlas("PagerFull")));
                }
                else
                {
                    gearPagerItems[i].Q<VisualElement>("PageImage").SetBackgroundImage((ResourcesManager.Instance.GetSpriteFromAtlas("PagerEmpty")));
                }
            }

            focusedGearPageBuffer = index;
        }

        // UPDATE
        private void Update()
        {
            if (isPanelOpen && gearPanelInit == true && currentTab == GadgetPickerTab.GEAR)
            {
                int focusedPage = -1;
                for (int i = 0; i < gearves.Count; i++)
                {
                    if (gearScrollView.isChildCompletelyInViewport(gearves[i].Q<VisualElement>("Top").Q<VisualElement>("1")) &&
                        gearScrollView.isChildCompletelyInViewport(gearves[i].Q<VisualElement>("Top").Q<VisualElement>("2"))
                    )
                    {
                        focusedPage = i; break;
                    }
                }
                focusGearPageHandler(focusedPage);
            }
        }

        public float GetSideSpin()
        {
            return sliderX * _commonSettings.BaseSideSpin;
        }

        public float GetBackSpin()
        {
            return -sliderY * _commonSettings.BaseBackSpin;
        }

        public void UpdateBallList()
        {
            ballsList = playerGadget.GetPlayerBallList(inventoryItemsSO.balls);
        }

        public void CalculateBallChange()
        {
            ballPanelInit = false;
            _currentBallImageCache = -1;
            currentBallImage = "null";
            currentBall = 0;
            playerGadget.ChosenBall = ballsList[0].Data;
            ResetSpinCache();
        }

        public void CalculateGearChange()
        {
            var gear = playerGadget.ChosenChip;
            GearInfo gearInfo = null;
            if (gear != null)
            {
                gearInfo = gearsList.Where(x => x.Data.ItemId.Equals(gear.ItemId)).FirstOrDefault();
            }

            if (gearInfo == null)
            {
                currentGear = 0;
                gearPanelInit = false;
                return;
            }

            if (gearInfo.Amount > 0)
            {
                gearInfo.Amount--;
            }

            if (gearInfo.Amount == 0)
            {
                gearsList.Remove(gearInfo);
                currentGear = 0;
            }

            ResetGear();
        }

        public void ResetSpinCache()
        {
            spinCached = false;

            ballSlider.value = initValue;
            ballSlider.SetValueWithoutNotify(initValue);
        }

        public void ResetGear()
        {
            playerGadget.ChosenChip = null;
            currentGear = 0;
            gearPanelInit = false;
        }

        public void UpdateBallView()
        {
            var clubType = AimHelper.FindClubTypeUsingBallPosition(_balls.Find(x => x.IsLocalBall()).transform.position, hole.transform.position);
            if (CurrentClubType != clubType)
            {
                this.clubType = clubType; //For case if not open gadget picker
                ResetSpinCache();
            }

            if (currentGear == 0)
            {
                playerGadget.ChosenChip = null;
            }
        }

        #region check gear usage
        private bool CheckIfGearIsUsableInGameMode(GearData currentGearData)
        {
            if (GlobalSO.GameplayBus.currentLogic != null)
            {
                return GlobalSO.GameplayBus.currentLogic.CheckIfGameModeAllowThisGear(currentGearData);
            }

            return false;
        }

        private bool CheckOneUseConsumedOnHitGearUsed(GearData currentGearData)
        {
            if (GlobalSO.GameplayBus.currentLogic != null)
            {
                return GlobalSO.GameplayBus.currentLogic.IsOneUseConsumedOnHitGearUsed(currentGearData, isLocal: true);
            }

            return false;
        }

        private bool CheckIfUnchangeableGearCauseDisable(GearData currentGearData)
        {
            if (GlobalSO.GameplayBus.currentLogic != null)
            {
                if (GlobalSO.GameplayBus.currentLogic.IsAnyUnchangeableUponPickGearUsed() == false)
                {
                    return false;
                }
                else
                {
                    return !GlobalSO.GameplayBus.currentLogic.IsUnchangeableUponPickGearUsed(currentGearData);
                }
            }

            return false;
        }

        private bool CheckIfAnyUnchangeableUponPickGearUsed()
        {
            if (GlobalSO.GameplayBus.currentLogic != null)
            {
                return GlobalSO.GameplayBus.currentLogic.IsAnyUnchangeableUponPickGearUsed();
            }

            return false;
        }
        #endregion

        public void UpdateClubImage()
        {
            var clubType = AimHelper.FindClubTypeUsingBallPosition(_balls.Find(x => x.IsLocalBall()).transform.position, hole.transform.position);
            var mainClub = playerGadget.GetClub(clubType, isMain: true);
            _clubImage.SetBackgroundImage(ResourcesManager.Instance.GetClubSprite(mainClub.ItemId));
        }

        public void UpdateBallImage()
        {
            var chosenBall = playerGadget.ChosenBall;
            _ballImage.SetBackgroundImage(ResourcesManager.Instance.GetItemSprite(chosenBall.ItemId));
        }

        public void SetBallRefernceInScene(List<Ball> balls)
        {
            _balls = balls;

            UpdateBallImage();

            UpdateClubImage();
        }

        public void SetHole(HoleBehaviour holeBehaviour)
        {
            hole = holeBehaviour;
        }

        //Snap effect in ball page
        private void UpdateBallUIIndex(int index)
        {

            if (index == _currentSnapBallIndex)
            {
                return;
            }

            if (index < 0)
            {
                index = 0;
            }
            else if (index >= ballsList.Count)
            {
                index = ballsList.Count - 1;
            }

            _currentSnapBallIndex = index;

            for (int i = 0; i < ballsList.Count; i++)
            {
                if (i == index)
                {
                    _cachedBallItems[i].RemoveFromClassList("unfocus-ball");
                }
                else
                {
                    _cachedBallItems[i].AddToClassList("unfocus-ball");
                }
            }
        }

        private void SnapToCurrentPickedBall(int index)
        {
            float targetPosition = index * ballScrollViewHeight * 0.33f;
            ballScrollView.verticalScroller.value = targetPosition;
            var cachedSnap = ballScrollView.snapDurationSec;
            ballScrollView.snapDurationSec = 0.01f;

            ExcuteSnap(cachedSnap).Forget();
        }

        private async UniTask ExcuteSnap(float cachedSnapDuration)
        {
            ballScrollView.Snap();
            await UniTask.DelayFrame(2);
            ballScrollView.snapDurationSec = cachedSnapDuration;
        }
    }

    public enum GadgetPickerTab
    {
        CLUB = 0,
        BALL = 1,
        GEAR = 2,
    }

    public enum BallAttribute
    {
        NONE = -1,
        EXTRA_DISTANCE = 0,
        WIND_RESISTANCE = 1,
        BOUNCING = 2,
        SIDE_SPIN = 3,
    }
}