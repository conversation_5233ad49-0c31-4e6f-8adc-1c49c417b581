using _Golf.Scripts.Common;
using _Golf.Scripts.ScriptableObjects;
using _Golf.Scripts.ScriptableObjects.Item;
using _Golf.Scripts.UI;
using System.Collections;
using System.Linq;
using _Golf.Scripts.Core;
using UnityEngine;
using UnityEngine.UI;
using _Golf.Scripts.Common.Helper;
using _Golf.Scripts.Lobby;
using DG.Tweening;
using GolfPhysics;
using NUnit.Framework;
using System.Collections.Generic;

namespace GolfGame
{
    public enum DragState
    {
        None = 0,
        Under = 1,
        Exact = 2,
        Over = 3,
    }

    public class GolfBallSwingDragUIVersion2 : GolfBallSwingDragUISample, ModuleComponent
    {
        #region Mediators
        private GameplayGolfBallSwingUIComponentMediator mediator;
        private GolfBallSwingUIManager manager;
        private GolfBallSwingUIProxy proxy;
        #endregion

        #region Debug
        [SerializeField] private bool isDebug;
        [SerializeField] private Button autoHit;
        #endregion

        #region Exposed Attributes
        [SerializeField] private GolfBallSwingDragVersion2 golfBallSwingDrag;
        [SerializeField] private RawImage dragBallImageStill;

        [SerializeField] private Image dragCircle;                                // EXACT
        [SerializeField] private Image dragCircleExact;                           // EXACT
        [SerializeField] private Image overDraggedBG;                             // EXACT
        [SerializeField] private ParticleSystem _launchParticles;

        [SerializeField] private GameObject Swing;
        [SerializeField] private GameObject TopView;
        [SerializeField] private Button SwingButton;
        [SerializeField] private Button TopViewButton;
        [SerializeField] private Button GadgetPickerClubButton;
        [SerializeField] private Button GadgetPickerBallButton;
        [SerializeField] private Button GadgetPickerGearButton;
        [SerializeField] private Image GadgetPickerButtonClubImage;
        [SerializeField] private Image GadgetPickerButtonBallImage;
        [SerializeField] private Button SpectateButton;

        [SerializeField] private GolfBallSwingDragLine GolfBallSwingDragLine;

        [SerializeField] private Image controllerDragAreaOuterCircle;             // EXACT
        [SerializeField] private Image controllerDragAreaEffectExact;             // EXACT
        [SerializeField] private Image controllerDragAreaEffectOver;              // EXACT

        [SerializeField] private GameObject controllerSlider;
        [SerializeField] private Image controllerSliderImageLeft;
        [SerializeField] private Image controllerSliderImageRight;
        [SerializeField] private GameObject controllerNeedle;
        [SerializeField] private GameObject controllerBallSideAngleIndicator;

        [SerializeField] private Image controllerNeedleImage;
        [SerializeField] private Image controllerNeedleImageBlur;
        [SerializeField] private Image controllerSliderImageBlur1;
        [SerializeField] private Image controllerSliderImageBlur2;
        [SerializeField] private Image exactArrow1;
        [SerializeField] private Image exactArrow2;
        [SerializeField] private Image exactGlow;
        #endregion

        #region Camera
        private Camera DragBallCamera;
        private Canvas UICanvas;
        #endregion

        #region Controller Script Components
        private ControllerSlider controllerSliderComponent;
        private ControllerNeedle controllerNeedleComponent;
        #endregion

        private Vector3 outerCircleLocalScale;
        private Color controllerNeedleImageColor;
        private Color controllerNeedleImageBlurColor;
        private Color controllerSliderImageBlurColor1;
        private Color controllerSliderImageBlurColor2;
        private Color sliderImageColor;
        private Color exactArrow1Color;
        private Color exactArrow2Color;
        private Color exactGlowColor;

        #region Config
        private const float outerLimit = 1.25f;
        #endregion

        #region UI-derived numbers
        private Vector3 upperBoundOfDragBallImageTransform;
        private Vector3 exactBoundOfDragBallImageTransform;

        private Vector3 exactBoundOfDragBallImageTransformWithSideSpin;
        #endregion

        #region Voodoo drag line position math
        private float stillImageYToDragLineDifference = 644.47f;
        private float exactBallHeightInCanvasForExactDragLineScale = 180f;
        private float ballHeightToDragLineScaleStep = 0.02f / 20f;
        #endregion

        #region Data and State
        private float dragPercentage      = 0f; // from 0 -> 1
        private float currentRawSideAngle = 0f;
        private float currentPhysicsAngle = 0f;
        private bool isTopView            = true;
        private DragState dragState       = DragState.None;
        private float controllerAngle     = 0f;
        private bool verticalPivot        = false;
        #endregion

        private float FinalSpeed => GlobalSO.NeedleProperties.FinalSpeed;
        private float needleMaximumAngle;
        private float dragPerfectDelta = 0.05f;

        private DragStateChangeAction dragStateChangeAction;

        public void Init(ModuleMediator _mediator)
        {
            upperBoundOfDragBallImageTransform = dragBallImageStill.gameObject.transform.position;
            cachedOpponentDragBallImagePosition = new Vector3(0f, upperBoundOfDragBallImageTransform.y, 100f);

            exactBoundOfDragBallImageTransform = controllerDragAreaEffectExact.gameObject.transform.position;

            outerCircleLocalScale = controllerDragAreaOuterCircle.transform.localScale;

            //sliderImageColor = controllerSliderImageLeft.color;
            exactArrow1Color = exactArrow1.color;
            exactArrow2Color = exactArrow2.color;
            //exactGlowColor = exactGlow.color;
            controllerSliderImageBlurColor1 = controllerSliderImageBlur1.color;
            controllerSliderImageBlurColor2 = controllerSliderImageBlur2.color;
            controllerNeedleImageColor = controllerNeedleImage.color;
            controllerNeedleImageBlurColor = controllerNeedleImageBlur.color;

            //

            golfBallSwingDrag.Init(upperBoundOfDragBallImageTransform.y, exactBoundOfDragBallImageTransform.y);
            golfBallSwingDrag.GetComponent<RawImage>().texture = ResourcesManager.Instance.DragBallTexture;
            dragBallImageStill.texture = ResourcesManager.Instance.DragBallTexture;

            GolfBallSwingDragLine.Init();

            mediator = (GameplayGolfBallSwingUIComponentMediator)_mediator;
            manager = (GolfBallSwingUIManager)_mediator.GetManager();
            proxy = (GolfBallSwingUIProxy)_mediator.GetProxy();

            manager.SetBall(mediator.GetBall());
            manager.SetHole(mediator.GetHole());

            DragBallCamera = CameraManager.Instance.GetDragBallCamera();

            golfBallSwingDrag.SetCanvas(UICanvas);

            golfBallSwingDrag.dragStarted += StartDragging;
            golfBallSwingDrag.dragEnded += StopDragging;
            golfBallSwingDrag.dragEndedDelayed += StopDraggingDelayed;
            golfBallSwingDrag.dragUpdateLine += DragUpdateLine;
            golfBallSwingDrag.swingBall += SwingBall;

            controllerSliderComponent = controllerSlider.GetComponent<ControllerSlider>(); 
            controllerSliderComponent.SetAngleDerivedPositionMultiplier(angleDerivedPositionMultiplier);
            exactGlowColor = controllerSliderComponent.GetBlurColor();

            controllerNeedleComponent = controllerNeedle.GetComponent<ControllerNeedle>();

            SwingButton.onClick.AddListener(SwingViewOnClickCallback);
            TopViewButton.onClick.AddListener(TopViewOnClickCallback);
            GadgetPickerClubButton.onClick.AddListener(() => GadgetPickerOnClickCallback(0));
            // GadgetPickerBallButton.onClick.AddListener(() => GadgetPickerOnClickCallback(1));
            // GadgetPickerGearButton.onClick.AddListener(() => GadgetPickerOnClickCallback(2));


            autoHit.onClick.AddListener(AutoHitButtonOnClickCallback);

            needleMaximumAngle = Mathf.Abs(CalculateRotationOfNeedle(upperBound * needleXMultiplier));
            controllerSliderComponent.SetNeedleMaximumAngle(needleMaximumAngle, CalculatePhysicsSideAngleValue(needleMaximumAngle));

            dragStateChangeAction = new DragStateChangeAction();
        }

        public void Dispose()
        {
            golfBallSwingDrag.dragStarted -= StartDragging;
            golfBallSwingDrag.dragEnded -= StopDragging;
            golfBallSwingDrag.dragEndedDelayed -= StopDraggingDelayed;
            golfBallSwingDrag.dragUpdateLine -= DragUpdateLine;
            golfBallSwingDrag.swingBall -= SwingBall;

            SwingButton.onClick.RemoveAllListeners();
            TopViewButton.onClick.RemoveAllListeners();
            GadgetPickerClubButton.onClick.RemoveAllListeners();
            // GadgetPickerBallButton.onClick.RemoveAllListeners();
            // GadgetPickerGearButton.onClick.RemoveAllListeners();


            autoHit.onClick.RemoveAllListeners();
        }

        private void ResetColor()
        {
            //controllerSliderImage.color = sliderImageColor;
            //controllerSliderImageBlur1.color = controllerSliderImageBlurColor1;
            //controllerSliderImageBlur2.color = controllerSliderImageBlurColor2;
            controllerNeedleImage.color = controllerNeedleImageColor;
            controllerNeedleImageBlur.color = controllerNeedleImageBlurColor;
            exactArrow1.color = exactArrow1Color;
            exactArrow2.color = exactArrow2Color;
            //exactGlow.color = exactGlowColor;
        }

        #region Fetch Data and Config
        /// <summary>
        /// Fetch perfect velocity delta config, used for rating ball dragging
        /// </summary>
        /// <returns></returns>
        private float FetchPerfectVelocityDeltaConfig()
        {
            float result = 0f;

            string rankId = GlobalSO.GameplayBus.localPlayer.GetPlayerRankInfo().rankId;

            List<GolfShotRatingByRank> ratingConfig = GlobalSO.RemoteConfigData.GolfShotRating.GolfShotRatingByRanks;

            foreach (var golfShotRating in ratingConfig)
            {
                if (golfShotRating.RankId == rankId)
                {
                    result = golfShotRating.PerfectShotVelocityDelta;
                }
            }

            // check debug
            if (GlobalSO.LocalGameSetting.IsDebuggingRankShotData)
            {
                return GlobalSO.LocalGameSetting.PerfectShotVelocityDelta;
            }

            if (result == 0)
            {
                Debug.LogError("Perfect Velocity Delta Config cannot be 0.");
                return 0.1f; // default
            }
            else
            {
                return result;
            }
        }

        /// <summary>
        /// Fetch great velocity delta config, used for rating ball dragging
        /// </summary>
        /// <returns></returns>
        private float FetchGreatVelocityDeltaConfig()
        {
            float result = 0f;

            string rankId = GlobalSO.GameplayBus.localPlayer.GetPlayerRankInfo().rankId;

            List<GolfShotRatingByRank> ratingConfig = GlobalSO.RemoteConfigData.GolfShotRating.GolfShotRatingByRanks;

            foreach (var golfShotRating in ratingConfig)
            {
                if (golfShotRating.RankId == rankId)
                {
                    result = golfShotRating.GreatShotVelocityDelta;
                }
            }

            // check debug
            if (GlobalSO.LocalGameSetting.IsDebuggingRankShotData)
            {
                return GlobalSO.LocalGameSetting.GreatShotVelocityDelta;
            }

            if (result == 0)
            {
                Debug.LogError("Great Velocity Delta Config cannot be 0.");
                return 0.2f; // default
            }
            else
            {
                return result;
            }
        }

        /// <summary>
        /// Config for perfect degree, used for PHYSICS
        /// </summary>
        /// <returns></returns>
        

        private float FetchAccuracyDeltaConfig()
        {
            return GlobalSO.RemoteConfigData.ControllerConfig.NormalShotConfig.AccuracyDelta;
        }

        private float FetchMinAccuracyConfig()
        {
            return GlobalSO.RemoteConfigData.ControllerConfig.NormalShotConfig.MinAccuracy;
        }

        /// <summary>
        /// Fetch Perfect degree for UI visualization, used for UI
        /// </summary>
        /// <returns></returns>
        private float FetchPerfectDegreeForUIVisualization()
        {
            return GolfShotRatingHelper.FetchPerfectDegreeConfig() /
                (GetSideAngleMultiplierFactor() / needleMaximumAngle) * 
                (1 + FetchAccuracyDeltaConfig() * (FetchCurrentClubAccuracy() - FetchMinAccuracyConfig()) / 100f);
        }

        /// <summary>
        /// Fetch Great degree for UI visualization, used for UI
        /// </summary>
        /// <returns></returns>
        private float FetchGreatDegreeForUIVisualization()
        {
            return GolfShotRatingHelper.FetchGreatDegreeConfig() /
                   (GetSideAngleMultiplierFactor() / needleMaximumAngle) * 
                   (1 + FetchAccuracyDeltaConfig() * (FetchCurrentClubAccuracy() - FetchMinAccuracyConfig()) / 100f);
        }

        private ClubSO FetchCurrentClubSO()
        {
            Ball ball = mediator.GetBall(); GameObject hole = mediator.GetHole();
            ClubSO mainClub = GlobalSO.PlayerGadget.GetClub(AimHelper.FindClubTypeUsingBallPosition(ball.transform.position, hole.transform.position), isMain: true);

            return mainClub;
        }

        private ClubInfo FetchCurrentClubPlayerData()
        {
            return GlobalSO.PlayerGadget.GetPlayerClubData(FetchCurrentClubSO());
        }

        private float FetchCurrentClubAccuracy()
        {
            ClubInfo clubInfo = FetchCurrentClubPlayerData();
            return clubInfo.GetAccuracy();
        }

        private float GetSideAngleMultiplierFactor()
        {
            if (GlobalSO.LocalGameSetting.IsDebuggingRankShotData)
            {
                return GlobalSO.LocalGameSetting.SideAngleMultiplyingFactor;
            }

            string rankId = GlobalSO.GameplayBus.localPlayer.GetPlayerRankInfo().rankId;

            return GolfShotRatingHelper.GetSideAngleMultiplierFactor(rankId, GlobalSO.RemoteConfigData.GolfShotRating.GolfShotRatingByRanks);
        }
        #endregion

        private void SetPositionForExactPositionComponent(Vector3 exactPosition)
        {
            dragCircle.gameObject.transform.position = exactPosition;
            dragCircleExact.gameObject.transform.position = exactPosition;
            overDraggedBG.gameObject.transform.position = exactPosition;
            controllerDragAreaOuterCircle.gameObject.transform.position = exactPosition;
            controllerDragAreaEffectExact.gameObject.transform.position = exactPosition;
            controllerDragAreaEffectOver.gameObject.transform.position = exactPosition;
        }

        private void StartDraggingSliderAnimation()
        {
            float sliderExpandTime = 0.35f;

            float perfectDegree = FetchPerfectDegreeForUIVisualization();
            float greatDegree = FetchGreatDegreeForUIVisualization();

            controllerSlider.SetActive(true);
            controllerSliderComponent.SetActiveDivider(true);

            controllerSliderComponent.SetPerfectAngle(perfectDegree, GolfShotRatingHelper.FetchPerfectDegreeConfig());
            controllerSliderComponent.SetGreatAngle(greatDegree, GolfShotRatingHelper.FetchGreatDegreeConfig());

            controllerSliderComponent.SetLimitDivider();
            controllerSliderComponent.SetLimitDividerText();

            controllerSliderComponent.BeginFillLerp(0, controllerSliderComponent.visualFullFillArch, sliderExpandTime, ControllerSliderType.Normal);
            controllerSliderComponent.BeginFillLerp(0, (greatDegree / needleMaximumAngle) * controllerSliderComponent.fullFillArch, sliderExpandTime, ControllerSliderType.OuterGreat);
            controllerSliderComponent.BeginFillLerp(0, (greatDegree / 2f / needleMaximumAngle) * controllerSliderComponent.fullFillArch, sliderExpandTime, ControllerSliderType.InnerGreat);
            controllerSliderComponent.BeginFillLerp(0, (perfectDegree / needleMaximumAngle) * controllerSliderComponent.fullFillArch, sliderExpandTime, ControllerSliderType.Perfect);
            controllerSliderComponent.SetFillAmount(ControllerSliderType.PerfectBlur, (perfectDegree / needleMaximumAngle) * controllerSliderComponent.fullFillArch);

            controllerSliderImageBlur1.color = new Color(controllerSliderImageBlurColor1.r, controllerSliderImageBlurColor1.g, controllerSliderImageBlurColor1.b, 0f);
            controllerSliderImageBlur2.color = new Color(controllerSliderImageBlurColor2.r, controllerSliderImageBlurColor2.g, controllerSliderImageBlurColor2.b, 0f);
            
            var sliderSequence = DOTween.Sequence();
            var sliderTransform = controllerSlider.transform;
            sliderSequence
                //.Append(controllerSliderImageLeft.DOColor(sliderImageColor, .5f))
                .Append(controllerSliderImageBlur1.DOColor(controllerSliderImageBlurColor1, .5f))
                .Join(controllerSliderImageBlur2.DOColor(controllerSliderImageBlurColor2, .5f))
                .onComplete = () =>
                {
                    //controllerSliderImageLeft.color = sliderImageColor;
                    controllerSliderImageBlur1.color = controllerSliderImageBlurColor1;
                    controllerSliderImageBlur2.color = controllerSliderImageBlurColor2;
                };

            exactArrow1.gameObject.SetActive(true);
            exactArrow2.gameObject.SetActive(true);
            controllerSliderComponent.SetActiveBlur(true); //exactGlow.gameObject.SetActive(true);

            exactArrow1.gameObject.transform.localScale = Vector3.one * 1f;
            exactArrow2.gameObject.transform.localScale = Vector3.one * 1f;
            controllerSliderComponent.SetBlurColor(new Color(exactGlowColor.r, exactGlowColor.g, exactGlowColor.b, 0f)); //exactGlow.color = new Color(exactGlowColor.r, exactGlowColor.g, exactGlowColor.b, 0f);
        }

        #region Drag Callbacks
        private void StartDragging()
        {
            MasterManager.Instance.SetRenderInterval(1);
            DragBallCamera.gameObject.SetActive(true);

            dragPerfectDelta = FetchPerfectVelocityDeltaConfig();

            controllerDragAreaOuterCircle.transform.localScale = outerCircleLocalScale;

            dragState = DragState.None; GolfBallSwingDragLine.SetState(dragState);
            dragStateChangeAction.dragState = dragState; ActionDispatcher.Dispatch(dragStateChangeAction);

            SetActiveForSwingButton(false);
            SetActiveForTopViewButton(false);
            SetActiveForGadgetPickerButton(false);
            SetActiveForSpectateButton(false);
            SetActiveForAutoHitButton(false);

            StartDraggingSliderAnimation();

            ResetColor();

            controllerNeedle.SetActive(true);
            controllerBallSideAngleIndicator.SetActive(true);

            dragCircle.gameObject.SetActive(true); dragCircleExact.gameObject.SetActive(false);
            overDraggedBG.gameObject.SetActive(false);

            var ball = mediator.GetBall();
            var liePenaltyConfig = GlobalSO.RemoteConfigData.LiePenaltyConfigs
                .FirstOrDefault(config => config.surfaceType == ball.CurrentSurfaceType.ToString());
            GlobalSO.NeedleProperties.LiePenaltyModifier = liePenaltyConfig?.needleSpeedPenalty ?? 0f;

            controllerAngle = 0f;

            sideAngleCoroutine = MoveSideAngleArrow();
            StartCoroutine(sideAngleCoroutine);

            ballSideAngleIndicator = MoveBallSideAngleIndicator();
            StartCoroutine(ballSideAngleIndicator);
        }

        private void DragUpdateLine(float x, float y, float verticalUIAngle, float physicsAngle, float dragPercent)
        {
            controllerAngle = verticalUIAngle;

            float verticalValue = y * -1f;
            float verticalDragPercentage = ((verticalValue - -upperBoundOfDragBallImageTransform.y) / (upperBoundOfDragBallImageTransform.y - exactBoundOfDragBallImageTransform.y));

            dragPercentage = dragPercent;

            GlobalSO.NeedleProperties.CalculateDragPenaltyModifier(Mathf.Clamp(dragPercentage, 1, 1.3f) - 1);

            controllerSliderComponent.UpdatePositionBasedOnAngle(controllerAngle);

            controllerSliderComponent.UpdateScale(dragPercentage);
            controllerNeedleComponent.UpdateScale(dragPercentage);

            controllerSlider.transform.localRotation = Quaternion.Euler(0, 0, controllerAngle);

            if (1 - dragPerfectDelta <= dragPercentage && dragPercentage <= 1 + dragPerfectDelta)
            {
                controllerDragAreaEffectExact.gameObject.SetActive(true);
                controllerDragAreaEffectOver.gameObject.SetActive(false);

                controllerDragAreaOuterCircle.gameObject.SetActive(true);

                controllerDragAreaOuterCircle.transform.localScale = outerCircleLocalScale * 1f;

                if (dragState != DragState.Exact)
                {
                    dragState = DragState.Exact; GolfBallSwingDragLine.SetState(dragState);
                    dragStateChangeAction.dragState = dragState; ActionDispatcher.Dispatch(dragStateChangeAction);

                    dragCircle.gameObject.SetActive(false); dragCircleExact.gameObject.SetActive(true);
                    overDraggedBG.gameObject.SetActive(false);
                }
            }
            else if (dragPercentage < 1 - dragPerfectDelta)
            {
                controllerDragAreaEffectExact.gameObject.SetActive(false);
                controllerDragAreaEffectOver.gameObject.SetActive(false);

                controllerDragAreaOuterCircle.gameObject.SetActive(true);
                controllerDragAreaOuterCircle.transform.localScale = outerCircleLocalScale * (outerLimit - (outerLimit - 1f) * dragPercentage);

                if (dragState != DragState.Under)
                {
                    dragState = DragState.Under; GolfBallSwingDragLine.SetState(dragState);
                    dragStateChangeAction.dragState = dragState; ActionDispatcher.Dispatch(dragStateChangeAction);

                    dragCircle.gameObject.SetActive(true); dragCircleExact.gameObject.SetActive(false);
                    overDraggedBG.gameObject.SetActive(false);
                }
            }
            else
            {
                controllerDragAreaEffectExact.gameObject.SetActive(false);
                controllerDragAreaEffectOver.gameObject.SetActive(true);

                controllerDragAreaOuterCircle.gameObject.SetActive(false);

                if (dragState != DragState.Over)
                {
                    dragState = DragState.Over; GolfBallSwingDragLine.SetState(dragState);
                    dragStateChangeAction.dragState = dragState; ActionDispatcher.Dispatch(dragStateChangeAction);

                    dragCircle.gameObject.SetActive(false); dragCircleExact.gameObject.SetActive(false);
                    overDraggedBG.gameObject.SetActive(true);
                }
            }

            GolfBallSwingDragLine.Set(verticalDragPercentage, verticalUIAngle, golfBallSwingDrag.GetCachedPos());
        }

        public void StopDragging(bool successful)
        {
            DragBallCamera.gameObject.SetActive(false);

            SetActiveForSwingButton(false);
            SetActiveForTopViewButton(false);
            SetActiveForGadgetPickerButton(false);
            SetActiveForSpectateButton(false);
            SetActiveForAutoHitButton(false);
            controllerBallSideAngleIndicator.SetActive(false);

            if (successful)
            {
                dragBallImageStill.gameObject.SetActive(false);

                CheckIfPerfect(true);
                SliderAnimation();
            }

            controllerDragAreaOuterCircle.gameObject.SetActive(false);
            controllerDragAreaEffectExact.gameObject.SetActive(false);
            controllerDragAreaEffectOver.gameObject.SetActive(false);

            dragCircle.gameObject.SetActive(false); dragCircleExact.gameObject.SetActive(false);
            overDraggedBG.gameObject.SetActive(false);

            StopCoroutine(sideAngleCoroutine);
            StopCoroutine(ballSideAngleIndicator);

            dragState = DragState.None; GolfBallSwingDragLine.SetState(dragState);
            dragStateChangeAction.dragState = dragState; ActionDispatcher.Dispatch(dragStateChangeAction);

            controllerAngle = 0f;
        }

        private bool CheckIfPerfect(bool visual)
        {
            float perfectDegreePhysics = GolfShotRatingHelper.FetchPerfectDegreeConfig();

            bool perfect = (Mathf.Abs(currentPhysicsAngle) <= perfectDegreePhysics);

            if (visual)
            {
                exactArrow1.gameObject.SetActive(perfect);
                exactArrow2.gameObject.SetActive(perfect);
                controllerSliderComponent.SetActiveBlur(perfect); //exactGlow.gameObject.SetActive(perfect);
            }

            return perfect;
        }

        private void SliderAnimation()
        {
            var sequence = DOTween.Sequence();
            var sliderTransform = controllerSlider.transform;

            sequence
                .Append(sliderTransform.DOScaleY(sliderTransform.localScale.y + 0.45f, .35f))
                .Join(sliderTransform.DOScaleX(sliderTransform.localScale.x - 0.1f, .35f))
                .onComplete = () =>
                {
                    float perfectDegree = FetchPerfectDegreeForUIVisualization();
                    float greatDegree = FetchGreatDegreeForUIVisualization();

                    float sliderExpandTime = 0.35f;

                    controllerSliderComponent.BeginFillLerp(controllerSliderComponent.fullFillArch, 0, sliderExpandTime, ControllerSliderType.Normal);
                    controllerSliderComponent.BeginFillLerp((greatDegree / needleMaximumAngle) * controllerSliderComponent.fullFillArch, 0, sliderExpandTime, ControllerSliderType.OuterGreat);
                    controllerSliderComponent.BeginFillLerp((greatDegree / 2f / needleMaximumAngle) * controllerSliderComponent.fullFillArch, 0, sliderExpandTime, ControllerSliderType.InnerGreat);
                    controllerSliderComponent.BeginFillLerp((perfectDegree / needleMaximumAngle) * controllerSliderComponent.fullFillArch, 0, sliderExpandTime, ControllerSliderType.Perfect);
                    controllerSliderComponent.BeginFillLerp((perfectDegree / needleMaximumAngle) * controllerSliderComponent.fullFillArch, 0, sliderExpandTime, ControllerSliderType.PerfectBlur);

                    DOTween.Sequence()
                        .Append(sliderTransform.DOScaleY(1, .35f))
                        .Join(sliderTransform.DOScaleX(1, .35f))
                        //.Join(controllerSliderImageLeft.DOColor(new Color(0f, 0f, 0f, 0f), .35f))
                        .Join(controllerSliderImageBlur1.DOColor(new Color(0f, 0f, 0f, 0f), .35f))
                        .Join(controllerSliderImageBlur2.DOColor(new Color(0f, 0f, 0f, 0f), .35f))
                        .Join(controllerNeedleImage.DOColor(new Color(0f, 0f, 0f, 0f), .35f))
                        .Join(controllerNeedleImageBlur.DOColor(new Color(0f, 0f, 0f, 0f), .35f))
                        .Join(exactArrow1.DOColor(new Color(0f, 0f, 0f, 0f), .35f))
                        .Join(exactArrow2.DOColor(new Color(0f, 0f, 0f, 0f), .35f))
                        //.Join(exactGlow.DOColor(new Color(0f, 0f, 0f, 0f), .35f))
                        .onComplete = () =>
                        {
                            controllerSliderComponent.SetActiveDivider(false);
                        };
                    ;
                };
        }

        public void StopDraggingDelayed(bool successful)
        {
            controllerSlider.SetActive(false);
            controllerNeedle.SetActive(false);

            gameObject.SetActive(!successful);
            SetActiveForTopViewButton(!successful);
            SetActiveForGadgetPickerButton(!successful);
            SetActiveForSpectateButton(!successful);
            SetActiveForAutoHitButton(!successful);

            ResetColor();
            MasterManager.Instance.SetRenderInterval(2);
        }

        private void SwingBall(float value, float sideAngle)
        {
            bool perfectDrag = 1 - dragPerfectDelta <= dragPercentage && dragPercentage <= 1 + dragPerfectDelta;
            float finalDrag = perfectDrag ? 1f : dragPercentage;

            manager.SwingBall(finalDrag, false, sideAngle);

            GolfShotRatingHelper.RateGolfShot(
                physicsSideAngle: CheckIfPerfect(false) ? 0 : currentPhysicsAngle, 
                perfectSideAngle: GolfShotRatingHelper.FetchPerfectDegreeConfig(),
                greatSideAngle: GolfShotRatingHelper.FetchGreatDegreeConfig()
            );

            _launchParticles.Play();
        }
        #endregion

        #region Controller Math
        /// <summary>
        /// Leftmost value of x raw value
        /// </summary>
        private const float bottomBound = -2.25f;
        /// <summary>
        /// Rightmost value of x raw lavue
        /// </summary>
        private const float upperBound = 2.25f;
        /// <summary>
        /// Multiply this value with raw x => Needle actual x value
        /// </summary>
        private float needleXMultiplier = 144f;
        /// <summary>
        /// Multiply this value with raw x => Angle actual x value
        /// </summary>
        private float angleIndicatorXMultiplier = 35f;

        /// <summary>
        /// 3 parameters to calculate needle position
        /// </summary>
        private float needlePositionFunctionParameterA = -0.00122507f;
        private float needlePositionFunctionParameterB = +0.021234f;
        private float needlePositionFunctionParameterC = +190f;

        /// <summary>
        /// Calculate actualy y position of needle using actual x
        /// </summary>
        /// <param name="x"></param>
        /// <returns></returns>
        private float CalculateYPositionOfNeedle(float x)
        {
            return 
                needlePositionFunctionParameterA * Mathf.Pow(x, 2) + 
                needlePositionFunctionParameterB * x + 
                needlePositionFunctionParameterC;
        }

        /// <summary>
        /// 2 parameters to calculate needle rotation
        /// </summary>
        private float needleRotationFunctionParameterA = -0.140628f;
        private float needleRotationFunctionParameterB = +0.368952f;

        /// <summary>
        /// Calculate rotation of needle using actual x
        /// </summary>
        /// <param name="x"></param>
        /// <returns></returns>
        private float CalculateRotationOfNeedle(float x)
        {
            return
                needleRotationFunctionParameterA * x +
                needleRotationFunctionParameterB;
        }

        /// <summary>
        /// Position to move the and slider and needle under the effect of rotation
        /// </summary>
        private float angleDerivedPositionMultiplier = -5.0f;

        /// <summary>
        /// Convert UI angle of needle to physics angle
        /// </summary>
        /// <param name="controllerNeedleRotation"></param>
        /// <returns></returns>
        private float CalculatePhysicsSideAngleValue(float controllerNeedleRotation)
        {
            return -controllerNeedleRotation * 
                (GetSideAngleMultiplierFactor() / needleMaximumAngle) /
                (1 + FetchAccuracyDeltaConfig() * ((FetchCurrentClubAccuracy() - FetchMinAccuracyConfig()) / 100f));
        }

        /// <summary>
        /// Convert physics angle to UI angle of needle (used for opponent)
        /// Deprecated because this formula requires club accuracy of opponent.
        /// To fix later
        /// </summary>
        /// <param name="physicsSideAngleValue"></param>
        /// <returns></returns>
        private float CalculateControllerNeedleRotation(float physicsSideAngleValue)
        {
            return -physicsSideAngleValue * needleMaximumAngle / GetSideAngleMultiplierFactor()
                // * (1 + FetchAccuracyDeltaConfig() * (FetchCurrentClubAccuracy() - FetchMinAccuracyConfig()))
                ;
        }

        /// <summary>
        /// 3 parameters to calculate side angle indicator position
        /// </summary>
        private float sideAngleIndicatorPositionFunctionParameterA = -0.00635647f;
        private float sideAngleIndicatorPositionFunctionParameterB = +0.00428792f;

        /// <summary>
        /// Calculate actualy y position of side angle indicator using actual x
        /// </summary>
        /// <param name="x"></param>
        /// <returns></returns>
        private float CalculateYPositionOfSideAngleIndicator(float x)
        {
            return 
                sideAngleIndicatorPositionFunctionParameterA * Mathf.Pow(x, 2) +
                sideAngleIndicatorPositionFunctionParameterB * x +
                angleIndicatorHeight;
        }

        /// <summary>
        /// 3 parameters to calculate side angle indicator rotation
        /// </summary>
        private float sideAngleIndicatorRositionFunctionParameterA = -0.588251f;
        private float sideAngleIndicatorRositionFunctionParameterB = +0.533258f;

        /// <summary>
        /// Calculate rotation of side angle indicator using actual x
        /// </summary>
        /// <param name="x"></param>
        /// <returns></returns>
        private float CalculateRotationOfSideAngleIndicator(float x)
        {
            return 
                sideAngleIndicatorRositionFunctionParameterA * x +
                sideAngleIndicatorRositionFunctionParameterB;
        }

        // private float visualPerfectThreshold = 0.5f;

        private void RenderAimingIndicator()
        {
            float perfectDegreePhysics = GolfShotRatingHelper.FetchPerfectDegreeConfig();

            if (Mathf.Abs(currentPhysicsAngle) <= perfectDegreePhysics)
            {
                float a = 1f - 0.8f * (Mathf.Abs(currentPhysicsAngle) / perfectDegreePhysics);
                controllerSliderComponent.SetBlurColor(new Color(exactGlowColor.r, exactGlowColor.g, exactGlowColor.b, a)); //exactGlow.color = new Color(exactGlowColor.r, exactGlowColor.g, exactGlowColor.b, a);

                float scale = 1.5f - 0.5f * (Mathf.Abs(currentPhysicsAngle) / perfectDegreePhysics);
                exactArrow1.gameObject.transform.localScale = Vector3.one * scale;
                exactArrow2.gameObject.transform.localScale = Vector3.one * scale;
            }
            else
            {
                controllerSliderComponent.SetBlurColor(new Color(exactGlowColor.r, exactGlowColor.g, exactGlowColor.b, 0f)); //exactGlow.color = new Color(exactGlowColor.r, exactGlowColor.g, exactGlowColor.b, 0f);
                exactArrow1.gameObject.transform.localScale = Vector3.one * 1f;
                exactArrow2.gameObject.transform.localScale = Vector3.one * 1f;
            }
        }

        private IEnumerator sideAngleCoroutine;
        private IEnumerator MoveSideAngleArrow()
        {
            float xRaw = bottomBound;
            bool up = true;

            while (true)
            {
                if (up)
                {
                    float yPos = CalculateYPositionOfNeedle(xRaw * needleXMultiplier);
                    float rotation = CalculateRotationOfNeedle(xRaw * needleXMultiplier);

                    Vector3 currentPosition = CalculateCurrentNeedlePosition(new Vector3(xRaw * needleXMultiplier, yPos, 0));

                    controllerNeedle.transform.localPosition = currentPosition + new Vector3(controllerAngle * angleDerivedPositionMultiplier, 0, 0);
                    controllerNeedle.transform.localRotation = Quaternion.Euler(0, 0, rotation + controllerAngle);

                    float physicsSideAngleValue = CalculatePhysicsSideAngleValue(rotation); //Debug.Log("UI rotation: " + rotation + "/ Physics angle: " + physicsSideAngleValue);
                    currentPhysicsAngle = physicsSideAngleValue;
                    manager.SetSideAngleInput(CheckIfPerfect(false) ? 0 : physicsSideAngleValue);

                    controllerSliderComponent.SetNeedleAngle(rotation, physicsSideAngleValue);

                    // iterate x value
                    xRaw += ((upperBound * 2f) * FinalSpeed) * Time.deltaTime * 1f;
                    if (xRaw > upperBound)
                    {
                        xRaw = upperBound; //Debug.Log("X Raw UpBound Position: " + currentPosition); Debug.Log("X Raw UpBound Position buffer: " + new Vector3(controllerAngle * angleDerivedPositionMultiplier, 0, 0)); Debug.Log("X Raw UpBound Rotation: " + rotation);
                        up = false;

                        //controllerNeedle.GetComponent<ControllerNeedle>().trailBase.Clear();
                        //controllerNeedle.GetComponent<ControllerNeedle>().trailBaseParticles.Clear();
                    }
                    // wait for fps amount of time
                    yield return new WaitForSeconds(Time.deltaTime * 0.25f);
                    // done waiting, go to next loop

                    currentRawSideAngle = xRaw; 
                    
                    RenderAimingIndicator();

                    SyncInterval(PlayerCommandSignature.DragBallCommand, 
                        new object[] { dragPercentage, controllerAngle });

                    continue;
                }
                else
                {
                    float yPos = CalculateYPositionOfNeedle(xRaw * needleXMultiplier);
                    float rotation = CalculateRotationOfNeedle(xRaw * needleXMultiplier);

                    Vector3 currentPosition = CalculateCurrentNeedlePosition(new Vector3(xRaw * needleXMultiplier, yPos, 0));

                    controllerNeedle.transform.localPosition = currentPosition + new Vector3(controllerAngle * angleDerivedPositionMultiplier, 0, 0);
                    controllerNeedle.transform.localRotation = Quaternion.Euler(0, 0, rotation + controllerAngle);

                    float physicsSideAngleValue = CalculatePhysicsSideAngleValue(rotation); //Debug.Log("UI rotation: " + rotation + "/ Physics angle: " + physicsSideAngleValue);
                    currentPhysicsAngle = physicsSideAngleValue;
                    manager.SetSideAngleInput(CheckIfPerfect(false) ? 0 : physicsSideAngleValue);

                    controllerSliderComponent.SetNeedleAngle(rotation, physicsSideAngleValue);

                    // iterate x value
                    xRaw -= ((upperBound * 2f) * FinalSpeed) * Time.deltaTime * 1f;
                    if (xRaw < bottomBound)
                    {
                        xRaw = bottomBound; //Debug.Log("X Raw DownBound Position: " + currentPosition); Debug.Log("X Raw DownBound Position buffer: " + new Vector3(controllerAngle * angleDerivedPositionMultiplier, 0, 0)); Debug.Log("X Raw DownBound Rotation: " + rotation);
                        up = true;

                        //controllerNeedle.GetComponent<ControllerNeedle>().trailBase.Clear();
                        //controllerNeedle.GetComponent<ControllerNeedle>().trailBaseParticles.Clear();
                    }
                    // wait for fps amount of time
                    yield return new WaitForSeconds(Time.deltaTime * 0.25f);
                    // done waiting, go to next loop

                    currentRawSideAngle = xRaw; 
                    
                    RenderAimingIndicator();

                    SyncInterval(PlayerCommandSignature.DragBallCommand,
                        new object[] { dragPercentage, controllerAngle });

                    continue;
                }
            }
        }

        private Vector3 CalculateCurrentNeedlePosition(Vector3 rawPosition)
        {
            Vector3 currentPosition = rawPosition;
            currentPosition = Quaternion.Euler(0, 0, controllerAngle) * currentPosition;

            return currentPosition;
        }

        private IEnumerator ballSideAngleIndicator;
        private IEnumerator MoveBallSideAngleIndicator()
        {
            float x = bottomBound;
            bool up = true;
            while (true)
            {
                if (up)
                {
                    float yPosition = CalculateYPositionOfSideAngleIndicator(x * angleIndicatorXMultiplier);
                    float rotation = CalculateRotationOfSideAngleIndicator(x * angleIndicatorXMultiplier);

                    controllerBallSideAngleIndicator.transform.localPosition = new Vector3(x * angleIndicatorXMultiplier, yPosition, 0f);
                    controllerBallSideAngleIndicator.transform.localRotation = Quaternion.Euler(0f, 0f, rotation);

                    // iterate x value
                    x += ((upperBound * 2f) * FinalSpeed) * Time.deltaTime;
                    if (x > upperBound)
                    {
                        x = upperBound;
                        up = false;
                    }
                    // wait for fps amount of time
                    yield return new WaitForSeconds(Time.deltaTime * 0.25f);
                    // done waiting, go to next loop
                    continue;
                }
                else
                {
                    float yPosition = CalculateYPositionOfSideAngleIndicator(x * angleIndicatorXMultiplier);
                    float rotation = CalculateRotationOfSideAngleIndicator(x * angleIndicatorXMultiplier);

                    controllerBallSideAngleIndicator.transform.localPosition = new Vector3(x * angleIndicatorXMultiplier, yPosition, 0f);
                    controllerBallSideAngleIndicator.transform.localRotation = Quaternion.Euler(0f, 0f, rotation);

                    // iterate x value
                    x -= ((upperBound * 2f) * FinalSpeed) * Time.deltaTime;
                    if (x < bottomBound)
                    {
                        x = bottomBound;
                        up = true;
                    }
                    // wait for fps amount of time
                    yield return new WaitForSeconds(Time.deltaTime * 0.25f);
                    // done waiting, go to next loop
                    continue;
                }
            }
        }
        #endregion

        #region Set Active For Buttons
        private void SetActiveForSwingButton(bool active)
        {
            Swing.SetActive(active); SwingButton.gameObject.SetActive(active);
        }

        private void SetActiveForTopViewButton(bool active)
        {
            TopView.SetActive(active); TopViewButton.gameObject.SetActive(active);
        }

        private void SetActiveForGadgetPickerButton(bool active)
        {
            var isClosestToPinMode = GlobalSO.GameplayBus.currentLogic.logicData is SinglePlayerClosestToPinSO;
            if (isClosestToPinMode)
            {
                GadgetPickerClubButton.gameObject.SetActive(false);
                // GadgetPickerBallButton.gameObject.SetActive(false);
                // GadgetPickerGearButton.gameObject.SetActive(false);
                return;
            }
            GadgetPickerClubButton.gameObject.SetActive(active);
            // GadgetPickerBallButton.gameObject.SetActive(active);
            // GadgetPickerGearButton.gameObject.SetActive(active);
            if (active)
            {
                SetDataForGadgetPickerButton();
            }
        }

        public void SetDataForGadgetPickerButton()
        {
            Ball ball = mediator.GetBall(); GameObject hole = mediator.GetHole();
            var mainClub = GlobalSO.PlayerGadget.GetClub(AimHelper.FindClubTypeUsingBallPosition(ball.transform.position, hole.transform.position), isMain: true);
            GadgetPickerButtonClubImage.sprite = ResourcesManager.Instance.GetClubSprite(mainClub.ItemId);
            GadgetPickerButtonBallImage.sprite = ResourcesManager.Instance.GetItemSprite(GlobalSO.PlayerGadget.ChosenBall.ItemId);
        }

        public void SetActiveForSpectateButton(bool active)
        {
            if (active)
            {
                if (GlobalSO.GameplayBus.currentLogic is MultiplayerNormalLogic)
                {
                    SpectateButton.gameObject.SetActive(true);
                }
                else
                {
                    SpectateButton.gameObject.SetActive(false);
                }
            }
            else
            {
                SpectateButton.gameObject.SetActive(false);
            }
        }

        private void SetActiveForAutoHitButton(bool active)
        {
            if (isDebug && active)
            {
                if (isTopView)
                {
                    autoHit.gameObject.SetActive(false);
                }
                else
                {
                    autoHit.gameObject.SetActive(true);
                }
            }
            else
            {
                autoHit.gameObject.SetActive(false);
            }
        }
        #endregion

        #region Button callbacks
        public void SwingViewOnClickCallback()
        {
            isTopView = false;

            SetActiveForSwingButton(false);
            SetActiveForTopViewButton(true);
            SetActiveForGadgetPickerButton(false);
            SetActiveForSpectateButton(false);
            SetActiveForAutoHitButton(false);

            golfBallSwingDrag.gameObject.SetActive(false);
            dragBallImageStill.gameObject.SetActive(false);

            ActionDispatcher.Dispatch(new ChangeCameraModeAction(0));
            Sync(PlayerCommandSignature.GoToRearViewCommand, null);
        }

        public void TopViewOnClickCallback()
        {
            isTopView = true;

            SetActiveForSwingButton(true);
            SetActiveForTopViewButton(false);
            SetActiveForGadgetPickerButton(true);
            SetActiveForSpectateButton(false);
            SetActiveForAutoHitButton(false);

            golfBallSwingDrag.gameObject.SetActive(false);
            dragBallImageStill.gameObject.SetActive(false);

            ActionDispatcher.Dispatch(new ChangeCameraModeAction(1));
            Sync(PlayerCommandSignature.GoToTopViewCommand, null);
        }

        public void GadgetPickerOnClickCallback(int index)
        {
            object[] data = new object[3];

            data[0] = this;
            data[1] = 2;

            Ball ball = mediator.GetBall(); GameObject hole = mediator.GetHole();

            ClubType clubType = AimHelper.FindClubTypeUsingBallPosition(ball.transform.position, hole.transform.position);

            data[2] = clubType;

            _ = MasterManager.Instance.OpenUIComponent(UIComponentEnum.GadgetPickerComponentUI, data);

            SetActiveForSwingButton(false);
            SetActiveForTopViewButton(false);
        }

        GadgetPickerUIComponent gadgetPickerUIComponent;

        public void SetGadgetPicker(GadgetPickerUIComponent _gadgetPickerUIComponent)
        {
            gadgetPickerUIComponent = _gadgetPickerUIComponent;
            manager.SetGadgetPicker(gadgetPickerUIComponent);
        }

        public void GadgetPickerPanelClose()
        {
            SetActiveForSwingButton(isTopView);
            SetActiveForTopViewButton(!isTopView);
        }

        float angleIndicatorHeight = -159.13f;

        public void SetActiveDrag(bool able)
        {
            Ball currentBall = mediator.GetCurrentInSpectateBall();

            #region experiment code
            float dragCamFOV = DragBallCamera.fieldOfView;
            Vector3 dragBallCamToBallPos = - DragBallCamera.transform.position + currentBall.gameObject.transform.position;

            Vector3 dragBallCamToTopBallPos = Vector3.RotateTowards(dragBallCamToBallPos, Vector3.up, (float)Utility.DegreeToRadian(dragCamFOV / 2f), 0.1f);
            Vector3 dragBallCamToBotBallPos = Vector3.RotateTowards(dragBallCamToBallPos, Vector3.down, (float)Utility.DegreeToRadian(dragCamFOV / 2f), 0.1f);
            Vector3 dragBallCamToLeftBallPos = Vector3.RotateTowards(dragBallCamToBallPos, Vector3.left, (float)Utility.DegreeToRadian(dragCamFOV / 2f), 0.1f);
            Vector3 dragBallCamToRightBallPos = Vector3.RotateTowards(dragBallCamToBallPos, Vector3.right, (float)Utility.DegreeToRadian(dragCamFOV / 2f), 0.1f);

            float length = dragBallCamToBallPos.magnitude * Mathf.Cos((float)Utility.DegreeToRadian(dragCamFOV / 2f));

            dragBallCamToTopBallPos = dragBallCamToTopBallPos.normalized * length;
            dragBallCamToBotBallPos = dragBallCamToBotBallPos.normalized * length;
            dragBallCamToLeftBallPos = dragBallCamToLeftBallPos.normalized * length;
            dragBallCamToRightBallPos = dragBallCamToRightBallPos.normalized * length;

            Vector3 topBallPos = DragBallCamera.transform.position + dragBallCamToTopBallPos;
            Vector3 botBallPos = DragBallCamera.transform.position + dragBallCamToBotBallPos;
            Vector3 leftBallPos = DragBallCamera.transform.position + dragBallCamToLeftBallPos;
            Vector3 rightBallPos = DragBallCamera.transform.position + dragBallCamToRightBallPos;

            Vector3 topBallPosScreenPoint = CameraManager.Instance.GetMainCamera().WorldToScreenPoint(topBallPos);
            Vector3 botBallPosScreenPoint = CameraManager.Instance.GetMainCamera().WorldToScreenPoint(botBallPos);
            Vector3 leftBallPosScreenPoint = CameraManager.Instance.GetMainCamera().WorldToScreenPoint(leftBallPos);
            Vector3 rightBallPosScreenPoint = CameraManager.Instance.GetMainCamera().WorldToScreenPoint(rightBallPos);

            Vector2 topBallPosRectPosition;
            RectTransformUtility.ScreenPointToLocalPointInRectangle(
                (RectTransform)UICanvas.transform,
                topBallPosScreenPoint,
                UICanvas.worldCamera,
                out topBallPosRectPosition
                );

            Vector2 botBallPosRectPosition;
            RectTransformUtility.ScreenPointToLocalPointInRectangle(
                (RectTransform)UICanvas.transform,
                botBallPosScreenPoint,
                UICanvas.worldCamera,
                out botBallPosRectPosition
                );

            Vector2 leftBallPosRectPosition;
            RectTransformUtility.ScreenPointToLocalPointInRectangle(
                (RectTransform)UICanvas.transform,
                leftBallPosScreenPoint,
                UICanvas.worldCamera,
                out leftBallPosRectPosition
                );

            Vector2 rightBallPosRectPosition;
            RectTransformUtility.ScreenPointToLocalPointInRectangle(
                (RectTransform)UICanvas.transform,
                rightBallPosScreenPoint,
                UICanvas.worldCamera,
                out rightBallPosRectPosition
                );
            #endregion

            Vector3 ballScreenPoint = 
                CameraManager.Instance.GetMainCamera().WorldToScreenPoint(currentBall.gameObject.transform.position);

            Vector2 ballRectPosition;
            RectTransformUtility.ScreenPointToLocalPointInRectangle(
                (RectTransform)UICanvas.transform,
                ballScreenPoint,
                UICanvas.worldCamera,
                out ballRectPosition
                );
            
            Vector3 ballPosition = UICanvas.transform.TransformPoint(ballRectPosition);

            float ballHeightInCanvas = Vector3.Distance(topBallPosRectPosition, botBallPosRectPosition);
            float ballWidthInCanvas = Vector3.Distance(leftBallPosRectPosition, rightBallPosRectPosition);
            angleIndicatorHeight = ballRectPosition.y + ballHeightInCanvas * 0.75f;

            gameObject.SetActive(able);
            golfBallSwingDrag.gameObject.SetActive(able);

            DragBallCamera.gameObject.SetActive(able);

            if (able)
            {
                if (!MasterManager.Instance.zoomedOutFailed && MasterManager.Instance.destinationVerticalPivot != 0)
                {
                    float imageRatio = 1.8f;  // in zoom out mode, camera move 2x times as far from ball so ball image should be x(?) times as big

                    dragBallImageStill.gameObject.SetActive(true);
                    Vector3 dragBallImageStillScale = dragBallImageStill.rectTransform.localScale;
                    float dragBallImageStillHeight = dragBallImageStill.rectTransform.rect.height;
                    dragBallImageStill.rectTransform.sizeDelta = new Vector2(ballWidthInCanvas * imageRatio, ballHeightInCanvas * imageRatio);
                    dragBallImageStill.rectTransform.localScale = Vector3.one;
                    dragBallImageStill.rectTransform.anchoredPosition = new Vector2(ballRectPosition.x, ballRectPosition.y);

                    golfBallSwingDrag.drag.rectTransform.sizeDelta = new Vector2(ballWidthInCanvas * imageRatio, ballHeightInCanvas * imageRatio);
                    golfBallSwingDrag.drag.rectTransform.localScale = Vector3.one;
                    golfBallSwingDrag.drag.rectTransform.anchoredPosition = new Vector2(ballRectPosition.x, ballRectPosition.y);

                    golfBallSwingDrag.ResetOriginalPosition(dragBallImageStill.transform.position);
                    upperBoundOfDragBallImageTransform = dragBallImageStill.gameObject.transform.position;

                    OnHashedSideAngleChanged();

                    GolfBallSwingDragLine.GetComponent<RectTransform>().anchoredPosition = new Vector2(0f, ballRectPosition.y - stillImageYToDragLineDifference * 0.99f); // 1f to be exact, 0.99 to move up a bit to hide under ball image

                    GolfBallSwingDragLine.gameObject.transform.localScale = Vector3.one;

                    angleIndicatorHeight = dragBallImageStill.rectTransform.anchoredPosition.y + dragBallImageStill.rectTransform.rect.height * 0.75f;

                    verticalPivot = true;
                }
                else
                {
                    dragBallImageStill.gameObject.SetActive(true);
                    Vector3 dragBallImageStillScale = dragBallImageStill.rectTransform.localScale;
                    float dragBallImageStillHeight = dragBallImageStill.rectTransform.rect.height;
                    dragBallImageStill.rectTransform.sizeDelta = new Vector2(ballWidthInCanvas, ballHeightInCanvas);
                    dragBallImageStill.rectTransform.localScale = Vector3.one;
                    dragBallImageStill.rectTransform.anchoredPosition = new Vector2(ballRectPosition.x, ballRectPosition.y);

                    golfBallSwingDrag.drag.rectTransform.sizeDelta = new Vector2(ballWidthInCanvas, ballHeightInCanvas);
                    golfBallSwingDrag.drag.rectTransform.localScale = Vector3.one;
                    golfBallSwingDrag.drag.rectTransform.anchoredPosition = new Vector2(ballRectPosition.x, ballRectPosition.y);

                    golfBallSwingDrag.ResetOriginalPosition(dragBallImageStill.transform.position);
                    upperBoundOfDragBallImageTransform = dragBallImageStill.gameObject.transform.position;

                    OnHashedSideAngleChanged();

                    GolfBallSwingDragLine.GetComponent<RectTransform>().anchoredPosition = new Vector2(0f, topBallPosRectPosition.y - stillImageYToDragLineDifference - (ballHeightInCanvas * 0.45f)); // 0.5 to be exact, 0.45 to move up a bit to hide under ball

                    if (ballHeightInCanvas < exactBallHeightInCanvasForExactDragLineScale)
                    {
                        GolfBallSwingDragLine.gameObject.transform.localScale = Vector3.one;
                    }
                    else
                    {
                        float adaptiveDragLineScale = 1f - (ballHeightInCanvas - exactBallHeightInCanvasForExactDragLineScale) * ballHeightToDragLineScaleStep;
                        GolfBallSwingDragLine.gameObject.transform.localScale = new Vector3(adaptiveDragLineScale, adaptiveDragLineScale, adaptiveDragLineScale);
                    }

                    verticalPivot = false;
                }
            }
            else
            {
                dragBallImageStill.gameObject.SetActive(false);
            }

            SetActiveForSwingButton(!able);
            SetActiveForTopViewButton(able);
            SetActiveForGadgetPickerButton(able);
            SetActiveForSpectateButton(able);
            SetActiveForAutoHitButton(able);
        }

        public void OnHashedSideAngleChanged()
        {
            // process hashed side angle
            float sideSpin = manager.GetHashedSideAngle();
            exactBoundOfDragBallImageTransformWithSideSpin = RotateDragLineVector3(
                dragBallImageStill.gameObject.transform.position,
                exactBoundOfDragBallImageTransform,
                sideSpin
            );
            golfBallSwingDrag.ResetExactPosition(exactBoundOfDragBallImageTransformWithSideSpin);
            SetPositionForExactPositionComponent(exactBoundOfDragBallImageTransformWithSideSpin);
            //
        }

        private Vector3 RotateDragLineVector3(Vector3 origin, Vector3 exact, float angleInDegrees)
        {
            Vector3 direction = exact - origin;

            Quaternion rotation = Quaternion.Euler(0, 0, -angleInDegrees);

            Vector3 rotated = rotation * direction;

            return origin + rotated;
        }

        private void AutoHitButtonOnClickCallback()
        {
            manager.SetSideAngleInput(0);
            manager.SwingBall(1f, false);
            _launchParticles.Play();
            gameObject.SetActive(false);
            SpectateButton.gameObject.SetActive(false);
        }
        #endregion

        public override void OnHide()
        {
            gameObject.SetActive(false);
        }

        public override void OnOpen()
        {
            gameObject.SetActive(true);
        }

        public override void OnGoingTop()
        {
            gameObject.SetActive(true);

            golfBallSwingDrag.gameObject.SetActive(false);
            dragBallImageStill.gameObject.SetActive(false);

            isTopView = true;

            SetActiveForSwingButton(true);
            SetActiveForTopViewButton(false);
            SetActiveForGadgetPickerButton(true);
            SetActiveForSpectateButton(false);
            SetActiveForAutoHitButton(false);
        }

        public void SetCanvas(Canvas uiCanvas)
        {
            UICanvas = uiCanvas;
        }

        public void StartGameSetup()
        {
            dragState = DragState.None; GolfBallSwingDragLine.SetState(dragState);
            isTopView = true;

            SetActiveForSwingButton(isTopView);
            SetActiveForTopViewButton(!isTopView);
            SetActiveForGadgetPickerButton(isTopView);
            SetActiveForAutoHitButton(!isTopView);

            dragBallImageStill.gameObject.SetActive(false);
            golfBallSwingDrag.gameObject.SetActive(false);

            controllerDragAreaOuterCircle.gameObject.SetActive(false);
            controllerDragAreaEffectExact.gameObject.SetActive(false);
            controllerDragAreaEffectOver.gameObject.SetActive(false);
        }

        public void ExternalForceStopDragging()
        {
            golfBallSwingDrag.ForceStopDragging();
        }

        #region Opponent states
        private bool isMovingOpponentDragBallImage = false;
        private Vector3 cachedOpponentDragBallImagePosition;
        private float cachedOpponentControllerAngle = 0f;
        private IEnumerator moveOpponentDragBallImage;

        private IEnumerator SmoothLerpDragBallImage(float dragPercentage, float angle)
        {
            isMovingOpponentDragBallImage = true;

            DragBallCamera.gameObject.SetActive(true);
            golfBallSwingDrag.gameObject.SetActive(true); 
            golfBallSwingDrag.Show(); golfBallSwingDrag.SetIsBeingExternallyForced(true);

            if (!MasterManager.Instance.zoomedOutFailed && MasterManager.Instance.destinationVerticalPivot != 0)
            {
                DragBallCamera.gameObject.SetActive(true);
                dragBallImageStill.gameObject.SetActive(true);
            }
            else
            {
                dragBallImageStill.gameObject.SetActive(false);
            }

            GolfBallSwingDragLine.SetState(DragState.Under);

            float time = 0.2f;

            Vector3 startingPos = cachedOpponentDragBallImagePosition;
            Vector3 finalPos = new Vector3(
                0f, 
                upperBoundOfDragBallImageTransform.y - (-exactBoundOfDragBallImageTransform.y + upperBoundOfDragBallImageTransform.y) * dragPercentage, 
                100f);

            finalPos = new Vector3(0f, upperBoundOfDragBallImageTransform.y, 100f) + GolfUtility.RotateVectorZAxisAndKeepVerticalValue(new Vector3(0f, upperBoundOfDragBallImageTransform.y, 100f), finalPos, angle);

            float startAngle = cachedOpponentControllerAngle;
            float endAngle = angle;

            cachedOpponentDragBallImagePosition = finalPos;
            cachedOpponentControllerAngle = endAngle;

            float elapsedTime = 0;

            while (elapsedTime < time)
            {
                Vector3 lerpPosition = Vector3.Lerp(startingPos, finalPos, (elapsedTime / time));

                golfBallSwingDrag.gameObject.transform.position = lerpPosition;
                cachedOpponentDragBallImagePosition = golfBallSwingDrag.gameObject.transform.position;

                float lerpedAngle = Mathf.Lerp(startAngle, endAngle, (elapsedTime / time));
                cachedOpponentControllerAngle = lerpedAngle;

                float currentDragPercentage;
                currentDragPercentage = ((- golfBallSwingDrag.gameObject.transform.position.y + upperBoundOfDragBallImageTransform.y) / (upperBoundOfDragBallImageTransform.y - exactBoundOfDragBallImageTransform.y));

                if (1 - dragPerfectDelta <= currentDragPercentage && currentDragPercentage <= 1 + dragPerfectDelta)
                {
                    GolfBallSwingDragLine.SetState(DragState.Exact);

                    controllerDragAreaEffectExact.gameObject.SetActive(true);
                    controllerDragAreaEffectOver.gameObject.SetActive(false);

                    controllerDragAreaOuterCircle.gameObject.SetActive(true);

                    controllerDragAreaOuterCircle.transform.localScale = outerCircleLocalScale * 1f;
                }
                else if (currentDragPercentage < 1 - dragPerfectDelta)
                {
                    GolfBallSwingDragLine.SetState(DragState.Under);

                    controllerDragAreaEffectExact.gameObject.SetActive(false);
                    controllerDragAreaEffectOver.gameObject.SetActive(false);

                    controllerDragAreaOuterCircle.gameObject.SetActive(true);
                    controllerDragAreaOuterCircle.transform.localScale = outerCircleLocalScale * (outerLimit - (outerLimit - 1f) * dragPercentage);
                }
                else
                {
                    GolfBallSwingDragLine.SetState(DragState.Over);

                    controllerDragAreaEffectExact.gameObject.SetActive(false);
                    controllerDragAreaEffectOver.gameObject.SetActive(true);

                    controllerDragAreaOuterCircle.gameObject.SetActive(false);
                }

                GolfBallSwingDragLine.Set(currentDragPercentage, lerpedAngle, lerpPosition);

                elapsedTime += Time.deltaTime;
                yield return null;
            }

            isMovingOpponentDragBallImage = false;
            cachedOpponentDragBallImagePosition = golfBallSwingDrag.gameObject.transform.position;
            cachedOpponentControllerAngle = endAngle;
        }

        public void ExecuteOpponentDragBall(float dragPercentage, float angle)
        {
            gameObject.SetActive(true);

            controllerSlider.SetActive(true);
            controllerNeedle.SetActive(true);
            controllerBallSideAngleIndicator.SetActive(false);
            exactArrow1.gameObject.SetActive(false);
            exactArrow2.gameObject.SetActive(false);
            controllerSliderComponent.SetActiveBlur(false); //exactGlow.gameObject.SetActive(false);

            ResetColorOpponentView();

            SetActiveForSwingButton(false);
            SetActiveForTopViewButton(false);
            SetActiveForGadgetPickerButton(false);
            SetActiveForAutoHitButton(false);

            if (!IsMovingOpponentSideAngleArrow)
            {
                MoveOpponentSideAngleArrowCoroutine = MoveOpponentSideAngleArrow();
                StartCoroutine(MoveOpponentSideAngleArrowCoroutine);

                IsMovingOpponentSideAngleArrow = true;
            }

            // region: drag ball image

            if (isMovingOpponentDragBallImage)
            {
                StopCoroutine(moveOpponentDragBallImage);
                cachedOpponentDragBallImagePosition = golfBallSwingDrag.gameObject.transform.position;
            }

            moveOpponentDragBallImage = SmoothLerpDragBallImage(dragPercentage, angle);
            StartCoroutine(moveOpponentDragBallImage);
        }

        private void ResetColorOpponentView()
        {
            //controllerSliderImageLeft.color = sliderImageColor;
            controllerSliderImageBlur1.color = controllerSliderImageBlurColor1;
            controllerSliderImageBlur2.color = controllerSliderImageBlurColor2;
            controllerNeedleImage.color = controllerNeedleImageColor;
            controllerNeedleImageBlur.color = controllerNeedleImageBlurColor;
            exactArrow1.color = exactArrow1Color;
            exactArrow2.color = exactArrow2Color;
            controllerSliderComponent.SetBlurColor(exactGlowColor); //exactGlow.color = exactGlowColor;
        }

        private IEnumerator waitTilNeedleFinish;
        private bool isWaitingTilNeedleFinish = false;
        private OpponentHitBallPrepAction cachedPreHitAction;

        public void WaitTilNeedleFinish(OpponentHitBallPrepAction action)
        {
            cachedPreHitAction = action;

            float opponentSideAngle = (float)(action.Input.SideAngle);

            float needleRotationValue = CalculateControllerNeedleRotation(opponentSideAngle);

            if (IsMovingOpponentSideAngleArrow)
            {
                waitTilNeedleFinish = WaitTilNeedleMatchOpponentShot(Mathf.Clamp(needleRotationValue, bottomBound, upperBound), action);
                StartCoroutine(waitTilNeedleFinish);
            }
            else
            {
                ActionDispatcher.Dispatch(new OpponentHitBallAction(action.Player, action.Input, action.Ball, false));
            }
        }

        private IEnumerator WaitTilNeedleMatchOpponentShot(float needleRotationValue, OpponentHitBallPrepAction action)
        {
            isWaitingTilNeedleFinish = true;
            yield return new WaitUntil(() => Mathf.Abs(currentOpponentNeedleRotation - needleRotationValue) < 0.5f);

            StopOpponentDragBall();
            ActionDispatcher.Dispatch(new OpponentHitBallAction(action.Player, action.Input, action.Ball, false));
            isWaitingTilNeedleFinish = false;
        }

        public void StopOpponentDragBall()
        {
            if (!IsMovingOpponentSideAngleArrow) return;

            if (isWaitingTilNeedleFinish)
            {
                StopCoroutine(waitTilNeedleFinish);
                ActionDispatcher.Dispatch(new OpponentHitBallAction(cachedPreHitAction.Player, cachedPreHitAction.Input, cachedPreHitAction.Ball, false));
                isWaitingTilNeedleFinish = false;
            }

            gameObject.SetActive(false);

            controllerSlider.SetActive(false);
            controllerNeedle.SetActive(false);
            controllerBallSideAngleIndicator.SetActive(false);

            controllerDragAreaOuterCircle.gameObject.SetActive(false);
            controllerDragAreaEffectExact.gameObject.SetActive(false);
            controllerDragAreaEffectOver.gameObject.SetActive(false);

            StopCoroutine(MoveOpponentSideAngleArrowCoroutine);
            
            IsMovingOpponentSideAngleArrow = false;

            // region: drag ball image

            if (isMovingOpponentDragBallImage)
            {
                StopCoroutine(moveOpponentDragBallImage);
            }

            isMovingOpponentDragBallImage = false;

            cachedOpponentDragBallImagePosition = golfBallSwingDrag.GetOriginalPos();

            DragBallCamera.gameObject.SetActive(false);

            golfBallSwingDrag.ResetPosition();
            golfBallSwingDrag.SetIsBeingExternallyForced(false);
            golfBallSwingDrag.Hide();
            golfBallSwingDrag.gameObject.SetActive(false);

            GolfBallSwingDragLine.SetState(DragState.None);
        }

        private IEnumerator MoveOpponentSideAngleArrowCoroutine;
        private bool IsMovingOpponentSideAngleArrow = false;
        float currentOpponentNeedleXValue = bottomBound;
        float currentOpponentNeedleRotation = 0f;

        private IEnumerator MoveOpponentSideAngleArrow()
        {
            currentOpponentNeedleXValue = bottomBound;
            bool up = true;
            while (true)
            {
                var deltaTime = Time.deltaTime;
                if (up)
                {
                    float yPos = CalculateYPositionOfNeedle(currentOpponentNeedleXValue * needleXMultiplier);
                    float rotation = CalculateRotationOfNeedle(currentOpponentNeedleXValue * needleXMultiplier);

                    Vector3 currentPosition = CalculateCurrentNeedlePosition(new Vector3(currentOpponentNeedleXValue * needleXMultiplier, yPos, 0));

                    controllerNeedle.transform.localPosition = currentPosition + new Vector3(controllerAngle * angleDerivedPositionMultiplier, 0, 0);
                    controllerNeedle.transform.localRotation = Quaternion.Euler(0, 0, rotation + controllerAngle);

                    currentOpponentNeedleRotation = CalculatePhysicsSideAngleValue(rotation);

                    // iterate x value
                    currentOpponentNeedleXValue += ((upperBound * 2f) * deltaTime) * FinalSpeed;
                    if (currentOpponentNeedleXValue > upperBound)
                    {
                        currentOpponentNeedleXValue = upperBound;
                        up = false;

                        //controllerNeedle.GetComponent<ControllerNeedle>().trailBase.Clear();
                        //controllerNeedle.GetComponent<ControllerNeedle>().trailBaseParticles.Clear();
                    }
                    // wait for fps amount of time
                    yield return new WaitForSeconds(deltaTime);
                    // done waiting, go to next loop

                    currentRawSideAngle = currentOpponentNeedleXValue;

                    continue;
                }
                else
                {
                    float yPos = CalculateYPositionOfNeedle(currentOpponentNeedleXValue * needleXMultiplier);
                    float rotation = CalculateRotationOfNeedle(currentOpponentNeedleXValue * needleXMultiplier);

                    Vector3 currentPosition = CalculateCurrentNeedlePosition(new Vector3(currentOpponentNeedleXValue * needleXMultiplier, yPos, 0));

                    controllerNeedle.transform.localPosition = currentPosition + new Vector3(controllerAngle * angleDerivedPositionMultiplier, 0, 0);
                    controllerNeedle.transform.localRotation = Quaternion.Euler(0, 0, rotation + controllerAngle);

                    currentOpponentNeedleRotation = CalculatePhysicsSideAngleValue(rotation);

                    // iterate x value
                    currentOpponentNeedleXValue -= ((upperBound * 2f) * deltaTime) * FinalSpeed;
                    if (currentOpponentNeedleXValue < bottomBound)
                    {
                        currentOpponentNeedleXValue = bottomBound;
                        up = true;

                        //controllerNeedle.GetComponent<ControllerNeedle>().trailBase.Clear();
                        //controllerNeedle.GetComponent<ControllerNeedle>().trailBaseParticles.Clear();
                    }
                    // wait for fps amount of time
                    yield return new WaitForSeconds(deltaTime);
                    // done waiting, go to next loop

                    currentRawSideAngle = currentOpponentNeedleXValue;

                    continue;
                }
            }
        }
        #endregion
    }
}
