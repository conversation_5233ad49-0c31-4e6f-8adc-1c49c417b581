using _Golf.Scripts.Common;
using _Golf.Scripts.ScriptableObjects;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace GolfGame
{
    public class ControllerSettingsDebugUI : MonoBehaviour, ModuleComponent
    {
        [Header("Controller Config")]
        [SerializeField] private TMP_Text MaxControllerTiltDegreeText;
        [SerializeField] private TMP_InputField MaxControllerTiltDegreeInputField;

        [SerializeField] private TMP_Text MaxDegForVerticalGuideText;
        [SerializeField] private TMP_InputField MaxDegForVerticalGuideInputField;

        [SerializeField] private TMP_Text VerticalForcedPercentageThresholdText;
        [SerializeField] private TMP_InputField VerticalForcedPercentageThresholdInputField;

        [SerializeField] private TMP_Text MinimumDragPuttingThresholdText;
        [SerializeField] private TMP_InputField MinimumDragPuttingThresholdInputField;

        [SerializeField] private <PERSON><PERSON> ApplyControllerConfigButton;

        [Header("Weather")]
        [SerializeField] private TMP_Dropdown turnOffWeatherDebuggingToggle;

        [Header("Accuracy Config")]
        [SerializeField] private TMP_Text AccuracyDeltaText;
        [SerializeField] private TMP_InputField AccuracyDeltaInputField;
        [SerializeField] private TMP_Text MinAccuracyText;
        [SerializeField] private TMP_InputField MinAccuracyInputField;
        [SerializeField] private Button ApplyAccuracyConfigButton;

        [Header("Rank Shot Data")]
        [SerializeField] private TMP_Dropdown rankShotDataDebuggingToggle;
        [SerializeField] private TMP_Text rankShotDataCurrentRank;

        [SerializeField] private TMP_Text PerfectShotSideAngleDeltaConfig;
        [SerializeField] private TMP_Text PerfectShotSideAngleDeltaText;
        [SerializeField] private TMP_InputField PerfectShotSideAngleDeltaInputField;

        [SerializeField] private TMP_Text GreatShotSideAngleDeltaConfig;
        [SerializeField] private TMP_Text GreatShotSideAngleDeltaText;
        [SerializeField] private TMP_InputField GreatShotSideAngleDeltaInputField;

        [SerializeField] private TMP_Text PerfectShotVelocityDeltaConfig;
        [SerializeField] private TMP_Text PerfectShotVelocityDeltaText;
        [SerializeField] private TMP_InputField PerfectShotVelocityDeltaInputField;

        [SerializeField] private TMP_Text GreatShotVelocityDeltaConfig;
        [SerializeField] private TMP_Text GreatShotVelocityDeltaText;
        [SerializeField] private TMP_InputField GreatShotVelocityDeltaInputField;

        [SerializeField] private TMP_Text SideAngleMultiplyingFactorConfig;
        [SerializeField] private TMP_Text SideAngleMultiplyingFactorText;
        [SerializeField] private TMP_InputField SideAngleMultiplyingFactorInputField;

        [SerializeField] private Button ApplyDebugRankShotConfigButton;

        [Header("Debug Overlay")]
        [SerializeField] private TMP_Dropdown OverlayDebuggingToggle;

        public void Init(ModuleMediator _mediator)
        {
            // Controller config
            MaxControllerTiltDegreeText.text =
                GlobalSO.RemoteConfigData.ControllerConfig.NormalShotConfig.MaximumControllerTiltDegree.ToString();
            MaxControllerTiltDegreeInputField.text =
                GlobalSO.RemoteConfigData.ControllerConfig.NormalShotConfig.MaximumControllerTiltDegree.ToString();

            MaxDegForVerticalGuideText.text =
                GlobalSO.RemoteConfigData.ControllerConfig.NormalShotConfig.MaxDegForVerticalGuide.ToString();
            MaxDegForVerticalGuideInputField.text =
                GlobalSO.RemoteConfigData.ControllerConfig.NormalShotConfig.MaxDegForVerticalGuide.ToString();

            VerticalForcedPercentageThresholdText.text =
                GlobalSO.RemoteConfigData.ControllerConfig.NormalShotConfig.VerticalForcedPercentageThreshold.ToString();
            VerticalForcedPercentageThresholdInputField.text =
                GlobalSO.RemoteConfigData.ControllerConfig.NormalShotConfig.VerticalForcedPercentageThreshold.ToString();

            MinimumDragPuttingThresholdText.text =
                GlobalSO.RemoteConfigData.ControllerConfig.NormalShotConfig.MinimumDragPuttingThreshold.ToString();
            MinimumDragPuttingThresholdInputField.text =
                GlobalSO.RemoteConfigData.ControllerConfig.NormalShotConfig.MinimumDragPuttingThreshold.ToString();

            InitControllerConfigButton();
            //

            // Weather
            turnOffWeatherDebuggingToggle.ClearOptions();
            turnOffWeatherDebuggingToggle.AddOptions(new List<string>() { "No", "Yes" });
            turnOffWeatherDebuggingToggle.onValueChanged.AddListener(TurnOffWeatherDebuggingToggleChanged);
            
            if (GlobalSO.LocalGameSetting.TurnOffWeather)
            {
                TurnOffWeatherDebuggingToggleChanged(1);
            }
            else
            {
                TurnOffWeatherDebuggingToggleChanged(0);
            }
            //

            // Accuracy Config
            AccuracyDeltaText.text =
                GlobalSO.RemoteConfigData.ControllerConfig.NormalShotConfig.AccuracyDelta.ToString();
            AccuracyDeltaInputField.text =
                GlobalSO.RemoteConfigData.ControllerConfig.NormalShotConfig.AccuracyDelta.ToString();
            MinAccuracyText.text =
                GlobalSO.RemoteConfigData.ControllerConfig.NormalShotConfig.MinAccuracy.ToString();
            MinAccuracyInputField.text =
                GlobalSO.RemoteConfigData.ControllerConfig.NormalShotConfig.MinAccuracy.ToString();

            InitAccuracyConfigButton();
            //

            // Rank Shot Data
            rankShotDataDebuggingToggle.ClearOptions();
            rankShotDataDebuggingToggle.AddOptions(new List<string>() { "No", "Yes" });
            rankShotDataDebuggingToggle.onValueChanged.AddListener(RankShotDataDebuggingToggleChanged);

            if (GlobalSO.LocalGameSetting.IsDebuggingRankShotData)
            {
                RankShotDataDebuggingToggleChanged(1);
            }
            else
            {
                RankShotDataDebuggingToggleChanged(0);
            }

            string rankId = GlobalSO.GameplayBus.localPlayer.GetPlayerRankInfo().rankId;

            rankShotDataCurrentRank.text = "Current rank: " + rankId;

            List<GolfShotRatingByRank> ratingConfig = GlobalSO.RemoteConfigData.GolfShotRating.GolfShotRatingByRanks;

            foreach (var golfShotRating in ratingConfig)
            {
                if (golfShotRating.RankId == rankId)
                {
                    PerfectShotSideAngleDeltaConfig.text =
                        golfShotRating.PerfectShotSideAngleDelta.ToString();

                    GreatShotSideAngleDeltaConfig.text =
                        golfShotRating.GreatShotSideAngleDelta.ToString();

                    PerfectShotVelocityDeltaConfig.text =
                        golfShotRating.PerfectShotVelocityDelta.ToString();

                    GreatShotVelocityDeltaConfig.text =
                        golfShotRating.GreatShotVelocityDelta.ToString();

                    SideAngleMultiplyingFactorConfig.text =
                        golfShotRating.SideAngleMultiplyingFactor.ToString();
                }
            }

            PerfectShotSideAngleDeltaText.text =
                GlobalSO.LocalGameSetting.PerfectShotSideAngleDelta.ToString();
            PerfectShotSideAngleDeltaInputField.text =
                 GlobalSO.LocalGameSetting.PerfectShotSideAngleDelta.ToString();

            GreatShotSideAngleDeltaText.text =
                 GlobalSO.LocalGameSetting.GreatShotSideAngleDelta.ToString();
            GreatShotSideAngleDeltaInputField.text =
                 GlobalSO.LocalGameSetting.GreatShotSideAngleDelta.ToString();

            PerfectShotVelocityDeltaText.text =
                 GlobalSO.LocalGameSetting.PerfectShotVelocityDelta.ToString();
            PerfectShotVelocityDeltaInputField.text =
                 GlobalSO.LocalGameSetting.PerfectShotVelocityDelta.ToString();

            GreatShotVelocityDeltaText.text =
                 GlobalSO.LocalGameSetting.GreatShotVelocityDelta.ToString();
            GreatShotVelocityDeltaInputField.text =
                 GlobalSO.LocalGameSetting.GreatShotVelocityDelta.ToString();

            SideAngleMultiplyingFactorText.text =
                GlobalSO.LocalGameSetting.SideAngleMultiplyingFactor.ToString();
            SideAngleMultiplyingFactorInputField.text =
                GlobalSO.LocalGameSetting.SideAngleMultiplyingFactor.ToString();

            InitRankShotDebugButton();
            //

            // Over lay debugging
            OverlayDebuggingToggle.ClearOptions();
            OverlayDebuggingToggle.AddOptions(new List<string>() { "No", "Yes" });
            OverlayDebuggingToggle.onValueChanged.AddListener(OverlayDebuggingToggleChanged);

            if (GlobalSO.LocalGameSetting.ControllerNormalDebugOverlay)
            {
                OverlayDebuggingToggleChanged(1);
            }
            else
            {
                OverlayDebuggingToggleChanged(0);
            }
            //
        }

        private void OverlayDebuggingToggleChanged(int idx)
        {
            GlobalSO.LocalGameSetting.ControllerNormalDebugOverlay = idx == 0 ? false : true;
        }

        private void TurnOffWeatherDebuggingToggleChanged(int idx)
        {
            GlobalSO.LocalGameSetting.TurnOffWeather = idx == 0 ? false : true;
        }

        private void RankShotDataDebuggingToggleChanged(int idx)
        {
            GlobalSO.LocalGameSetting.IsDebuggingRankShotData = idx == 0 ? false : true;
        }

        public void Dispose()
        {
            ApplyControllerConfigButton.onClick.RemoveAllListeners();
        }

        private void InitControllerConfigButton()
        {
            ApplyControllerConfigButton.onClick.AddListener(() =>
            {
                if (float.TryParse(MaxControllerTiltDegreeInputField.text, out float value))
                {
                    GlobalSO.RemoteConfigData.ControllerConfig.NormalShotConfig.MaximumControllerTiltDegree = value;
                    MaxControllerTiltDegreeText.text = value.ToString();
                    MaxControllerTiltDegreeInputField.text = value.ToString();
                }
                else
                {
                    Debug.LogError("VALUE NOT VALID");
                }
                if (float.TryParse(MaxDegForVerticalGuideInputField.text, out float value1))
                {
                    GlobalSO.RemoteConfigData.ControllerConfig.NormalShotConfig.MaxDegForVerticalGuide = value1;
                    MaxDegForVerticalGuideText.text = value1.ToString();
                    MaxDegForVerticalGuideInputField.text = value1.ToString();
                }
                else
                {
                    Debug.LogError("VALUE NOT VALID");
                }
                if (float.TryParse(VerticalForcedPercentageThresholdInputField.text, out float value2))
                {
                    GlobalSO.RemoteConfigData.ControllerConfig.NormalShotConfig.VerticalForcedPercentageThreshold = value2;
                    VerticalForcedPercentageThresholdText.text = value2.ToString();
                    VerticalForcedPercentageThresholdInputField.text = value2.ToString();
                }
                else
                {
                    Debug.LogError("VALUE NOT VALID");
                }
                if (float.TryParse(MinimumDragPuttingThresholdInputField.text, out float value3))
                {
                    GlobalSO.RemoteConfigData.ControllerConfig.NormalShotConfig.MinimumDragPuttingThreshold = value3;
                    MinimumDragPuttingThresholdText.text = value3.ToString();
                    MinimumDragPuttingThresholdInputField.text = value3.ToString();
                }
                else
                {
                    Debug.LogError("VALUE NOT VALID");
                }
            });
        }

        private void InitAccuracyConfigButton()
        {
            ApplyAccuracyConfigButton.onClick.AddListener(() =>
            {
                if (float.TryParse(AccuracyDeltaInputField.text, out float value))
                {
                    GlobalSO.RemoteConfigData.ControllerConfig.NormalShotConfig.AccuracyDelta = value;
                    AccuracyDeltaText.text = value.ToString();
                    AccuracyDeltaInputField.text = value.ToString();
                }
                else
                {
                    Debug.LogError("VALUE NOT VALID");
                }
                if (float.TryParse(MinAccuracyInputField.text, out float value1))
                {
                    GlobalSO.RemoteConfigData.ControllerConfig.NormalShotConfig.MinAccuracy = value1;
                    MinAccuracyText.text = value1.ToString();
                    MinAccuracyInputField.text = value1.ToString();
                }
                else
                {
                    Debug.LogError("VALUE NOT VALID");
                }
            });
        }

        private void InitRankShotDebugButton()
        {
            ApplyDebugRankShotConfigButton.onClick.AddListener(() =>
            {
                if (float.TryParse(PerfectShotSideAngleDeltaInputField.text, out float value))
                {
                    GlobalSO.LocalGameSetting.PerfectShotSideAngleDelta = value;
                    PerfectShotSideAngleDeltaText.text = value.ToString();
                    PerfectShotSideAngleDeltaInputField.text = value.ToString();
                }
                else
                {
                    Debug.LogError("VALUE NOT VALID");
                }
                if (float.TryParse(GreatShotSideAngleDeltaInputField.text, out float value1))
                {
                    GlobalSO.LocalGameSetting.GreatShotSideAngleDelta = value1;
                    GreatShotSideAngleDeltaText.text = value1.ToString();
                    GreatShotSideAngleDeltaInputField.text = value1.ToString();
                }
                else
                {
                    Debug.LogError("VALUE NOT VALID");
                }
                if (float.TryParse(PerfectShotVelocityDeltaInputField.text, out float value2))
                {
                    GlobalSO.LocalGameSetting.PerfectShotVelocityDelta = value2;
                    PerfectShotVelocityDeltaText.text = value2.ToString();
                    PerfectShotVelocityDeltaInputField.text = value2.ToString();
                }
                else
                {
                    Debug.LogError("VALUE NOT VALID");
                }
                if (float.TryParse(GreatShotVelocityDeltaInputField.text, out float value3))
                {
                    GlobalSO.LocalGameSetting.GreatShotVelocityDelta = value3;
                    GreatShotVelocityDeltaText.text = value3.ToString();
                    GreatShotVelocityDeltaInputField.text = value3.ToString();
                }
                else
                {
                    Debug.LogError("VALUE NOT VALID");
                }
                if (float.TryParse(SideAngleMultiplyingFactorInputField.text, out float value4))
                {
                    GlobalSO.LocalGameSetting.SideAngleMultiplyingFactor = value4;
                    SideAngleMultiplyingFactorText.text = value4.ToString();
                    SideAngleMultiplyingFactorInputField.text = value4.ToString();
                }
                else
                {
                    Debug.LogError("VALUE NOT VALID");
                }
            });
        }
    }
}
