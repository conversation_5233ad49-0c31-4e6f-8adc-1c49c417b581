using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using _Golf.Physics;
using _Golf.Physics.Data;
using _Golf.Scripts.Common.Helper;
using _Golf.Scripts.Core;
using _Golf.Scripts.Core.CourseController;
using _Golf.Scripts.Lobby;
using _Golf.Scripts.ScriptableObjects;
using _Golf.Scripts.ScriptableObjects.Item;
using _Golf.Scripts.UI;
using DG.Tweening;
using GolfGame;
using GolfPhysics;
using UnityEngine;
using UnityEngine.UI;

public class GolfBallSwingDragPuttingUI : GolfBallSwingDragUISample, ModuleComponent
{
    private const float NeedleRange = 0.2f;
    private const float OffsetDrawLine = 0.03f;
    private const float UpperBoundDragLine = 196f;
    private const float DownBoundDragLine = 508f;
    private const float ConvertDragLineAngle = 1.8f;
    private const float DefaultLineEffectSpeed = 0.75f;
    private const float MaxLineEffectSpeed = 5f;
    
    [SerializeField] private Button autoHit;
    [SerializeField] private Button SpectateButton;
    [SerializeField] private GolfBallSwingDragPutting golfBallSwingDrag;
    [SerializeField] private Button GadgetPickerButton;
    [SerializeField] private Button GadgetPickerBallButton;
    [SerializeField] private Button GadgetPickerGearButton;
    [SerializeField] private Image GadgetPickerButtonClubImage;
    [SerializeField] private Image GadgetPickerButtonBallImage;
    [SerializeField] private Image dragCircle;
    [SerializeField] private ParticleSystem _launchParticles;
    [SerializeField] private GolfBallSwingDragLine GolfBallSwingDragLine;

    private GameplayGolfBallSwingUIComponentMediator _mediator;
    private GolfBallSwingUIManager _manager;
    private GolfBallSwingUIProxy _proxy;
    private GameObject _hole;
    private HoleBehaviour _holeBehaviour;
    private GameObject _ball;
    private LineRenderer _predictLine;
    private Canvas _uiCanvas;
    private Camera _dragBallCamera;

    private GameObject _controllerContainer;

    private GameObject _controllerArrowPutting;
    private GameObject _controllerArrowCenterPutting;
    private GameObject _controllerPuttingNeedle;
    // private GameObject _dragLineOver;
    private ParticleSystem _matchEffect;
    private GadgetPickerUIComponent _gadgetPickerUIComponent;

    private float minDragThreshold = 0.6f;

    private float _dragAngle = 0f;
    private float _maxVelocity = 0f;
    private float _dragPercentage = 0f;
    private Vector3 _outerCircleLocalScale;
    private float _dragMultiplier = 0.5f;

    #region Voodoo drag line position math
    private float stillImageYToDragLineDifference = 643f;
    private float exactBallHeightInCanvasForExactDragLineScale = 180f;
    private float ballHeightToDragLineScaleStep = 0.02f / 20f;
    #endregion

    private void Awake()
    {
        golfBallSwingDrag.GetComponent<RawImage>().texture = ResourcesManager.Instance.DragBallTexture;
    }

    public void Dispose()
    {
        golfBallSwingDrag.dragStarted -= StartDragging;
        golfBallSwingDrag.dragEnded -= StopDragging;
        golfBallSwingDrag.dragUpdateLine -= DragUpdateLine;
        golfBallSwingDrag.swingBall -= SwingBall;
        
        GadgetPickerButton.onClick.RemoveAllListeners();
        // GadgetPickerBallButton.onClick.RemoveAllListeners();
        // GadgetPickerGearButton.onClick.RemoveAllListeners();
        autoHit.onClick.RemoveAllListeners();
    }


    private void DrawProjection(float sideAngle)
    {
        var ballPosition = _ball.transform.position;
        var holePosition = _hole.transform.position;
        
        var forwardDirection = (holePosition - ballPosition).normalized;
        var currentBallData = GlobalSO.PlayerGadget.ChosenBall.serverData.customData;
        var currentGearData = GlobalSO.PlayerGadget.ChosenChip == null ? null : GlobalSO.PlayerGadget.ChosenChip.serverData.customData;

        var input = new TrajectoryInput()
        {
            Origin = ballPosition.ToDouble3(),
            ForwardDirection = forwardDirection.ToDouble3(),
            BallSpeed = (double)_maxVelocity * _dragPercentage,
            LaunchAngle = Constant.PuttingLaunchAngle,
            BackSpin = 0,
            SideSpin = 0,
            SideAngle = sideAngle,
        };
        var realTrajectory = TrajectoryGenerator.Generate(
            input,
            out var previewTrajectory,
            ballData: currentBallData,
            gearData: currentGearData,
            holePosition: holePosition
        );

        if (_predictLine == null) return;
        _predictLine.enabled = true;
        
        var holeToBall = Vector3.Distance(holePosition, ballPosition);

        var starDrawIndex = 0;
        
        for (var i = 0; i < previewTrajectory.Count; i++)
        {
            var distance = Vector3.Distance(ballPosition, previewTrajectory[i].Position.ToVector3());
            if (distance > NeedleRange && (i +1) < previewTrajectory.Count)
            {
                var controllerArrowPuttingTransform = _controllerArrowPutting.transform;
                var previewPoint = previewTrajectory[i].Position.ToVector3();
                controllerArrowPuttingTransform.position = (ballPosition + (previewPoint - ballPosition).normalized * NeedleRange).SetY(previewPoint.y);
                var nextStep = previewTrajectory[i+1].Position.ToVector3();
                controllerArrowPuttingTransform.LookAt(nextStep);
                controllerArrowPuttingTransform.eulerAngles = controllerArrowPuttingTransform.eulerAngles.OffsetX(90);
                starDrawIndex = i;
                break;
            }
        }

        _predictLine.positionCount = previewTrajectory.Count;
        _predictLine.SetPositions(previewTrajectory.Positions.ToArray());
        
        int segmentIndex;
        Vector3 hitPoint;

        var offsetDrawLineVector = OffsetDrawLine * (_ball.transform.position - _controllerArrowPutting.transform.position).normalized;

        if (IsPointOnLineRenderer(_predictLine, _controllerArrowPutting.transform.position + offsetDrawLineVector, out segmentIndex, out hitPoint, 0.015f))
        {
            TrimLineRenderer(_predictLine, segmentIndex, hitPoint);
        }
        
        var showEffect = realTrajectory.BallHoleStatus == BallHoleStatus.FallInHole;
        var targetColor = realTrajectory.BallHoleStatus switch
        {
            BallHoleStatus.PassHole => Constant.OverPutLineColor,
            BallHoleStatus.FallInHole => Constant.DefaultPutLineColor,
            _ => Constant.WeakLineColor
        };

        _holeBehaviour.PlayHitHolePuttingEffect(showEffect, IsBallFarFromHole());
        _predictLine.material.DOKill();
        _predictLine.material.DOColor(targetColor, "_Color", 0.1f);
    }

    private bool IsPointOnLineRenderer(LineRenderer lr, Vector3 point, out int segmentIndex, out Vector3 hitPoint, float tolerance = 0.1f)
    {
        segmentIndex = -1;
        hitPoint = Vector3.zero;

        for (int i = 0; i < lr.positionCount - 1; i++)
        {
            Vector3 a = lr.GetPosition(i);
            Vector3 b = lr.GetPosition(i + 1);

            Vector3 ab = b - a;
            Vector3 ap = point - a;

            float abLength = ab.magnitude;
            float projLength = Vector3.Dot(ap, ab.normalized);

            if (projLength >= 0 && projLength <= abLength)
            {
                Vector3 projected = a + ab.normalized * projLength;
                float distance = Vector3.Distance(projected, point);
                if (distance <= tolerance)
                {
                    segmentIndex = i;
                    hitPoint = projected;
                    return true;
                }
            }
        }

        return false;
    }

    private void TrimLineRenderer(LineRenderer lr, int segmentIndex, Vector3 hitPoint)
    {
        List<Vector3> newPositions = new List<Vector3>();

        newPositions.Add(hitPoint);

        for (int i = segmentIndex + 1; i < lr.positionCount; i++)
        {
            newPositions.Add(lr.GetPosition(i));
        }

        lr.positionCount = newPositions.Count;
        lr.SetPositions(newPositions.ToArray());
    }
    

    public bool IsBallFarFromHole()
    {
        return Vector3.Distance(_hole.gameObject.transform.position, _ball.transform.position) > 2.5f;
    }
    
    public void Init(ModuleMediator _mediator)
    {
        this._mediator = (GameplayGolfBallSwingUIComponentMediator)_mediator;
        _manager = (GolfBallSwingUIManager)_mediator.GetManager();
        _proxy = (GolfBallSwingUIProxy)_mediator.GetProxy();

        GolfBallSwingDragLine.Init();
        GolfBallSwingDragLine.SetState(DragState.None);

        golfBallSwingDrag.Init();
        golfBallSwingDrag.SetCanvas(_uiCanvas);
        golfBallSwingDrag.dragStarted += StartDragging;
        golfBallSwingDrag.dragEnded += StopDragging;

        golfBallSwingDrag.dragUpdateLine += DragUpdateLine;

        golfBallSwingDrag.swingBall += SwingBall;

        _controllerContainer = ResourcesManager.Instance.GetControllerContainer();

        _controllerArrowPutting = ResourcesManager.Instance.GetControllerArrowPutting();
        _arrowRenderer = _controllerArrowPutting.transform.GetChild(0);

        _controllerPuttingNeedle = ResourcesManager.Instance.GetPuttingNeedle();
        _controllerArrowCenterPutting = ResourcesManager.Instance.GetControllerArrowPuttingCenter();
        _matchEffect = ResourcesManager.Instance.GetMatchEffect();

        GadgetPickerButton.onClick.AddListener(() => OpenGadgetPickerPanel(0));
        // GadgetPickerBallButton.onClick.AddListener(() => OpenGadgetPickerPanel(1));
        // GadgetPickerGearButton.onClick.AddListener(() => OpenGadgetPickerPanel(2));

        autoHit.onClick.AddListener(AutoHitButton);

        _dragBallCamera = CameraManager.Instance.GetDragBallCamera();

        _ball = this._mediator.GetBall().gameObject;
        _hole = this._mediator.GetHole().gameObject; _holeBehaviour = _hole.GetComponent<HoleBehaviour>();
        _predictLine = this._mediator.GetPredicLine();

        cachedOpponentDragBallImagePosition = golfBallSwingDrag.GetOriginalPos();
    }

    public void StartGameSetUp()
    {
        golfBallSwingDrag.gameObject.SetActive(false);

        var puttingConfig = GlobalSO.RemoteConfigData.ControllerConfig.PuttingShotConfig;

        _dragMultiplier = puttingConfig?.DragMultiplier ?? 0.5f;
        _sideAngleHelp = puttingConfig?.SideAngleHelp ?? 0f;
        _uiToSignAngle = puttingConfig?.ConvertUiToBallFightModel ?? 1f;

        GolfBallSwingDragLine.SetState(DragState.None);
    }

    private void SetSwingSignAngle(float angle)
    {
        var drawAngle = -_dragAngle / 2;

        var sideAngleHelp = _sideAngleHelp;

#if UNITY_EDITOR
        sideAngleHelp = 5f;
#endif
        
        _manager.SetSideAngleInput(Math.Abs(angle - drawAngle) < sideAngleHelp ? drawAngle : angle);
    }

    private float _sideAngleHelp = 2f;
    private const float RangeMatch = 3.5f;
    private float _uiToSignAngle = 1.4123f;
    private const float UpperBound = 0.75f;
    private const float BottomBound = -0.75f;
    
    private IEnumerator _moveSignAngleArrow;
    private Transform _arrowRenderer;
    private IEnumerator MoveSignAngleArrow()
    {
        var x = BottomBound;
        var up = true;

        while (true)
        {
            var rotation = CurveFunctionRotation(x);

            _controllerPuttingNeedle.transform.eulerAngles = _controllerArrowPutting.transform.eulerAngles.OffsetZ(-rotation);
            
            Vector3 centerPosition = _ball.transform.position;
            
            var radius =  Vector3.Distance(_ball.transform.position, _arrowRenderer.position);
            
            const float zeroAngle = 0f;
            
            const float zeroAngleInRadians = Mathf.Deg2Rad * zeroAngle;
            
            var newPos = new Vector3(
                centerPosition.x - radius * Mathf.Cos(zeroAngleInRadians),
                centerPosition.y,
                centerPosition.z - radius * Mathf.Sin(zeroAngleInRadians)
            );
            
            var fwdDirection = (centerPosition - _arrowRenderer.position);

            var zeroDirection = (centerPosition - newPos);

            var differenceAngle = Vector3.SignedAngle(fwdDirection, zeroDirection, Vector3.up);

            var realSideAngle = differenceAngle - rotation;

            var realAngleRadians = Mathf.Deg2Rad * realSideAngle;
            
            _controllerPuttingNeedle.transform.position = new Vector3(
                centerPosition.x - radius * Mathf.Cos(realAngleRadians),
                _arrowRenderer.position.y,
                centerPosition.z - radius * Mathf.Sin(realAngleRadians)
            );

            // Match Effect

            var needleDirection = (centerPosition - _controllerPuttingNeedle.transform.position);

            var range = Vector3.SignedAngle(fwdDirection, needleDirection, Vector3.up);
            
            SetSwingSignAngle(range * _uiToSignAngle - _dragAngle /2f);

            _controllerArrowCenterPutting.transform.position = _controllerArrowPutting.transform.position;

            // _controllerArrowCenterPutting.transform.eulerAngles = _controllerArrowPutting.transform.eulerAngles;
            _controllerArrowCenterPutting.transform.rotation = _controllerArrowPutting.transform.rotation;
            
            if (Math.Abs(range) < RangeMatch)
            {
                _controllerArrowPutting.SetActive(false);
                _controllerArrowCenterPutting.SetActive(true);
                if (!_matchEffect.isPlaying)
                    _matchEffect.Play();
            }
            else
            {
                _controllerArrowPutting.SetActive(true);
                _controllerArrowCenterPutting.SetActive(false);
            }
            
            // Revert Needle Side
            if (up)
            {
                x += (UpperBound / Constant.Fps) * (_dragMultiplier + 1f * 1f);
                if (x > UpperBound)
                {
                    x = UpperBound;
                    up = false;
                }
            }
            else
            {
                x -= (UpperBound / Constant.Fps) * (_dragMultiplier + 1f * 1f);
                if (x < BottomBound)
                {
                    x = BottomBound;
                    up = true;
                }
            }

            yield return new WaitForSeconds((float)Constant.DeltaTime);
        }
    }

    private void DragUpdateLine(float currentNewStepX, float currentOffset, float currentAngle, float currentScaleUp, 
        Vector3 imagePosition, float y, float angle)
    {
        float verticalValue = currentOffset * -1f;
        _dragPercentage = ((verticalValue - UpperBoundDragLine) / (DownBoundDragLine - UpperBoundDragLine));

        if (GlobalSO.RemoteConfigData.ControllerConfig.NormalShotConfig.MinimumDragPuttingThreshold != 0)
        {
            minDragThreshold = GlobalSO.RemoteConfigData.ControllerConfig.NormalShotConfig.MinimumDragPuttingThreshold;
        }

        if (_dragPercentage < minDragThreshold)
        {
            _dragPercentage = 0f;
        }
        else
        {
            _dragPercentage = (_dragPercentage - minDragThreshold) / (1f - minDragThreshold);
        }

        var dragLineAngle = CalculateCurrentLineAngle(currentAngle);

        var scroll = _predictLine.gameObject.GetComponent<ScrollVertical>();

        if (_dragPercentage > 1f)
        {
            var speed = Mathf.Clamp(DefaultLineEffectSpeed + (_dragPercentage - 1f) * 10f, DefaultLineEffectSpeed, MaxLineEffectSpeed);
            scroll.SetSpeed(speed);
        }
        else
        {
            scroll.SetSpeed(DefaultLineEffectSpeed);
        }

        _dragAngle = currentAngle;

        DrawProjection(- currentAngle / 2);

        // 

        float trueDragPercentage = ((-y - UpperBoundDragLine) / (593f - UpperBoundDragLine));

        GolfBallSwingDragLine.Set(trueDragPercentage, angle, imagePosition);

        SyncInterval(PlayerCommandSignature.PuttBallCommand, new object[] { imagePosition });
    }

    private float CalculateCurrentLineAngle(float currentAngle)
    {
        var dragLineAngle = currentAngle * ConvertDragLineAngle;
        return dragLineAngle;
    }

    private void SwingBall(float value)
    {
        _manager.SwingBall(_dragPercentage, true);
        _launchParticles.Play();
        gameObject.SetActive(false);
        _holeBehaviour.PlayHitHolePuttingEffect(false);
        SetSpectateButton(false);
    }

    public void CalculatePositionOfDragLine()
    {
        Ball currentBall = _mediator.GetCurrentInSpectateBall();

        Vector3 ballScreenPoint = CameraManager.Instance.GetMainCamera().WorldToScreenPoint(currentBall.gameObject.transform.position);

        Vector2 ballRectPosition;
        RectTransformUtility.ScreenPointToLocalPointInRectangle(
            (RectTransform)_uiCanvas.transform,
            ballScreenPoint,
            _uiCanvas.worldCamera,
            out ballRectPosition
        );

        Vector3 ballPosition = _uiCanvas.transform.TransformPoint(ballRectPosition);

        #region experiment code
        float dragCamFOV = _dragBallCamera.fieldOfView;
        Vector3 dragBallCamToBallPos = -_dragBallCamera.transform.position + currentBall.gameObject.transform.position;

        Vector3 dragBallCamToTopBallPos = Vector3.RotateTowards(dragBallCamToBallPos, Vector3.up, (float)Utility.DegreeToRadian(dragCamFOV / 2f), 0.1f);
        Vector3 dragBallCamToBotBallPos = Vector3.RotateTowards(dragBallCamToBallPos, Vector3.down, (float)Utility.DegreeToRadian(dragCamFOV / 2f), 0.1f);

        float length = dragBallCamToBallPos.magnitude * Mathf.Cos((float)Utility.DegreeToRadian(dragCamFOV / 2f));

        dragBallCamToTopBallPos = dragBallCamToTopBallPos.normalized * length;
        dragBallCamToBotBallPos = dragBallCamToBotBallPos.normalized * length;

        Vector3 topBallPos = _dragBallCamera.transform.position + dragBallCamToTopBallPos;
        Vector3 botBallPos = _dragBallCamera.transform.position + dragBallCamToBotBallPos;

        Vector3 topBallPosScreenPoint = CameraManager.Instance.GetMainCamera().WorldToScreenPoint(topBallPos);
        Vector3 botBallPosScreenPoint = CameraManager.Instance.GetMainCamera().WorldToScreenPoint(botBallPos);

        Vector2 topBallPosRectPosition;
        RectTransformUtility.ScreenPointToLocalPointInRectangle(
            (RectTransform)_uiCanvas.transform,
            topBallPosScreenPoint,
            _uiCanvas.worldCamera,
            out topBallPosRectPosition
            );

        Vector2 botBallPosRectPosition;
        RectTransformUtility.ScreenPointToLocalPointInRectangle(
            (RectTransform)_uiCanvas.transform,
            botBallPosScreenPoint,
            _uiCanvas.worldCamera,
            out botBallPosRectPosition
            );
        #endregion

        float ballHeightInCanvas = Mathf.Abs(topBallPosRectPosition.y - botBallPosRectPosition.y);
        GolfBallSwingDragLine.GetComponent<RectTransform>().anchoredPosition = new Vector2(0f, topBallPosRectPosition.y - stillImageYToDragLineDifference - (ballHeightInCanvas / 2f));

        if (ballHeightInCanvas < exactBallHeightInCanvasForExactDragLineScale)
        {
            GolfBallSwingDragLine.gameObject.transform.localScale = Vector3.one;
        }
        else
        {
            float adaptiveDragLineScale = 1f - (ballHeightInCanvas - exactBallHeightInCanvasForExactDragLineScale) * ballHeightToDragLineScaleStep;
            GolfBallSwingDragLine.gameObject.transform.localScale = new Vector3(adaptiveDragLineScale, adaptiveDragLineScale, adaptiveDragLineScale);
        }
    }

    private void StartDragging()
    {
        MasterManager.Instance.SetRenderInterval(1);
        CalculatePositionOfDragLine();
        _dragBallCamera.gameObject.SetActive(true);

        // Enalge Controller Effect, Hide Gadget
        _controllerContainer.SetActive(true);
        SetActiveForGadgetPickerButton(false);

        // Hide Normal Controller

        _controllerArrowPutting.SetActive(true);
        _controllerPuttingNeedle.SetActive(true);

        // Drag Effect
        dragCircle.gameObject.SetActive(true);

        // Predict Line
        _predictLine.gameObject.SetActive(true);
        var maxDistance = GolfUtility.GetMaximumDistance(GlobalSO.PlayerGadget, _mediator.GetBall(), _mediator.GetHole().transform.position);
        _maxVelocity = AimHelper.FindPuttingMaxVelocityInIdealCondition(maxDistance);
        _manager.SetPuttingVelocity(_maxVelocity);
        DrawProjection(0);

        // CorrouTine
        _moveSignAngleArrow = MoveSignAngleArrow();
        StartCoroutine(_moveSignAngleArrow);

        GolfBallSwingDragLine.SetState(DragState.Exact);
    }

    public void StopDragging()
    {
        _dragBallCamera.gameObject.SetActive(false);

        SetActiveForGadgetPickerButton(true);

        _predictLine.gameObject.SetActive(false);

        dragCircle.gameObject.SetActive(false);

        _controllerArrowPutting.SetActive(false);
        _controllerPuttingNeedle.SetActive(false);
        _controllerArrowCenterPutting.SetActive(false);

        StopCoroutine(_moveSignAngleArrow);

        _hole.GetComponent<HoleBehaviour>().PlayHitHolePuttingEffect(false);

        GolfBallSwingDragLine.SetState(DragState.None);
        MasterManager.Instance.SetRenderInterval(2);
    }

    public void ExternalForceStopDragging()
    {
        golfBallSwingDrag.ForceStopDragging();
    }

    private float CurveFunction(float x)
    {
        return (-0.16f * Mathf.Pow(x, 2)) + 3f;
    }

    private float CurveFunctionRotation(float x)
    {
        return -36f * x;
    }
    private float CurveballSideAngleIndicatorRotation(float x)
    {
        return -45f * x + 90f;
    }

    public void OpenGadgetPickerPanel(int index)
    {
        object[] data = new object[3];

        data[0] = this;
        data[1] = 3;

        Ball ball = _mediator.GetBall(); GameObject hole = _mediator.GetHole();

        ClubType clubType = AimHelper.FindClubTypeUsingBallPosition(ball.transform.position, hole.transform.position);

        data[2] = clubType;

        _ = MasterManager.Instance.OpenUIComponent(UIComponentEnum.GadgetPickerComponentUI, data);
    }
    
    public void SetGadgetPicker(GadgetPickerUIComponent _gadgetPickerUIComponent)
    {
        this._gadgetPickerUIComponent = _gadgetPickerUIComponent;
        _manager.SetGadgetPicker(this._gadgetPickerUIComponent);
    }
    
    public void GadgetPickerPanelClose()
    {
        
    }
    
    private void AutoHitButton()
    {
        _manager.SetSideAngleInput(0);
        _manager.SwingBall(1f, true);
        _launchParticles.Play();
        OnHide();
        SetSpectateButton(false);
    }

    public void SetActiveDrag(bool able)
    {
        gameObject.SetActive(able);
        golfBallSwingDrag.gameObject.SetActive(able);
        autoHit.gameObject.SetActive(able);
        SetActiveForGadgetPickerButton(able);
    }

    public void SetActiveForGadgetPickerButton(bool isActive)
    {
        GadgetPickerButton.gameObject.SetActive(isActive);
        // GadgetPickerBallButton.gameObject.SetActive(isActive);
        // GadgetPickerGearButton.gameObject.SetActive(isActive);

        if (isActive)
        {
            SetDataForGadgetPickerButton();
        }
    }

    public void SetDataForGadgetPickerButton()
    {
        Ball ball = _mediator.GetBall(); GameObject hole = _mediator.GetHole();
        var mainClub = GlobalSO.PlayerGadget.GetClub(AimHelper.FindClubTypeUsingBallPosition(ball.transform.position, hole.transform.position), isMain: true);
        GadgetPickerButtonClubImage.sprite = ResourcesManager.Instance.GetClubSprite(mainClub.ItemId);
        GadgetPickerButtonBallImage.sprite = ResourcesManager.Instance.GetItemSprite(GlobalSO.PlayerGadget.ChosenBall.ItemId);
    }

    internal void SetCanvas(Canvas uiCanvas)
    {
        _uiCanvas = uiCanvas;
    }

    public override void OnHide()
    {
        this.gameObject.SetActive(false);
        _dragBallCamera.gameObject.SetActive(false);
        _controllerArrowPutting.SetActive(false);
        _controllerPuttingNeedle.SetActive(false);
        _controllerArrowCenterPutting.SetActive(false);
    }

    public override void OnOpen()
    {
        this.gameObject.SetActive(true);
    }

    public override void OnGoingTop()
    {
        SetSpectateButton(true);
    }

    public void SetSpectateButton(bool active)
    {
        if (active)
        {
            if (GlobalSO.GameplayBus.currentLogic is MultiplayerNormalLogic)
            {
                SpectateButton.gameObject.SetActive(true);
            }
            else
            {
                SpectateButton.gameObject.SetActive(false);
            }
        }
        else
        {
            SpectateButton.gameObject.SetActive(false);
        }
    }

    #region Opponent states
    private bool isMovingOpponentDragBallImage = false;
    private Vector3 cachedOpponentDragBallImagePosition;
    private IEnumerator moveOpponentDragBallImage;

    private IEnumerator SmoothLerpDragBallImage(Vector3 ballImagePosition)
    {
        isMovingOpponentDragBallImage = true;

        _dragBallCamera.gameObject.SetActive(true);
        golfBallSwingDrag.gameObject.SetActive(true);
        golfBallSwingDrag.Show(); golfBallSwingDrag.SetIsBeingExternallyForced(true);

        GolfBallSwingDragLine.SetState(DragState.Exact);

        float time = 0.2f;

        Vector3 startingPos = cachedOpponentDragBallImagePosition == Vector3.zero ? golfBallSwingDrag.GetOriginalPos() : cachedOpponentDragBallImagePosition;
        Vector3 finalPos = ballImagePosition;

        cachedOpponentDragBallImagePosition = finalPos;

        golfBallSwingDrag.gameObject.transform.position = startingPos;

        float elapsedTime = 0;

        while (elapsedTime < time)
        {
            Vector3 lerpPosition = Vector3.Lerp(startingPos, finalPos, (elapsedTime / time));
            golfBallSwingDrag.gameObject.transform.position = lerpPosition;
            cachedOpponentDragBallImagePosition = golfBallSwingDrag.gameObject.transform.position;

            float verticalValue = golfBallSwingDrag.GetY() * -1f;
            _dragPercentage = ((verticalValue - UpperBoundDragLine) / (593f - UpperBoundDragLine));

            GolfBallSwingDragLine.Set(_dragPercentage, golfBallSwingDrag.CalculateTrueAngle(), lerpPosition);

            elapsedTime += Time.deltaTime;
            yield return null;
        }

        isMovingOpponentDragBallImage = false;
        cachedOpponentDragBallImagePosition = golfBallSwingDrag.gameObject.transform.position;
    }

    public void ExecuteOpponentPuttBall(Vector3 ballImagePosition)
    {
        gameObject.SetActive(true);
        autoHit.gameObject.SetActive(false);
        SetActiveForGadgetPickerButton(false);

        // region: drag ball image

        if (isMovingOpponentDragBallImage)
        {
            StopCoroutine(moveOpponentDragBallImage);
            cachedOpponentDragBallImagePosition = golfBallSwingDrag.gameObject.transform.position;
        }

        moveOpponentDragBallImage = SmoothLerpDragBallImage(ballImagePosition);
        StartCoroutine(moveOpponentDragBallImage);
    }

    public void StopOpponentDragBall()
    {
        gameObject.SetActive(false);

        // region: drag ball image

        if (isMovingOpponentDragBallImage)
        {
            StopCoroutine(moveOpponentDragBallImage);
        }

        cachedOpponentDragBallImagePosition = golfBallSwingDrag.GetOriginalPos();
        golfBallSwingDrag.ResetPosition(); golfBallSwingDrag.SetIsBeingExternallyForced(false);

        isMovingOpponentDragBallImage = false;

        _dragBallCamera.gameObject.SetActive(false);

        golfBallSwingDrag.Hide();
        golfBallSwingDrag.gameObject.SetActive(false);

        GolfBallSwingDragLine.SetState(DragState.None);
    }
    #endregion
}