using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using TMPro;
using UnityEngine.UI;
using System;
using _Golf.Scripts.Core;
using _Golf.Scripts.PlayField;
using _Golf.Scripts.ScriptableObjects;
using GolfGame;
using GolfPhysics;

namespace _Golf.Scripts.UI
{
    public class PlayersInfoUIComponent : MonoBehaviour
    {
        [SerializeField] private GameObject windInfo;
        [SerializeField] private GameObject ballInfo;
        [SerializeField] private GameObject gearInfo;

        [SerializeField] private TMP_Text ExtraDistanceValue;
        [SerializeField] private TMP_Text WindResistanceValue;
        [SerializeField] private TMP_Text BouncingValue;
        [SerializeField] private TMP_Text SideSpinValue;

        [SerializeField] private Image WindIcon;
        [SerializeField] private TMP_Text WindValue;

        [SerializeField] private Image GearIcon;

        [SerializeField] private TMP_Text RewardAmount;

        [SerializeField] private Image AvatarCountDown;
        [SerializeField] private TMP_Text AvatarCountDownTxt;

        [SerializeField] private List<Image> localPlayerStrokeFillers;
        [SerializeField] private List<Image> localPlayerStrokeBall;
        [SerializeField] private TMP_Text localRedundantStrokeCountTxt;

        [SerializeField] private TMP_Text localPlayerNameTxt;
        [SerializeField] private TMP_Text localPlayerEloTxt;

        [SerializeField] private List<Image> otherPlayerStrokeFillers;
        [SerializeField] private List<Image> otherPlayerStrokeBall;
        [SerializeField] private TMP_Text otherRedundantStrokeCountTxt;

        [SerializeField] private TMP_Text otherPlayerNameTxt;
        [SerializeField] private TMP_Text otherPlayerEloTxt;

        [SerializeField] private GameObject otherPlayerInfo;
        [SerializeField] private GameObject otherPlayerStroke;
        [SerializeField] private GameObject rewardInfo;
        [SerializeField] private Button _settingsButton;

        [SerializeField] private List<Button> _tooltips;
        [SerializeField] private List<Image> _tooltipImages;
        [SerializeField] private Button _closeTooltipButton;

        [SerializeField] private Image _playerAvt;
        [SerializeField] private Image _otherPlayerAvt;

        private bool _isTieBreak;
        private int _cacheLocalStroke;
        private int _cacheOtherStroke;

        public void OnTieBreak(bool value)
        {
            _isTieBreak = value;
        }

        public void SetOtherPlayerStroke(int strokeCount)
        {
            if (_isTieBreak)
            {
                for (int i = 0; i < otherPlayerStrokeFillers.Count; i++)
                {
                    otherPlayerStrokeFillers[i].gameObject.SetActive(false);
                    otherPlayerStrokeBall[i].gameObject.SetActive(false);
                }

                otherRedundantStrokeCountTxt.gameObject.SetActive(false);

                if (strokeCount > _cacheOtherStroke)
                {
                    otherPlayerStrokeBall[0].gameObject.SetActive(true);
                }
                return;
            }

            var par = GlobalSO.PlayFieldSO.HoleInfo.Par;

            if (strokeCount <= otherPlayerStrokeFillers.Count)
            {
                for (int i = 0; i < otherPlayerStrokeFillers.Count; i++)
                {
                    otherRedundantStrokeCountTxt.gameObject.SetActive(false);

                    if (strokeCount > i)
                    {
                        if (i < par)
                        {
                            otherPlayerStrokeFillers[i].gameObject.SetActive(true);
                            otherPlayerStrokeFillers[i].color = Constant.HighlightTextColor;
                            otherPlayerStrokeFillers[i].fillAmount = 1f;
                        }
                        else
                        {
                            otherPlayerStrokeFillers[i].gameObject.SetActive(false);
                        }

                        otherPlayerStrokeBall[i].gameObject.SetActive(true);
                    }
                    else
                    {
                        if (i < par)
                        {
                            otherPlayerStrokeFillers[i].gameObject.SetActive(true);
                            otherPlayerStrokeFillers[i].color = Constant.DisableTextColor;
                            otherPlayerStrokeFillers[i].fillAmount = 1f;

                            otherPlayerStrokeBall[i].gameObject.SetActive(false);
                            continue;
                        }

                        otherPlayerStrokeFillers[i].gameObject.SetActive(false);
                        otherPlayerStrokeBall[i].gameObject.SetActive(false);
                    }
                }
            }
            else
            {
                otherPlayerStrokeFillers[0].gameObject.SetActive(false);
                otherPlayerStrokeBall[0].gameObject.SetActive(true);

                for (int i = 1; i < otherPlayerStrokeFillers.Count; i++)
                {
                    otherPlayerStrokeFillers[i].gameObject.SetActive(false);
                    otherPlayerStrokeBall[i].gameObject.SetActive(false);
                }

                otherRedundantStrokeCountTxt.gameObject.SetActive(true);
                otherRedundantStrokeCountTxt.text = strokeCount.ToString() + "+";
            }

            _cacheOtherStroke = strokeCount;
        }

        public void DisplayOtherContent(bool value)
        {
            otherPlayerInfo.gameObject.SetActive(value);
            otherPlayerStroke.gameObject.SetActive(value);
            rewardInfo.gameObject.SetActive(value);

            for (int i = 0; i < _tooltips.Count; i++)
            {
                _tooltipImages[i].gameObject.SetActive(false);
                var index = i;
                _tooltips[i].onClick.RemoveAllListeners();
                _tooltips[i].onClick.AddListener(() => SetTooltip(index, true));
            }

            _closeTooltipButton.gameObject.SetActive(false);
            _closeTooltipButton.onClick.RemoveAllListeners();
            var tempIndex = 0;
            _closeTooltipButton.onClick.AddListener(() => SetTooltip(tempIndex, false));
        }

        public void SetTooltip(int index, bool value)
        {
            _tooltipImages[index].gameObject.SetActive(value);

            for (int i = 0; i < _tooltips.Count; i++)
            {
                if (i != index)
                {
                    _tooltipImages[i].gameObject.SetActive(false);
                }
            }

            _closeTooltipButton.gameObject.SetActive(value);
        }

        public void SetPlayersInfo(string localName, int localElo, string otherName, int otherElo, Sprite otherAvatar)
        {
            localPlayerNameTxt.text = localName;
            localPlayerEloTxt.text = localElo.ToString();
            _playerAvt.sprite = GlobalSO.GameplayBus.localPlayer.UserAvt;

            otherPlayerNameTxt.text = otherName;
            otherPlayerEloTxt.text = otherElo.ToString();
            _otherPlayerAvt.sprite = otherAvatar;
        }

        public void SetBallInfo(float extraDistanceValue, float windResistanceValue, float bouncingValue, float sideSpinValue = 0f)
        {
            ExtraDistanceValue.text = extraDistanceValue * 100f + "%";
            WindResistanceValue.text = windResistanceValue * 100f + "%";
            BouncingValue.text = bouncingValue * 100f + "%";
            SideSpinValue.text = sideSpinValue + "%";
        }

        public void SetGearInfo(Sprite image)
        {
            GearIcon.gameObject.SetActive(image != null);
            GearIcon.sprite = image;
        }

        public void SetWind(WeatherData weatherData)
        {
            if (Mathf.Approximately(weatherData.WindSpeed, (int)weatherData.WindSpeed))
                WindValue.text = weatherData.WindSpeed.ToString("N0");
            else
                WindValue.text = weatherData.WindSpeed.ToString("F2");

            if (weatherData.WindSpeed <= 0)
            {
                WindIcon.sprite = ResourcesManager.Instance.GetSpriteFromAtlas("NoWindIcon");
                WindIcon.rectTransform.eulerAngles = Vector3.zero;
            }
            else
            {
                WindIcon.sprite = ResourcesManager.Instance.GetSpriteFromAtlas("WindIcon");
                WindIcon.rectTransform.eulerAngles = new Vector3(0, 0, -weatherData.WindDirection);
            }
        }

        public void UpdateWinDirection(float windDirection, Transform cameraTransform)
        {
            if (cameraTransform == null) return;

            // Convert wind direction (0-360 degrees) to a rotation relative to the camera
            float adjustedDirection = windDirection - cameraTransform.eulerAngles.y;

            // Rotate the WindIcon UI element
            WindIcon.rectTransform.eulerAngles = new Vector3(0, 0, -adjustedDirection);
        }

        public void SetRewardAmount(float amount)
        {
            RewardAmount.text = amount.ToString();
        }

        public void SetAvatarCountDown(float remainTime, float totalTime)
        {
            SetActiveAvatarCountDown(true);

            var remainPercentage = remainTime / totalTime;
            AvatarCountDown.fillAmount = 1f - remainPercentage;
            if (remainPercentage < 0.5f)
            {
                AvatarCountDown.color = Constant.WarningMaskColor;
            }
            else
            {
                AvatarCountDown.color = Constant.DefautMaskColor;
            }

            AvatarCountDownTxt.text = ((int)remainTime).ToString();
        }

        public void TurnOffAvatarCountDown()
        {
            SetActiveAvatarCountDown(false);
        }

        private void SetActiveAvatarCountDown(bool active)
        {
            AvatarCountDown.gameObject.SetActive(active);
            AvatarCountDownTxt.gameObject.SetActive(active);
        }

        public void SetStrokeImages(float remainTime, float totalTime, int strokeCount)
        {
            if (_isTieBreak)
            {
                for (int i = 0; i < localPlayerStrokeFillers.Count; i++)
                {
                    localPlayerStrokeFillers[i].gameObject.SetActive(false);
                    localPlayerStrokeBall[i].gameObject.SetActive(false);
                }

                localRedundantStrokeCountTxt.gameObject.SetActive(false);

                localPlayerStrokeBall[0].gameObject.SetActive(true);

                return;
            }

            var par = GlobalSO.PlayFieldSO.HoleInfo.Par;

            if (GlobalSO.GameplayBus.currentLogic is SinglePlayerClosestToPin)
            {
                par = 1;
            }

            var maxDisplayStroke = localPlayerStrokeFillers.Count;
            if (strokeCount < maxDisplayStroke)
            {
                for (int i = 0; i < maxDisplayStroke; i++)
                {
                    if (i > strokeCount)
                    {
                        localPlayerStrokeFillers[i].gameObject.SetActive(false);
                        localPlayerStrokeBall[i].gameObject.SetActive(false);
                    }
                    else
                    {
                        localPlayerStrokeBall[i].gameObject.SetActive(true);
                        var temp = localPlayerStrokeBall[i].color; temp.a = 1f;
                        localPlayerStrokeBall[i].color = temp;
                        if (i < par)
                        {
                            localPlayerStrokeFillers[i].gameObject.SetActive(true);
                            localPlayerStrokeFillers[i].color = Constant.HighlightTextColor;

                            localPlayerStrokeFillers[i].fillAmount = 1f;
                        }
                        else
                        {
                            localPlayerStrokeFillers[i].gameObject.SetActive(false);
                        }

                        if (i == strokeCount)
                        {
                            var next = i + 1;
                            localPlayerStrokeFillers[i].gameObject.SetActive(true);
                            localPlayerStrokeFillers[i].color = Constant.HighlightTextColor;
                            localPlayerStrokeFillers[i].fillAmount = 1f - (remainTime / totalTime);
                            temp = localPlayerStrokeBall[i].color; temp.a = 0.5f;
                            localPlayerStrokeBall[i].color = temp;


                            for (int j = i + 1; j < maxDisplayStroke; j++)
                            {
                                if (j < par)
                                {
                                    localPlayerStrokeFillers[j].gameObject.SetActive(true);
                                    localPlayerStrokeFillers[j].color = Constant.DisableTextColor;

                                    localPlayerStrokeFillers[j].fillAmount = 1f;
                                    localPlayerStrokeBall[j].gameObject.SetActive(false);
                                    continue;
                                }

                                localPlayerStrokeFillers[j].gameObject.SetActive(false);
                                localPlayerStrokeBall[j].gameObject.SetActive(false);
                            }
                            break;
                        }
                    }
                }

                localRedundantStrokeCountTxt.gameObject.SetActive(false);
            }
            else
            {
                for (int i = 0; i < localPlayerStrokeFillers.Count; i++)
                {
                    if (i == 0)
                    {
                        localPlayerStrokeFillers[i].gameObject.SetActive(true);
                        localPlayerStrokeFillers[i].fillAmount = 1f - (remainTime / totalTime);
                    }
                    else
                    {
                        localPlayerStrokeFillers[i].gameObject.SetActive(false);
                    }
                }

                for (int i = 0; i < localPlayerStrokeBall.Count; i++)
                {
                    if (i == 0)
                    {
                        localPlayerStrokeBall[i].gameObject.SetActive(true);
                        var temp = localPlayerStrokeBall[i].color; temp.a = 0.5f;
                        localPlayerStrokeBall[i].color = temp;
                    }
                    else
                    {
                        localPlayerStrokeBall[i].gameObject.SetActive(false);
                    }
                }

                localRedundantStrokeCountTxt.gameObject.SetActive(true);
                localRedundantStrokeCountTxt.text = "+" + (strokeCount - 1).ToString();
            }

            _cacheLocalStroke = strokeCount;
        }

        public void OpenSettings()
        {
            switch (GlobalSO.GameplayBus.currentLogic.logicData)
            {
                case TournamentLogicSO:
                    MasterManager.Instance.OpenUIComponent(UIComponentEnum.TournamentSettingsComponentUI);
                    break;
                case MultiplayerNormalLogicSO:
                    MasterManager.Instance.OpenUIComponent(UIComponentEnum.H2HSettingsComponentUI);
                    break;
            }
        }

        void OnEnable()
        {
            _settingsButton.onClick.AddListener(OpenSettings);
        }

        void OnDisable()
        {
            _settingsButton.onClick.RemoveListener(OpenSettings);
        }

        public void OnSpectate(bool isLocal)
        {
            if (!isLocal)
            {
                windInfo.SetActive(false);
                ballInfo.SetActive(false);
            }
            else
            {
                windInfo.SetActive(true);
                ballInfo.SetActive(true);
            }
        }
    }
}
