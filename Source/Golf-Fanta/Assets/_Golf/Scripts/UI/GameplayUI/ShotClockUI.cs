using System;
using _Golf.Scripts.Common;
using _Golf.Scripts.GameLogic;
using TinyMessenger;
using UnityEngine;
using UnityEngine.UI;
namespace _Golf.Scripts.UI.GameplayUI
{
    public class ShotClockUI : MonoBehaviour
    {
        [SerializeField] private Text _shotClockText;
        [SerializeField] private GameObject _container;

        private TinyMessageSubscriptionToken _setShotClockToken;
        private TinyMessageSubscriptionToken _onLocalPlayerHitToken;
        private TinyMessageSubscriptionToken _onShotClockTimeOut;

        private void Awake()
        {
           _container.SetActive(false); 
        }
        private void OnEnable()
        {
           //_setShotClockToken = ActionDispatcher.Bind<SetShotClockAction>(SetShotClockText); 
           //_onLocalPlayerHitToken = ActionDispatcher.Bind<LocalPlayerHitBall>(OnBallHit);
           //_onShotClockTimeOut = ActionDispatcher.Bind<ShotClockTimeOut>(OnShotClockTimeOut);
        }
        
        private void OnDisable()
        {
            //ActionDispatcher.Unbind(_setShotClockToken); 
            //ActionDispatcher.Unbind(_onLocalPlayerHitToken);
        }

        private void OnBallHit(LocalPlayerHitBall _)
        {
            _container.SetActive(false);
        }

        private void OnShotClockTimeOut(ShotClockTimeOut _)
        {
            _container.SetActive(false);
        }
        private void SetShotClockText(SetShotClockAction ctx)
        {
            _container.SetActive(true);
            var remainTime = ctx.RemainTime;
            _shotClockText.text = remainTime.ToString() + "/" + ctx.TotalTime + "(" + ctx.StrokeCount +")";
        }
    }

    public class ShotClockTimeOut: ActionBase
    {
        public ShotClockTimeOut()
        {

        }
    }

    public class TurnOffCountDownAction : ActionBase
    {
        public TurnOffCountDownAction()
        {

        }
    }

    public class SetShotClockAction : ActionBase
    {
        public float RemainTime;
        public float TotalTime;
        public int StrokeCount;

        public SetShotClockAction(float remainTime, float totalTime, int strokeCount)
        {
            RemainTime = remainTime;
            TotalTime = totalTime;
            StrokeCount = strokeCount;
        }
    }

    public class WarningShotAction : ActionBase
    {
        public bool TurnOn;
        public WarningShotAction(bool turnOn = false)
        {
            TurnOn = turnOn;
        }
    }
}