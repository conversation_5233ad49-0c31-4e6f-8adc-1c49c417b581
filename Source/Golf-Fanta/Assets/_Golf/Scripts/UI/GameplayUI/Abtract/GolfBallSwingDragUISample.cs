using System.Collections;
using System.Collections.Generic;
using _Golf.Scripts.Lobby;
using UnityEngine;

namespace GolfGame
{
    public abstract class GolfBallSwingDragUISample : PlayerSyncable
    {
        public virtual void OnHide()
        {
            gameObject.SetActive(false);
        }

        public virtual void OnOpen()
        {
            gameObject.SetActive(false);
        }

        public virtual void OnGoingTop()
        {

        }
    }
}