using System.Collections.Generic;
using System.Threading;
using _Golf.Scripts.Common;
using _Golf.Scripts.Lobby;
using _Golf.Scripts.Networking.Courses;
using _Golf.Scripts.ScriptableObjects.Tours;
using Cysharp.Threading.Tasks;
using GolfPhysics;
using TinyMessenger;
using UnityEngine;
using UnityEngine.UIElements;

namespace _Golf.Scripts.UI
{
	public class LoadingProgressUI : MonoBehaviour
	{
		private TinyMessageSubscriptionToken _uiLoadCourseRequestToken;
		private CancellationTokenSource _cancellationTokenSource;
		
		private void OnEnable()
		{
			_uiLoadCourseRequestToken = ActionDispatcher.Bind<RequestCheckAndDownload>(OnCheckAndDownloadRequested);
		}

		private void OnDisable()
		{
			ActionDispatcher.Unbind(_uiLoadCourseRequestToken);
		}

		private async void OnCheckAndDownloadRequested(RequestCheckAndDownload ctx)
		{
			if (ctx == null || ctx.UiPanel == null || ctx.Lobby == null || ctx.Item == null)
			{
				UnityEngine.Debug.LogError("RequestCheckAndDownload: Missing references");
				return;
			}

			UpdateProgressBar(ctx.UiPanel, 0, ctx.Item);

			List<HoleInfo> holeInfos = new List<HoleInfo>();

			for (int i = 0; i < ctx.Lobby.CourseInfo.Holes.Count; i++)
			{
				holeInfos.Add(ctx.Lobby.CourseInfo.Holes[i]);
			}

			_cancellationTokenSource?.Dispose();
			_cancellationTokenSource = new CancellationTokenSource();
			await MapLoader.Instance.CheckAndDownloadMaps(holeInfos, _cancellationTokenSource.Token, (progress) =>
			{
				UpdateProgressBar(ctx.UiPanel, progress, ctx.Item);
			});
			await UniTask.Delay(Constant.TransitionDelayMs);

			//If not cancelled
			ctx.UiPanel?.SetUpRequestMatchUp(ctx.Lobby);
		}

		private void UpdateProgressBar(HeadToHeadStageUIComponent uiPanel, float progress, VisualElement item)
		{
			uiPanel?.SetUpAssetLoading(progress, item);
		}
	}

	public class RequestCheckAndDownload : ActionBase
	{
		public HeadToHeadStageUIComponent UiPanel;
		public LobbyProperty Lobby;
		public VisualElement Item;
		public RequestCheckAndDownload(HeadToHeadStageUIComponent uiPanel, LobbyProperty lobby, VisualElement item)
		{
			UiPanel = uiPanel;
			Lobby = lobby;
			Item = item;
		}

		public RequestCheckAndDownload()
		{
		}

		public void ClearReference()
		{
			UiPanel = null;
			Lobby = null;
			Item = null;
		}
	}
}