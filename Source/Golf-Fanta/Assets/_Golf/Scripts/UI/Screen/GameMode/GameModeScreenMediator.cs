using _Golf.Scripts.Common;
using Cysharp.Threading.Tasks;
using GolfGame;
using GolfGame.UIMediators.Base;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace _Golf.Scripts.UI
{
    public class GameModeScreenMediator : UIScreenMediator
    {
        public override void OnOpen(EScreenEnum prevScreen, object[] data = null)
        {
            _ = MasterManager.Instance.HideAllComponents();
            _ = MasterManager.Instance.OpenUIComponent(UIComponentEnum.TopBarComponentUI);
            _ = MasterManager.Instance.OpenUIComponent(UIComponentEnum.GameModeComponentUI);
            _ = MasterManager.Instance.OpenUIComponent(UIComponentEnum.MainMenuProfileUI);
            _ = MasterManager.Instance.OpenUIComponent(UIComponentEnum.GameModeInfoComponentUI);
        }

        public override void OnHide(EScreenEnum nextScreen, object[] data = null)
        {
            MasterManager.Instance.HideUIComponent(UIComponentEnum.GameModeComponentUI);
            MasterManager.Instance.HideUIComponent(UIComponentEnum.MainMenuProfileUI);
            MasterManager.Instance.HideUIComponent(UIComponentEnum.TopBarComponentUI);
            MasterManager.Instance.HideUIComponent(UIComponentEnum.GameModeInfoComponentUI);
        }

        public override UniTask OnHideWithAnimation(EScreenEnum nextScreen, object[] data = null)
        {
            var task = new List<UniTask>
            {
                MasterManager.Instance.HideUIComponentAsync(UIComponentEnum.GameModeComponentUI),
                MasterManager.Instance.HideUIComponentAsync(UIComponentEnum.MainMenuProfileUI),
                MasterManager.Instance.HideUIComponentAsync(UIComponentEnum.TopBarComponentUI),
                MasterManager.Instance.HideUIComponentAsync(UIComponentEnum.GameModeInfoComponentUI)
            };

            return UniTask.WhenAll(task);
        }

        public override async UniTask OnOpenWithAnimation(EScreenEnum prevScreen, object[] data = null)
        {
            await MasterManager.Instance.HideAllComponents();
            var task = new List<UniTask>
            {
                MasterManager.Instance.OpenUIComponent(UIComponentEnum.TopBarComponentUI),
                MasterManager.Instance.OpenUIComponent(UIComponentEnum.GameModeComponentUI),
                MasterManager.Instance.OpenUIComponent(UIComponentEnum.MainMenuProfileUI),
                MasterManager.Instance.OpenUIComponent(UIComponentEnum.GameModeInfoComponentUI)
            };

            await UniTask.WhenAll(task);
        }
    }
}
