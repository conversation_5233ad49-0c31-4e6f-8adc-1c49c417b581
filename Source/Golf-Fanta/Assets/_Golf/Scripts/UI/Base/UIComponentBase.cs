using System.Threading.Tasks;
using _Golf.Scripts.Lobby;
using _Golf.Scripts.UI;
using Cysharp.Threading.Tasks;
using UnityEngine;
using UnityEngine.UIElements;

public abstract class UIComponentBase : PlayerSyncable
{
    protected VisualElement _root;
    public abstract UIComponentMediator CreateMediator();
    
    public abstract void Init();
    public abstract void OnOpen(object[] data = null);
    public abstract void OnHide(object[] data = null);

    private UIComponentEnum _componentEnum;
    public UIComponentEnum ComponentEnum => _componentEnum;

    private bool _canClose = false;
    protected bool CanClose { get => _canClose; set => _canClose = value; }
    public virtual async UniTask OnHideWithAnimation(object[] data = null){  OnHide(); }
    public virtual async UniTask OnOpenWithAnimation(object[] data = null)
    {
        CanClose = true;
        try
        {
            OnOpen(data);
        }
        catch (System.Exception e)
        {
            Debug.LogError(e);
            CanClose = true;
        }
        await UniTask.WaitUntil(() => CanClose);
    }

    internal void SetEnum(UIComponentEnum componentEnum)
    {
        _componentEnum = componentEnum;
    }

    internal virtual IPanel GetPanelSettings()
    {
        if (_root == null)
        {
            return null;
        }

       return _root.panel;
    }
}

public abstract class UIComponentBase<T> : UIComponentBase where T : UIComponentMediator
{
    protected T Mediator;

    public void SetMediator(T mediator)
    {
        Mediator = mediator;
    }

    public T GetMediator()
    {
        return Mediator;
    }
}