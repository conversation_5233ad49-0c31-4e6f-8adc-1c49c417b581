using System;
using System.Collections;
using System.Collections.Generic;
using System.Threading.Tasks;
using _Golf.Scripts.Common;
using _Golf.Scripts.Core;
using _Golf.Scripts.GlobalManagers;
using _Golf.Scripts.Lobby;
using _Golf.Scripts.ScriptableObjects;
using AYellowpaper.SerializedCollections;
using DG.Tweening;
using GolfGame;
using GolfGame.UIMediators.Base;
using Kamgam.UIToolkitScrollViewPro;
using Kamgam.UIToolkitVisualScripting;
using TinyMessenger;
using Unity.Services.Authentication;
using UnityEngine;
using UnityEngine.Serialization;
using UnityEngine.UIElements;
namespace _Golf.Scripts.UI
{
	public class MatchMakingAvatar : UIComponentBase<MatchMakingAvatarMediator>
	{
		private GameplayBus _gameplayBus;
		
		private int _modeDisplay = 0;

		#region Constanst
		private const float TimeDelayLoadUI = 0.1f;
		private const float TimeDelay = 0.5f;
		private const float TimeWaitPlayer = 1f;
		private const int TimeWaitRematchEffectMs = 2000;
		private const float TimeDelaySlowMatching = 0.05f;
		private const float DefaultScrollSpeed = 3f;
		private const float BlurOpacity = 0.25f;
		private const float DefaultOpacity = 1f;
		private const float MinStepScroll = 0.5f;
		private float _scrollSpeed = 3f;
		#endregion
		
		#region Resources
		[FormerlySerializedAs("avatarSettings")]
		[SerializeField] private List<Sprite> maskSprites;
		[SerializeField] private List<Sprite> textSprites;
		[SerializeField] private SerializedDictionary<string, Sprite> textSpriteMap;
		[SerializeField] private SerializedDictionary<string, Sprite> maskSpriteMap;
		#endregion

		#region UI Elements
		UIDocument _document;
		VisualElement _root;
		private ScrollViewPro _scrollViewPro;
		private Button _cancelButton;
		private VisualElement _cancelZone;
		private VisualElement _masterPanel;
		private VisualElement _playerInfo;
		private VisualElement _randomAvatar;
		private VisualElement _opponentInfo;
		private VisualElement _bg;
		private VisualElement _BG;
		private VisualElement _topMask;
		private VisualElement _bottomMask;
		private VisualElement _slash;
		private VisualElement _topMaskEffect;
		private VisualElement _botMaskEffect;
		private VisualElement _center;
		private VisualElement _player;
		private VisualElement _opponent;
		private List<Label> _tourNameText;
		private VisualElement _prizeArea;
		private VisualElement _rankContent;
		private VisualElement _courseContent;
		private Label _holeNameLabel;
		private Label _parLabel;
		private Label _prizeLabel;
		private List<VisualElement> _avatars = new List<VisualElement>();
		#endregion
		
		private bool _isMatched = true;
		private Tweener _countDownTween;
		private TinyMessageSubscriptionToken _rematchCancelledToken;

		private List<AvatarSO> allAvatar;
		
		private IEnumerator HideCoroutine()
		{
			// Move Out
			_player.RemoveFromClassList("player-in");
			_opponent.RemoveFromClassList("opponent-in");
			yield return new WaitForSeconds(TimeDelay);
			// Reset State for all
			_topMask.RemoveFromClassList("top-mask-in");
			_bottomMask.RemoveFromClassList("bottom-mask-in");

			_topMaskEffect.RemoveFromClassList("mask-show");
			_botMaskEffect.RemoveFromClassList("mask-show");
			_slash.RemoveFromClassList("mask-show");

			_slash.RegisterCallbackOnce<TransitionEndEvent>((evt) =>
			{
				_center.RemoveFromClassList("center-in");
				_masterPanel.style.display = DisplayStyle.None;
			});

			// _center.RemoveFromClassList("center-in");
			_bg.RemoveFromClassList("bg-in");

			_BG.RemoveFromClassList("bg-in");
			_BG.style.backgroundImage = null;
			_randomAvatar.RemoveFromClassList("random-avatar-out");
			_opponentInfo.RemoveFromClassList("opponent-info-in");
			_rankContent.RemoveFromClassList("rank-text-out");
			_courseContent.RemoveFromClassList("course-info-in");
			// _cancelButton.RemoveFromClassList("cancel-button-in");
			_cancelZone.RemoveFromClassList("cancel-button-in");
			_prizeArea.RemoveFromClassList("prize-content-in");
		}

		private IEnumerator ShowCoroutine(LobbyProperty lobbyProperty)
		{
			 var tourName = LocalizationManager.Instance.GetString(lobbyProperty.TourInfo.GetNameKey()).ToUpper();
			_tourNameText.ForEach(label => label.text = tourName);

			yield return new WaitForEndOfFrame();
			_masterPanel.style.display = DisplayStyle.Flex;
			yield return new WaitForSeconds(TimeDelayLoadUI);
			_topMask.AddToClassList("top-mask-in");
			_bottomMask.AddToClassList("bottom-mask-in");
			_bg.AddToClassList("bg-in");
			_BG.AddToClassList("bg-in");

			if (maskSpriteMap.ContainsKey(lobbyProperty.TourInfo.GetBannerAddress()))
			{
				_BG.style.backgroundImage = new StyleBackground(maskSpriteMap[lobbyProperty.TourInfo.GetBannerAddress()]);
			}
			else
			{
				_BG.style.backgroundImage = new StyleBackground(maskSprites[0]);
			}	

			yield return new WaitForSeconds(TimeDelay);
			_center.AddToClassList("center-in");

			_center.RegisterCallbackOnce<TransitionEndEvent>((evt) =>
			{
				_topMaskEffect.AddToClassList("mask-show");
				_botMaskEffect.AddToClassList("mask-show");
				_slash.AddToClassList("mask-show");
			});

			yield return new WaitForSeconds(TimeWaitPlayer);
			// Entry Content In
			// _cancelButton.AddToClassList("cancel-button-in");
			_cancelZone.AddToClassList("cancel-button-in");
			_prizeArea.RemoveFromClassList("prize-content-in");
			_player.AddToClassList("player-in");
			_opponent.AddToClassList("opponent-in");
			
			// Start Scrolling
			if (_modeDisplay == 0)
			{
				_scrollSpeed = DefaultScrollSpeed;
				_isMatched = false;
			}
			else
			{
				_randomAvatar.AddToClassList("random-avatar-out");
				yield return new WaitForSeconds(TimeDelay);
				_opponentInfo.AddToClassList("opponent-info-in");

				_isMatched = true;
				
				var opponentPlayer = GlobalSO.GameplayBus.localLobby.LocalPlayers.Find(
					(player) => player.GetId() != GlobalSO.GameplayBus.localPlayer.GetId());
				
				opponentPlayer.SetDisplayName(ACSIIHelper.GenerateGuestName(opponentPlayer.GetId()));
	
				SetPlayerInfo(opponentPlayer, _opponentInfo);
				// Start Waiting Effect
				bool isALlReady = GlobalSO.GameplayBus.localLobby.LocalPlayers.TrueForAll((player =>
					player.GetUserStatus() == PlayerStatus.ReadyForRematch));
				
				var infoText =  _opponentInfo.Q<Label>("Name");

				if (!isALlReady)
				{
					_countDownTween = DOVirtual.Int(10, 0, 10, newValue =>
					{
						infoText.text = "Waiting... " + newValue;
					}).SetEase(Ease.Linear).OnComplete(() =>
					{
						var localPlayer =
							GlobalSO.GameplayBus.localLobby.LocalPlayers.Find((player => player.GetId() ==  GlobalSO.GameplayBus.localPlayer.GetId()));
                    
						// Reset Stroke
						localPlayer.ResetPlayer();
                    
						localPlayer.SetUserStatus(PlayerStatus.FinishGame);
                    
						_ =  GlobalSO.GameplayBus.lobbyHandler.SyncPlayerData(localPlayer);
						MasterManager.Instance.HideUIComponent(UIComponentEnum.MatchMakingComponentUI);
					});
				}
			}
		}

		#region Button Function

		private void CancelRematchButton(Button cancel)
		{
			cancel.style.unityBackgroundImageTintColor = Color.grey;
			cancel.pickingMode = PickingMode.Ignore;
			MasterManager.Instance.HideAllComponents();
			ActionDispatcher.Dispatch(new ExitGameplayAction());
		}
		
		private void CancelMatchMaking(Button cancel)
		{
			cancel.style.unityBackgroundImageTintColor = Color.grey;
			cancel.pickingMode = PickingMode.Ignore;
			ActionDispatcher.Dispatch(new MatchMakingCancelAction());
		}

		#endregion

		#region Base UI Logic
		
		public override UIComponentMediator CreateMediator()
		{
			return new MatchMakingAvatarMediator(this);
		}
		
		public override void Init()
		{
			_gameplayBus = GlobalSO.GameplayBus;
			StartCoroutine(InitFindingPlayer());
		}
		public override void OnOpen(object[] data = null)
		{
			_cancelButton.style.unityBackgroundImageTintColor = Color.white;
			_cancelButton.pickingMode = PickingMode.Position;
			_playerInfo.style.display = DisplayStyle.Flex;
			_rematchCancelledToken = ActionDispatcher.Bind<LocalLobbyOpponentLeft>(HandleRematchCancelled);
			// Normal Mode
			if (data != null)
			{
				_modeDisplay = (int)data[0];
				_cancelButton.clickable = new Clickable(() =>
				{
					CancelRematchButton(_cancelButton);
				});
			}
			else
			{
				_modeDisplay = 0;
				_cancelButton.clickable = new Clickable(() =>
				{
					_cancelButton.clickable = null;
					CancelMatchMaking(_cancelButton);
				});
			}
			
			foreach (var item in _avatars)
			{
				item.style.opacity = BlurOpacity;
			}
			
			var playerId = AuthenticationService.Instance.PlayerId;
			var playerInfo = GlobalSO.PlayerInfoSO.Info;
			var localPlayer = new LocalPlayer(playerId, playerInfo.DisplayName, playerInfo.H2hRank, new PlayerAvatarData(playerInfo.Avatar.Id));
			
			SetPlayerInfo(GlobalSO.GameplayBus.localPlayer, _playerInfo, true);
			
			ConfigEntryFee(Visibility.Hidden);
			
			StartCoroutine(ShowCoroutine(_gameplayBus.currentLobbyProperty));
		}
		
		public override void OnHide(object[] data = null)
		{
			ActionDispatcher.Unbind(_rematchCancelledToken);
			_countDownTween.Kill();
			StartCoroutine(HideCoroutine());
		}

		#endregion
		
		private void ConfigEntryFee(Visibility visibility, int entryFee = 0)
		{
			var entryFeeLabels = _root.Query("EntryFeeArea").ToList();
			foreach (var entry in entryFeeLabels)
			{
				entry.style.visibility = visibility;
				var label = entry.Q<Label>("EntryFeeLabel");
				label.text = entryFee.ToString();
			}
		}
		private IEnumerator ConfigureFooter(LobbyProperty lobbyProperty = null, bool isRematch = false)
		{
			bool isFoundOpponent = lobbyProperty != null;
			if (!isFoundOpponent)
			{
				// _cancelButton.AddToClassList("cancel-button-in");
				_cancelZone.AddToClassList("cancel-button-in");
				_prizeArea.RemoveFromClassList("prize-content-in");
			}
			else
			{
				// _cancelButton.RemoveFromClassList("cancel-button-in");
				_cancelZone.RemoveFromClassList("cancel-button-in");
				_prizeArea.AddToClassList("prize-content-in");
			}

			yield return new WaitForSeconds(TimeDelay);
			
			if (!isFoundOpponent) yield break;
			
			var entryFeeLabels = _root.Query("EntryFeeArea").ToList();
			var fee = lobbyProperty.TourInfo.GetEntryFee();
			DOVirtual.Int(fee, 0, TimeDelay, newValue =>
			{
				foreach (var entry in entryFeeLabels)
				{
					var label = entry.Q<Label>("EntryFeeLabel");
					label.text = newValue.ToString();
				}
				_prizeLabel.text = ((fee - newValue) * 2).ToString();
			}).SetEase(Ease.InSine).OnComplete(() =>
			{
				if (!isRematch)
				{
					ActionDispatcher.Dispatch(new MatchMakingFinishAction());
				}
				else
				{
					DOVirtual.DelayedCall(1f, () =>
					{
						ActionDispatcher.Dispatch(new LocalLobbyRematchAction());
					});
				}
			});
		}
		
		private IEnumerator InitFindingPlayer()
		{
			_document = GetComponent<UIDocument>();
			_root = _document.rootVisualElement;
			
			_scrollViewPro = _root.Q<ScrollViewPro>("StageScrollView");
			
			// Get And Reset
			_masterPanel = _root.Q<VisualElement>("MatchMakingPanel");
			_bg = _root.Q<VisualElement>("MatchMakingPanel");
			_topMask = _root.Q<VisualElement>("TopMask");
			_bottomMask = _root.Q<VisualElement>("BottomMask");
			_center = _root.Q<VisualElement>("Center");
			_player = _root.Q<VisualElement>("PlayerPanel");
			_opponent = _root.Q<VisualElement>("OpponentArea");
			_playerInfo = _root.Q<VisualElement>("PlayerInfo");
			_randomAvatar = _root.Q<VisualElement>("OpponentMatchingView");
			_opponentInfo = _root.Q<VisualElement>("OpponentInfo");
			_cancelButton = _root.Q<Button>("CancelButton");
			GolfUIToolKit.Utility.SetButtonValue(_cancelButton, null, GolfUIToolKit.Utility.ButtonColor.Blue);
			_cancelZone = _root.Q<VisualElement>("CancelZone");
			_tourNameText = _root.Query<Label>("RankText").ToList();
			_prizeArea = _root.Q<VisualElement>("PrizeArea");
			_prizeLabel = _prizeArea.Q<Label>("PrizeAmountLabel");
			_courseContent = _root.Q<VisualElement>("CourseInfo");
			_rankContent = _root.Q<VisualElement>("RankText");
			_holeNameLabel = _root.Q<Label>("HoleText");
			_parLabel = _root.Q<Label>("ParText");

			_topMaskEffect = _root.Q<VisualElement>("Top");
			_botMaskEffect = _root.Q<VisualElement>("Bot");
			_slash = _root.Q<VisualElement>("Slash");
			_BG = _root.Q<VisualElement>("BG");
			
			yield return new WaitForEndOfFrame();

			allAvatar = GlobalSO.InventoryItemSo.avatars;
			
			for (int i = 0; i < allAvatar.Count; i++)
			{
				VisualElement newItem = new VisualElement
				{
					style =
					{
						width = _scrollViewPro.resolvedStyle.width,
						height = 250
					}
				};
				_avatars.Add(newItem);
				newItem.style.opacity = BlurOpacity;
				newItem.SetBackgroundImage(allAvatar[i].image);
				_scrollViewPro.Add(newItem);
			}
		}
		
		private void FixedUpdate()
		{
			if(!_isMatched)
				RandomAvatarScroller();
		}

		private void RandomAvatarScroller()
		{
			if (_scrollSpeed < MinStepScroll) return;
			
			var wheelEvent = WheelEvent.GetPooled(new Event
			{
			    type = EventType.ScrollWheel,
			    delta = new Vector2(0, - _scrollSpeed)
			});
			
			wheelEvent.target = _scrollViewPro;

			try
			{
				wheelEvent.target = _scrollViewPro;
				_scrollViewPro.SendEvent(wheelEvent);
			}
			catch (Exception e)
			{
				Console.WriteLine(e);
				throw;
			}
			finally
			{
				wheelEvent.Dispose();
			}
			
		}

		public void OnFoundOpponent(LocalPlayer opponent, LobbyProperty currentLobby)
		{
			if(_isMatched) return;
			
			StartCoroutine(SmoothScrollAndSwitch(opponent, currentLobby));
		}
		
		private void HandleRematchCancelled(LocalLobbyOpponentLeft obj)
		{
			GeneralPopupContainer popupContainer = new GeneralPopupContainer
			(
				title: "Rematch Canceled",
				description: obj.OpponentPlayer.GetDisplayName() + " has canceled the rematch request.",
				continueButtonText: "OK",
				continueAction: () =>
				{
					MasterManager.Instance.HideUIComponent(UIComponentEnum.GeneralPopupUIComponent);
					MasterManager.Instance.HideUIComponent(UIComponentEnum.MatchMakingComponentUI);
				}
			);

			MasterManager.Instance.OpenUIComponent(UIComponentEnum.GeneralPopupUIComponent, new object[] { popupContainer });
		}


		public async void OnReadyForNewMatch(List<LocalPlayer> players, LobbyProperty currentLobby)
		{
			_countDownTween.Kill();

			await Task.Delay(TimeWaitRematchEffectMs);
				
			var opponent = players.Find((player => player.GetId() != GlobalSO.GameplayBus.localPlayer.GetId()));
			
			SetPlayerInfo(opponent, _opponentInfo);
			
			ConfigEntryFee(Visibility.Visible, 0);
			
			StartCoroutine(ConfigureFooter(currentLobby, true));
		}
		
		private IEnumerator SmoothScrollAndSwitch(LocalPlayer opponent, LobbyProperty currentLobby)
		{
			// Reset Opacity
			foreach (var item in _avatars)
			{
				item.style.opacity = DefaultOpacity;
			}
			
			// Slow Scroll
			while (_scrollSpeed > MinStepScroll)
			{
				_scrollSpeed -= MinStepScroll;
				yield return new WaitForSeconds(TimeDelaySlowMatching);
			}
			
			yield return new WaitForSeconds(TimeDelaySlowMatching);
			
			// Stop Scroll
			_isMatched = true;
			
			_randomAvatar.AddToClassList("random-avatar-out");
			yield return new WaitForSeconds(TimeDelay);
			_opponentInfo.AddToClassList("opponent-info-in");

			// hole name
			_holeNameLabel.text = LocalizationManager.Instance.GetString(currentLobby.CourseInfo.CurrentHole.Name).ToUpper();

			var localizePar = LocalizationManager.Instance.GetLocalizedString("game_play_par_num");
			if (localizePar == null)
			{
				_parLabel.text = "PAR " + currentLobby.CourseInfo.CurrentHole.Par;
			}
			else
			{
				localizePar.Arguments = new object[] { currentLobby.CourseInfo.CurrentHole.Par };
				_parLabel.text = localizePar.GetLocalizedString();
			}

			yield return new WaitForSeconds(TimeDelay);
			_rankContent.AddToClassList("rank-text-out");
			_courseContent.AddToClassList("course-info-in");
			
			SetPlayerInfo(opponent, _opponentInfo);
			
			ConfigEntryFee(Visibility.Visible, currentLobby.TourInfo.GetEntryFee());
			StartCoroutine(ConfigureFooter(currentLobby));
		}

		private void SetPlayerInfo(LocalPlayer player, VisualElement info, bool isLocalPlayer = false)
		{
			Label nameInfo = info.Q<Label>("Name");
			VisualElement avatarInfo = info.Q<VisualElement>("Avatar");

			string nameText = "Guest";

			if (!string.IsNullOrEmpty(player.GetDisplayName()))
				nameText = player.GetDisplayName();

			int avatarIndex = 1;
			
			info.Q<Label>("RankLabel").text = player.GetPlayerRankInfo().currentPoint.ToString();

			nameInfo.text = nameText.Split("#")[0];
			
			var avatarSprite = player.UserAvt;
			
			avatarInfo.SetBackgroundImage(avatarSprite);
		}
	}

	public class MatchMakingAvatarMediator : UIComponentMediator
	{
		private new MatchMakingAvatar UIComponent
		{
			get
			{
				return (MatchMakingAvatar)_uiComponent;
			}
		}
		private TinyMessageSubscriptionToken _foundOpponentToken;
		private TinyMessageSubscriptionToken _localLobbyReadyForNewMatch;
		public MatchMakingAvatarMediator(UIComponentBase uIComponent) : base(uIComponent) { }
		public override void InitDependency(Dictionary<UIComponentEnum, UIComponentMediator> uiComponentMediators, Dictionary<EScreenEnum, UIScreenMediator> uiScreenMediators) { }
		public override void ComponentInitReference() { }
		public override void ComponentInitListener()
		{
			_foundOpponentToken = ActionDispatcher.Bind<FoundOpponentAction>(HandleFoundOpponent);
			_localLobbyReadyForNewMatch = ActionDispatcher.Bind<LocalLobbyReadyForNewMatch>(OnReadyForNewMatch);
		}

		private void OnReadyForNewMatch(LocalLobbyReadyForNewMatch obj)
		{
			UIComponent.OnReadyForNewMatch(obj.Players, GlobalSO.GameplayBus.currentLobbyProperty);
		}

		public override void ComponentDisposeReference() { }
		public override void ComponentDisposeListener()
		{
			ActionDispatcher.Unbind(_foundOpponentToken);
			ActionDispatcher.Unbind(_localLobbyReadyForNewMatch);
		}
		private void HandleFoundOpponent(FoundOpponentAction ctx)
		{
			UIComponent.OnFoundOpponent(ctx.Opponent, ctx.LobbyProperty);
		}
		
		public override ModuleProxy ComponentGetProxy()
		{
			throw new System.NotImplementedException();
		}

		public override ModuleManager ComponentGetManager()
		{
			throw new System.NotImplementedException();
		}
	}
}