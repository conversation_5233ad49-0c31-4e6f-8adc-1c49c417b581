using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using _Golf.Scripts.Common;
using Cysharp.Threading.Tasks;
using UnityEngine;
using UnityEngine.Localization;
using UnityEngine.Localization.Settings;
using UnityEngine.Localization.Tables;

namespace _Golf.Scripts.GlobalManagers
{
    public enum Language
    {
        English = 0,
        Vietnamese = 1
    }

    public class LocalizationManager : MonoSingleton<LocalizationManager>
    {
        private const Language DefaultLanguage = Language.English;
        private const string CachedLanguageKey = "SelectedLanguage";
        private const string CoreTableName = "CoreStringTable";
        private readonly Dictionary<Language, string> _languageCodes = new Dictionary<Language, string>()
        {
            [Language.English] = "en-US",
            [Language.Vietnamese] = "vi-VN"
        };

        public Dictionary<Language, string> LanguageCodes => _languageCodes;

        public Language CurrentLanguage { get; private set; }
        public event Action<Language> OnLanguageChanged;

        private StringTable _coreStringTable;

        public async Task Init()
        {
            LocalizationSettings.Instance.ResetState();
            await LocalizationSettings.InitializationOperation.Task;
            Enum.TryParse<Language>(PlayerPrefs.GetString(CachedLanguageKey), out var cachedLanguage);
            ChangeLanguage(cachedLanguage);
            _coreStringTable = await LocalizationSettings.StringDatabase.GetTableAsync(CoreTableName).Task;
        }

        public void ChangeLanguage(Language language)
        {
            CurrentLanguage = language;
            var languageCode = _languageCodes.TryGetValue(language, out var code) ? code : _languageCodes[DefaultLanguage];
            LocalizationSettings.SelectedLocale = LocalizationSettings.AvailableLocales.GetLocale(languageCode);

            PlayerPrefs.SetString(CachedLanguageKey, language.ToString());
            OnLanguageChanged?.Invoke(language);
        }

        public string GetString(string key)
        {
            return _coreStringTable.GetEntry(key)?.Value ?? key;
        }

        public bool IsKeyInExistTable(string key)
        {
            return _coreStringTable != null && _coreStringTable.GetEntry(key) != null;
        }


        public LocalizedString GetLocalizedString(string key)
        {
            try
            {
                var entry = _coreStringTable.GetEntry(key);
                if (entry != null)
                {
                    return new LocalizedString { TableReference = CoreTableName, TableEntryReference = key };
                }

                return null;
            }
            catch (Exception e)
            {
                Debug.LogWarning($"LOCALIZED KEY NOT FOUND: {key}");
                return null;
            }
        }
    }
}