using System;
using System.Collections.Generic;
using System.Linq;
using _Golf.Scripts.Common;
using _Golf.Scripts.Core.CourseController;
using _Golf.Scripts.ScriptableObjects;
using Cysharp.Threading.Tasks;
using GolfGame;
using Photon.Pun;
using Photon.Realtime;
using TinyMessenger;
using UnityEngine;
namespace _Golf.Scripts.Core
{
    public class GolfTaskManager : BaseControllerComponent, ISubscribable
    {
        public static Action<Ball> OnTaskCompleted;
        public static Action OnAllBallCompleted;
        public static Action OnPlayerJoined;

        private GameplayBus _gameplayBus;
        private Dictionary<Player, bool> _currentPlayers = new();
        private List<Ball> _completedBalls = new();
        private List<Ball> _allBalls = new();
        private HoleBehaviour _hole;

        private TinyMessageSubscriptionToken _ballSpawnToken;
        private TinyMessageSubscriptionToken _holeSpawnToken;

        private void OnEnable()
        {
            Subscribe();
        }
        private void OnDisable()
        {
            Unsubscribe();
        }

        
        public void Subscribe()
        {
            _ballSpawnToken = ActionDispatcher.Bind<BallSpawnAction>(OnBallSpawn);
            _holeSpawnToken = ActionDispatcher.Bind<HoleSpawnAction>(OnHoleSpawn);

            Ball.OnDispose += BallDisposed;
            OnTaskCompleted += TaskCompleted;
            OnPlayerJoined += PlayerJoined;
            OnAllBallCompleted += AllPlayerFinishedTasks;
        }

        private void OnHoleSpawn(HoleSpawnAction action)
        {
            HoleInitialize(action.holeBehaviour);
        }

        private void HoleInitialize(HoleBehaviour hole)
        {
            _hole = hole;
        }

        public void Unsubscribe()
        {
            ActionDispatcher.Unbind(_ballSpawnToken);
            ActionDispatcher.Unbind(_holeSpawnToken);
            
            Ball.OnDispose -= BallDisposed;
            OnTaskCompleted -= TaskCompleted;
            OnPlayerJoined -= PlayerJoined;
            OnAllBallCompleted -= AllPlayerFinishedTasks;
        }
      
        private void ResetTasks(GolfLogicSO _)
        {
            _allBalls.Clear();
            _completedBalls.Clear();
        }

        private void OnBallSpawn(BallSpawnAction action)
        {
            BallInitialize(action.ball);
        }

        private void BallInitialize(Ball ball)
        {
            _allBalls.Add(ball);
        }

        private void BallDisposed(Ball ball)
        {
	       _allBalls.Remove(ball); 
        } 

        private void PlayerJoined()
        {
            _currentPlayers = new Dictionary<Player, bool>();
            _completedBalls = new();
            foreach (Player player in PhotonNetwork.PlayerList)
            {
                _currentPlayers.Add(player, false);
            }
        }
        
        private Dictionary<string, bool> _currentRoomPlayers => _gameplayBus.currentInRoomPlayers;
        private void TaskCompleted(Ball ball)
        {
            
            // if (PhotonNetwork.OfflineMode)
            // {
            //     AllPlayerFinishedTasks();
            //     return;
            // }

            // var ugsId = ball.ClientPlayer.CustomProperties["ugsId"].ToString();
            // photonView.RPC("OnOpponentFinishTask", RpcTarget.All, ugsId, true);
            
            Debug.Log(ball.localPlayer.GetId() + " completed their task");
        }

        private void AllPlayerFinishedTasks()
        {
            _completedBalls.Clear();
            foreach (var ball in _allBalls)
            {
                _completedBalls.Add(ball);
            }

            _gameplayBus.currentLogic.OnAllTaskCompleted(_completedBalls, _hole);
        }

        [PunRPC]
        public void OnOpponentFinishTask(string id, bool isDone)
        {
            Debug.Log("done");
            if (!_currentRoomPlayers.TryGetValue(id, out var done)) return;

            _currentRoomPlayers[id] = isDone;

            bool isAllFinished = _currentRoomPlayers.All(x => x.Value);
            if (!isAllFinished) return;
            Debug.Log("Game Over");
            AllPlayerFinishedTasks();
        }


        public override UniTask Init(MasterManager masterManager)
        {
            MasterManager = masterManager;
            _gameplayBus = GlobalSO.GameplayBus;
            
            ResetTasks(null);
            
            return UniTask.CompletedTask;
        }

        public override void Reset()
        {
            ResetTasks(null);
        }
    }
}