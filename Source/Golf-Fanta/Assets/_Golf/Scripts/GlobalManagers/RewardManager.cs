using System;
using System.Collections.Generic;
using _Golf.Scripts.API.Models.Economy;
using _Golf.Scripts.Networking;
using _Golf.Scripts.ScriptableObjects;
using _Golf.Scripts.Tracking;
using Cysharp.Threading.Tasks;
using GolfFantaModule.Models.Economy;
using GolfGame;
using GolfGame.API;
using Newtonsoft.Json;
using Unity.Services.Economy;
using UnityEngine;

namespace _Golf.Scripts.Core
{
    public class RewardManager : BaseControllerComponent
    {
        public static Action<string, HeadToHeadMatchProperties> OnRewardWinners;
        public static Action<Dictionary<string, int>, Dictionary<GearType, int>, bool> OnCaculateResource;
        private PlayerGadget _playerGadget;
        private void OnEnable()
        {
            OnRewardWinners += RewardWinners;
            OnCaculateResource += DecreaseInventoryItemResult;
        }
        
        private void OnDisable()
        {
            OnRewardWinners -= RewardWinners;
            OnCaculateResource -= DecreaseInventoryItemResult;
        }
        
        public override UniTask Init(MasterManager masterManager)
        {
            MasterManager = masterManager;
            _playerGadget = GlobalSO.PlayerGadget;
            return UniTask.CompletedTask;
        }

        public override void Reset()
        {
        }

        private async void RewardWinners(string winners, HeadToHeadMatchProperties headToHeadMatchProperties)
        {
            HeadToHeadResultRequestModel data = new HeadToHeadResultRequestModel();
            data.headToHeadMatchProperties = headToHeadMatchProperties;
            data.configAssignmentHash = EconomyService.Instance.Configuration.GetConfigAssignmentHash();
            
            var result = await APIGameClient.Instance.GetHeadToHeadMatchResult(data);

            if (result.responseCode == APIResponseCode.Success && result.data.rewardItems != null)
            {
                var rewardItems = result.data.rewardItems;
                foreach (var item in rewardItems)
                {
                    if (item.ItemId == UGSEconomy.CoinCurrencyId || item.ItemId == UGSEconomy.GemCurrencyID)
                    {
                        TrackingManager.Instance.TrackEarnCurrency(item.ItemId, GlobalSO.GameplayBus.currentLobbyProperty.CourseInfo.Id, item.Quantity);
                    }
                }
            }
        }

        private async void DecreaseInventoryItemResult(Dictionary<string, int> consumeBalls, Dictionary<GearType, int> gearUsage, bool save = false)
        {
            var request = new ConsumeInventoryItemModel();
            
            if (consumeBalls.Count > 0)
            {
                var ballInventories = GlobalSO.InventoryItemSo.balls;
                
                foreach (var ball in consumeBalls)
                {
                    foreach (var ballConfig in ballInventories)
                    {
                        if (ballConfig.serverData.customData.id.ToUpper() == ball.Key)
                        {
                            request.ConsumeBalls.ItemIds.Add(ballConfig.serverData.id);
                            request.ConsumeBalls.Quantities.Add(ball.Value);   
                        }
                    }
                }
            }
            
            if (gearUsage != null)
            {
                var gearList = GlobalSO.InventoryItemSo.chips;
                foreach (var gearUse in gearUsage)
                {
                    foreach (var gearConfig in gearList)
                    {
                        if (gearConfig.serverData.customData.type == gearUse.Key)
                        {
                            request.ConsumeGears.ItemIds.Add(gearConfig.serverData.id);
                            request.ConsumeGears.Quantities.Add(gearUse.Value);
                            continue;
                        }
                    }
                }
            }
            
            // Club
            foreach (var club in _playerGadget.ChosenHybridClubs)
            {
                if (club != null)
                {
                    request.ConsumeClubs.ItemIds.Add(club.ItemId);
                }
            }

            foreach (var club in _playerGadget.ChosenIronClubs)
            {
                if (club != null)
                {
                    request.ConsumeClubs.ItemIds.Add(club.ItemId);
                }
            }

            foreach (var club in _playerGadget.ChosenWoodClubs)
            {
                if (club != null)
                {
                    request.ConsumeClubs.ItemIds.Add(club.ItemId);
                }
            }

            foreach (var club in _playerGadget.ChosenWedgeClubs)
            {
                if (club != null)
                {
                    request.ConsumeClubs.ItemIds.Add(club.ItemId);
                }
            }

            foreach (var club in _playerGadget.ChosenPutterClubs)
            {
                if (club != null)
                {
                    request.ConsumeClubs.ItemIds.Add(club.ItemId);
                }
            }

            if (save)
            {
                CryptoHelper.AESEncryptedText encryptedText = CryptoHelper.Encrypt(JsonConvert.SerializeObject(request), CryptoHelper.Password);

                PlayerPrefs.SetString(CryptoHelper.EncryptedTextKey0, encryptedText.EncryptedText);
                PlayerPrefs.SetString(CryptoHelper.EncryptedIV0, encryptedText.IV);
            }
            else
            {
                PlayerPrefs.SetString(CryptoHelper.EncryptedTextKey0, "Default");
                PlayerPrefs.SetString(CryptoHelper.EncryptedIV0, "Default");

                await APIGameClient.Instance.ConsumeInventoryResult(request);
            }
        }
    }
}