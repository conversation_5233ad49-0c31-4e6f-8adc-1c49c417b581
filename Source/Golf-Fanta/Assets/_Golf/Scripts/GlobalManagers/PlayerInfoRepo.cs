using System.Collections.Generic;
using System.Linq;
using _Golf.Scripts.Core;
using Cysharp.Threading.Tasks;
using GolfGame;
using GolfGame.API;
using GolfGame.API.Models.PlayerMessage;
using UnityEngine;

public class PlayerInfoRepo : BaseControllerComponent
{
    private Dictionary<string, PlayerInfo> _cachedPlayerInfo = new Dictionary<string, PlayerInfo>();

    public async UniTask<Dictionary<string, PlayerInfo>> GetPlayersInfo(List<string> playerIds, bool forceFetch = false)
    {
        var fetchIds = playerIds;
        if(!forceFetch)
            fetchIds = playerIds.Where(playerId => !_cachedPlayerInfo.ContainsKey(playerId)).ToList();
        await FetchPlayerInfo(fetchIds);
        return playerIds.ToDictionary(playerId => playerId, playerId => _cachedPlayerInfo[playerId]);
    }
        
    public override UniTask Init(MasterManager masterManager)
    {
        MasterManager = masterManager;
        return UniTask.CompletedTask;
    }

    public override void Reset()
    {
    }
        
    private async UniTask FetchPlayerInfo(List<string> playerIds)
    {
        var response = await APIGameClient.Instance.GetPlayersInfo(playerIds);
        if (response.responseCode == APIResponseCode.Fail)
        {
            Debug.LogError(response.message);
            return;
        }
            
        foreach (var (playerId, playerInfoModel) in response.data)
        {
            if (_cachedPlayerInfo.ContainsKey(playerId))
            {
                _cachedPlayerInfo[playerId].UpdateInfo(playerInfoModel);
            }
            else
            {
                _cachedPlayerInfo.Add(playerId, new PlayerInfo(playerId, "", playerInfoModel));
            }
        }
    }

    private void RegisterPlayerInfo(PlayerModel playerModel)
    {
        
    }
}