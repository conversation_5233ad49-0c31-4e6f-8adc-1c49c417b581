using _Golf.Physics.Data;
using System;
using System.Collections.Generic;
using UnityEngine;
using _Golf.Scripts.Common;
using _Golf.Scripts.UI;
using System.Threading.Tasks;
using _Golf.Scripts.Core;
using _Golf.Scripts.GlobalManagers;
using _Golf.Scripts.Networking;
using _Golf.Scripts.Networking.Photon;
using _Golf.Scripts.Networking.PlayerInfo;
using _Golf.Scripts.SceneLoader;
using _Golf.Scripts.UI.Debug;
using Cysharp.Threading.Tasks;
using GolfGame;
using GolfPhysics;
using UnityEngine.AddressableAssets;
using UnityEngine.ResourceManagement.AsyncOperations;
using UnityEngine.Rendering;

namespace GolfGame
{
    /// <summary>
    /// MasterManager is a singleton used to manage all mediators in the game.
    /// </summary>
    public class MasterManager : MonoSingleton<MasterManager>
    {
        public ResourcesManager ResourcesManager => _resourcesManager;
        [SerializeField] private ResourcesManager _resourcesManager;
        public CameraManager CameraManager => _cameraManager;
        [SerializeField] private CameraManager _cameraManager;
        public GolfTaskManager GolfTaskManager => _golfTaskManager;
        [SerializeField] private GolfTaskManager _golfTaskManager;
        public UGSEconomy UgsEconomy => _ugsEconomy;
        [SerializeField] private UGSEconomy _ugsEconomy;
        public RewardManager RewardManager => _rewardManager;
        [SerializeField] private RewardManager _rewardManager;
        public SurfaceParameterManager SurfaceParameterManager => _surfaceParameterManager;
        [SerializeField] private SurfaceParameterManager _surfaceParameterManager;
        public IAPManager IapManager => _iapManager;
        [SerializeField] private IAPManager _iapManager;
        public MultiPlayerController MultiPlayerController => _multiPlayerController;
        [SerializeField] private MultiPlayerController _multiPlayerController;
        public SinglePlayerMatchController SinglePlayerMatchController => _singlePlayerMatchController;
        [SerializeField] private SinglePlayerMatchController _singlePlayerMatchController;
        public TournamentController TournamentController => _tournamentController;
        [SerializeField] private TournamentController _tournamentController;
        public DebugMonitorUIController DebugMonitorUIController => _debugMonitorUIController;
        [SerializeField] private DebugMonitorUIController _debugMonitorUIController;
        public RegisterGameMode RegisterGameMode => _registerGameMode;
        [SerializeField] private RegisterGameMode _registerGameMode;
        public BagInventoryManager BagInventoryManager => _bagInventoryManager;
        [SerializeField] private BagInventoryManager _bagInventoryManager;
        public ShopConfigManager ShopConfigManager => _shopConfigManager;
        [SerializeField] private ShopConfigManager _shopConfigManager;
        public InventoryManager InventoryManager => _inventoryManager;
        [SerializeField] private InventoryManager _inventoryManager;
        public PlayerInfoManager PlayerInfoManager => _playerInfoManager;
        [SerializeField] private PlayerInfoManager _playerInfoManager;
        public AudioManager AudioManager => _audioManager;
        [SerializeField] private AudioManager _audioManager;
        public UIManager UIManager { get; private set; }
        [SerializeField] private AssetReferenceGameObject _uiManagerAssetRef;
        public RelationshipManager RelationshipManager => _relationshipManager;
        [SerializeField] private RelationshipManager _relationshipManager;
        public PlayerInfoRepo PlayerInfoRepo => _playerInfoRepo;
        [SerializeField] private PlayerInfoRepo _playerInfoRepo;
        public NotificationManager NotificationManager => _notificationManager;
        [SerializeField] private NotificationManager _notificationManager;
        public RedDotManager RedDotManager => _redDotManager;
        [SerializeField] private RedDotManager _redDotManager;

        [SerializeField] private bool isSanbox;

        /// <summary>
        /// List of all mediators currently in use.
        /// </summary>
        List<ModuleMediator> Mediators;

        /// <summary>
        /// A dictionary of all mediators currently in use.
        /// </summary>
        Dictionary<string, ModuleMediator> UIMediators;

        private UIScreenController _screenController;
        private UIComponentController _componentController;

        public override void Awake()
        {
            if (isSanbox)
            {
                _ = Initialize();
                _ = InitUiManager();
            }
        }

        public async Task Initialize()
        {
            await InitializeControllers();
        }

        private async UniTask InitializeControllers()
        {
            var parallelTasks = new List<UniTask>();

            if (_redDotManager != null)
                parallelTasks.Add(_redDotManager.Init(this));
                
            if (_golfTaskManager != null)
                parallelTasks.Add(_golfTaskManager.Init(this));
            if (_ugsEconomy != null)
                parallelTasks.Add(_ugsEconomy.Init(this));
            if (_rewardManager != null)
                parallelTasks.Add(_rewardManager.Init(this));
            if (_surfaceParameterManager != null)
                parallelTasks.Add(_surfaceParameterManager.Init(this));
            if (_iapManager != null)
                parallelTasks.Add(_iapManager.Init(this));
            if (_multiPlayerController != null)
                parallelTasks.Add(_multiPlayerController.Init(this));
            if (_singlePlayerMatchController != null)
                parallelTasks.Add(_singlePlayerMatchController.Init(this));
            if (_tournamentController != null)
                parallelTasks.Add(_tournamentController.Init(this));
            if (_debugMonitorUIController != null)
                parallelTasks.Add(_debugMonitorUIController.Init(this));
            if (_registerGameMode != null)
                parallelTasks.Add(_registerGameMode.Init(this));
            if (_bagInventoryManager != null)
                parallelTasks.Add(_bagInventoryManager.Init(this));
            if (_shopConfigManager != null)
                parallelTasks.Add(_shopConfigManager.Init(this));
            if (_inventoryManager != null)
                parallelTasks.Add(_inventoryManager.Init(this));
            if (_playerInfoManager != null)
                parallelTasks.Add(_playerInfoManager.Init(this));
            if (_audioManager != null)
                parallelTasks.Add(_audioManager.Init(this));
            if (_relationshipManager != null)
                parallelTasks.Add(_relationshipManager.Init(this));
            if (_playerInfoRepo != null)
                parallelTasks.Add(_playerInfoRepo.Init(this));
            if (_notificationManager != null)
                parallelTasks.Add(_notificationManager.Init(this));
            

            await UniTask.WhenAll(parallelTasks);
        }

        public void ResetControllers()
        {
            _golfTaskManager.Reset();
            _ugsEconomy.Reset();
            _rewardManager.Reset();
            _surfaceParameterManager.Reset();
        }


        /// <summary>
        /// This function is used to add a mediator to the manager.
        /// </summary>
        /// <param name="mediator"></param>
        public void AddMediator(ModuleMediator mediator)
        {
            Debug.Log("mediator:" + mediator);
            Mediators.Add(mediator);
            mediator.Init();

            // If mediator also implements the UIModuleMediator interface
            // Then add it to the UI dictionary
            if (mediator is UIModuleMediator)
            {
                // An UI mediator is added to the dictionary using its class name as key.
                // It means no duplicate UI on the scene is contextually allowed.
                if (!UIMediators.ContainsKey(mediator.GetType().Name))
                {
                    UIMediators.Add(mediator.GetType().Name, mediator);
                }
            }
        }

        /// <summary>
        /// This funtion is used to remove a mediator from a manager.
        /// </summary>
        /// <param name="mediator"></param>
        public void RemoveMediator(ModuleMediator mediator)
        {
            if (mediator == null) return;

            Mediators.Remove(mediator);
            mediator.Dispose();

            if (mediator is UIModuleMediator)
            {
                if (UIMediators.ContainsKey(mediator.GetType().Name))
                {
                    UIMediators.Remove(mediator.GetType().Name);
                }
            }
        }

        /// <summary>
        /// This function is used to open an UI using its mediator class name.
        /// </summary>
        /// <param name="signature"></param>
        public void OpenUI(string signature)
        {
            if (UIMediators.ContainsKey(signature))
            {
                ((UIModuleMediator)UIMediators[signature]).OnOpenUI();
            }
        }

        /// <summary>
        /// This function is used to close an UI using its mediator class name.
        /// </summary>
        /// <param name="signature"></param>
        public void CloseUI(string signature)
        {
            if (UIMediators.ContainsKey(signature))
            {
                ((UIModuleMediator)UIMediators[signature]).OnCloseUI();
            }
        }

        public async UniTask OnChangeScreen(EScreenEnum screenName, object[] data = null, bool force = false)
        {
            await UniTask.WaitUntil(() => _screenController != null);
            if (!force)
            {
                await _screenController.OnChangeScreenWithAnimation(screenName, data);
            }
            else
            {
                _screenController.OnChangeScreen(screenName, data);
            }
            ActionDispatcher.Dispatch(new ChangeScreenAction(screenName));
        }

        public UIComponentMediator InitUIComponent(UIComponentEnum componentName, object[] data = null)
        {
            UIComponentMediator uiComponentMediator = _componentController.InitComponent(componentName, data);
            return uiComponentMediator;
        }

        public void DestroyUIComponent(UIComponentEnum componentName)
        {
            _componentController.DestroyComponent(componentName);
        }

        public void OpenUIComponentWithoutInit(UIComponentEnum componentName, object[] data = null)
        {
            _componentController.OpenComponent(componentName, data);
        }

        public async UniTask OpenUIComponent(UIComponentEnum componentName, object[] data = null)
        {
            var alreadyInit = _componentController.InitComponent(componentName) == null;

            // UItoolkit need 1 frame to init animation
            if (!alreadyInit)
                await UniTask.Yield(PlayerLoopTiming.LastPostLateUpdate);

            await _componentController.OpenComponentAsync(componentName, data);
        }

        public void HideUIComponent(UIComponentEnum componentName, object[] data = null)
        {
            _componentController.CloseComponent(componentName, true);
        }

        public async UniTask HideUIComponentAsync(UIComponentEnum componentName)
        {
            await _componentController.CloseComponent(componentName, true);
        }

        public async UniTask HideAllComponents()
        {
            await _componentController.HideAll();
        }

        public void RegisterCacheScreen(EScreenEnum screenName)
        {
            _screenController.RegisterCachedScreen(screenName);
        }

        public void ReturnToCachedScreen()
        {
            _screenController.ReturnToCachedScreen();
        }

        public EScreenEnum GetCurrentScreen()
        {
            return _screenController.CurrentScreenEnum;
        }

        public EScreenEnum GetCachedScreen()
        {
            return _screenController.GetCachedScreen();
        }

        // SECTION: Events for mediators to subscribe to

        // BALL TRAJECTORY EVENTS
        public Action<BallMediatorManagerData> BallStartMoving;
        public Action<BallMediatorManagerData> BallMoveHitPointInWhichCameraStopWaiting;
        public Action<BallMediatorManagerData> BallMoveHitFirstRollPosition;
        public Action<BallTrajectory, AIRBORNE_TRANSITION, bool> BallGoingPutting;

        public Action ForceCameraBackToRearBallView;

        // END SECTION.

        // all of these values are default, and changed for debugging purposes only
        public int cameraDebugMode = 0;
        public int ballTrailMode = 0;
        public bool deviceMode = true;

        #region Addressables

        public async Task InitUiManager()
        {
            if (!isSanbox)
            {
                var retryAttempts = 10;
                while (UIManager == null && retryAttempts>0)
                {
                    retryAttempts--;
                    var loadHandle = Addressables.LoadAssetAsync<GameObject>(_uiManagerAssetRef);
                    await loadHandle.Task;
                    if (loadHandle.Status != AsyncOperationStatus.Succeeded) continue;
                    
                    var uiManagerObject = Instantiate(loadHandle.Result, transform);
                    UIManager = uiManagerObject.GetComponent<UIManager>();
                }
                
                if(UIManager == null)
                    Debug.LogError($"Failed to load UIManager");

                await UIManager.Init(this);
                UIManager.GetComponentInChildren<Canvas>().worldCamera = CameraManager.Instance.GetUICamera();

                Mediators = new List<ModuleMediator>();
                UIMediators = new Dictionary<string, ModuleMediator>();
                _screenController = new UIScreenController();
                _componentController = new UIComponentController();
                _componentController.Init(_screenController);
            }
            else
            {
                UIManager = GameObject.Find("UIManager").GetComponent<UIManager>();
                await UIManager.Init(this);

                Mediators = new List<ModuleMediator>();
                UIMediators = new Dictionary<string, ModuleMediator>();
                _screenController = new UIScreenController();
                _componentController = new UIComponentController();
                _componentController.Init(_screenController);
            }
        }
        #endregion

        #region Game Data
        public int CurrentBallInCamera;
        public void ChangeCurrentBallInCamera()
        {
            if (CurrentBallInCamera == 0)
            {
                CurrentBallInCamera = 1;
            }
            else if (CurrentBallInCamera == 1)
            {
                CurrentBallInCamera = 0;
            }
        }
        public bool ChangeFromOpponentToLocalSpectate(List<Ball> balls, Ball localBall)
        {
            if (balls[CurrentBallInCamera].localPlayer.GetId() != localBall.localPlayer.GetId())
            {
                // currently spectating opponent, switch back to local
                ChangeCurrentBallInCamera();
                ActionDispatcher.Dispatch(new OnChangeBallSpectateToken(false));
                return true;
            }
            else
            {
                return false;
            }
        }
        public bool ChangeFromLocalToOpponentSpectate(List<Ball> balls, Ball localBall)
        {
            if (balls[CurrentBallInCamera].localPlayer.GetId() == localBall.localPlayer.GetId())
            {
                // currently spectating local, switch to opponent
                ChangeCurrentBallInCamera();
                ActionDispatcher.Dispatch(new OnChangeBallSpectateToken(false));
                return true;
            }
            else
            {
                return false;
            }
        }

        public int destinationVerticalPivot = 0;
        public bool zoomedOutFailed = false;
        public int angleLift = 0;

        public int CalculateDestinationVerticalPivot(Vector3 destinationPosition, Vector3 ballPosition)
        {
            destinationVerticalPivot = 0;
            for (int i = 1; i < 10; i++)
            {
                int layerMask = GolfPhysics.Constant.TerrainLayerMask;
                RaycastHit hit;
                Vector3 currentEndPoint = destinationPosition + new Vector3(0f, i, 0f);
                if (UnityEngine.Physics.Raycast(ballPosition,
                    currentEndPoint - ballPosition,
                    out hit,
                    (currentEndPoint - ballPosition).magnitude,
                    layerMask
                ))
                {
                    destinationVerticalPivot = i;
                }
            }
            return destinationVerticalPivot;
        }
        #endregion
        
        public void SetRenderInterval(int value)
        {
            OnDemandRendering.renderFrameInterval = value;
        }

        public void AddToNotificationStack(NotificationActionEnum notificationAction)
        {
            var HomeScreenMediator = _screenController.GetUIScreenMediator(EScreenEnum.Home) as HomeScreenMediator;
            if (HomeScreenMediator != null)
            {
                HomeScreenMediator.NotificationStack.Push(notificationAction);
            }
            else
            {
                Debug.LogError("HomeScreenMediator is null");
            }
        }
    }
}

public abstract class BaseControllerComponent : MonoBehaviour
{
    protected MasterManager MasterManager;

    public abstract UniTask Init(MasterManager masterManager);
    
    public abstract void Reset();
}