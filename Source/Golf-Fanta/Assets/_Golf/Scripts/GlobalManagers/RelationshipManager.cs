using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using _Golf.Scripts.Common;
using _Golf.Scripts.Core;
using _Golf.Scripts.Models.DTOs;
using Cysharp.Threading.Tasks;
using GolfGame;
using GolfGame.API;
using GolfGame.API.Models.PlayerMessage;
using Unity.Services.Authentication;
using Unity.Services.Friends;
using Unity.Services.Friends.Models;
using Unity.Services.Friends.Notifications;
using UnityEngine;

public class RelationshipManager : BaseControllerComponent
{
    private IReadOnlyList<Relationship> _friendsRequest;

    public bool ForceFetchPlayerInfo = false;
    private DateTime _lastFetchTime;
    private bool _isDirty = false;
    public bool IsDirty = true;
    
    private Dictionary<string, RelatedPlayer> _cachedRelatedPlayers = new Dictionary<string, RelatedPlayer>();
    public override async UniTask Init(MasterManager masterManager)
    {
        MasterManager = masterManager;
        FriendsService.Instance.MessageReceived += OnMessageReceived;
        FriendsService.Instance.RelationshipAdded += OnAdded;
        FriendsService.Instance.RelationshipDeleted += OnDeleted;
        FriendsService.Instance.PresenceUpdated += OnPresenceUpdated;

        await GetFriends();
        CheckFriendRequest();
    }

    public override void Reset()
    {
    }

    public async UniTask<List<RelatedPlayer>> GetFriends()
    {
        return await GetRelatedPlayers(FriendsService.Instance.Friends);
    }
    
    public List<RelatedPlayer> GetCachedFriends()
    {
        return _cachedRelatedPlayers.Values.Where(player => player.RelationshipState == RelationshipState.Friend).ToList();
    }

    public async UniTask<List<RelatedPlayer>> GetSentRequestPlayers()
    {
        return await GetRelatedPlayers(FriendsService.Instance.OutgoingFriendRequests);
    }

    public async UniTask<List<RelatedPlayer>> GetReceivedRequestPlayers()
    {
        return await GetRelatedPlayers(FriendsService.Instance.IncomingFriendRequests);
    }

    public async UniTask<List<RelatedPlayer>> GetBlockPlayers()
    {
        return await GetRelatedPlayers(FriendsService.Instance.Blocks);
    }
    
    public async UniTask AddFriend(string id)
    {
        await FriendsService.Instance.AddFriendAsync(id);
        _cachedRelatedPlayers[id].SetRelationshipState(RelationshipState.SentRequest);
    }

    public async UniTask AcceptFriendRequest(string id)
    {
        await FriendsService.Instance.AddFriendAsync(id);
        _cachedRelatedPlayers[id].SetRelationshipState(RelationshipState.Friend);
        CheckFriendRequest();
    }

    public async UniTask CancelIncomingRequest(string id)
    {
        await FriendsService.Instance.DeleteIncomingFriendRequestAsync(id);
        _cachedRelatedPlayers[id].SetRelationshipState(RelationshipState.None);
        CheckFriendRequest();
    }

    private void CheckFriendRequest()
    {
        _friendsRequest = FriendsService.Instance.IncomingFriendRequests;
        ActionDispatcher.Dispatch(new UpdateFriendRequestCount(_friendsRequest.Count, RedDotType.FriendAddNew));
    }
    
    public async UniTask CancelOutgoingRequest(string id)
    {
        await FriendsService.Instance.DeleteOutgoingFriendRequestAsync(id);
        _cachedRelatedPlayers[id].RelationshipState = RelationshipState.None;
    }
    
    public async UniTask DeleteFriend(string id)
    {
        await FriendsService.Instance.DeleteFriendAsync(id);
        _cachedRelatedPlayers[id].RelationshipState = RelationshipState.None;
    }

    public async Task<APIServerResponse<SendP2PGiftResponse>> SendGift(string id)
    {
        var respone = await APIGameClient.Instance.SendP2PGift(id);

        if (respone.responseCode == APIResponseCode.Success)
        {
            var message = new InternalPlayerMessage
            {
                Id = id
            };
            FriendsService.Instance.MessageAsync(id, message).AsUniTask().Forget();
        }
        return respone;
    }

    public async UniTask<List<RelatedPlayer>> GetPlayers(List<string> playerIds)
    {
        var result = new List<RelatedPlayer>();
        try
        {
            var neededFetchPlayerIds = playerIds.Where(playerId =>
                    !_cachedRelatedPlayers.ContainsKey(playerId) && playerId != AuthenticationService.Instance.PlayerId)
                .ToList();
            var playerNames = (await APIGameClient.Instance.GetPlayerNames(neededFetchPlayerIds)).data;
            if (playerNames == null) return result;
            var playerInfos = await MasterManager.Instance.PlayerInfoRepo.GetPlayersInfo(neededFetchPlayerIds);

            foreach (var playerId in playerIds)
            {
                if (_cachedRelatedPlayers.TryGetValue(playerId, out var player))
                {
                    result.Add(player);
                }
                else
                {
                    var playerInfo = playerInfos[playerId];
                    playerInfo.UpdateAuthenticateInfo(playerId, playerNames[playerId]);
                    player = new RelatedPlayer
                    {
                        PlayerInfo = playerInfo,
                        RelationshipState = RelationshipState.None,
                        Availability = UserAvailability.Unknown,
                        LastSeen = default,
                    };
                    _cachedRelatedPlayers.Add(playerId, player);
                    result.Add(player);
                }
            }

            return result;
        }
        catch (Exception e)
        {
            Debug.LogError(e);
            return result;
        }
    }

    public async UniTask<List<RelatedPlayer>> SearchPlayerByName(string playerName)
    {
        var searchPlayerResponse =  await APIGameClient.Instance.SearchPlayerByName(playerName);
        var playerDTOs = searchPlayerResponse.data;
        var result = playerDTOs.Select(playerEntity => new RelatedPlayer(playerEntity)).ToList();
        foreach (var relatedPlayer in result)
        {
            _cachedRelatedPlayers[relatedPlayer.PlayerInfo.Id] = relatedPlayer;
        }
        return result;
    }

    public void SetDirty()
    {
        
    }
    
    // private async UniTask<List<RelatedPlayer>> GetRelatedPlayers(IReadOnlyCollection<Relationship> relationships)
    // {
    //     var playerInfos =
    //         await MasterManager.Instance.PlayerInfoRepo.GetPlayersInfo(relationships.Select(relationship => relationship.Member.Id)
    //             .ToList());
    //     return relationships.Select(relationship =>
    //     {
    //         var relationshipState = relationship.Type switch
    //         {
    //             RelationshipType.Friend => RelationshipState.Friend,
    //             RelationshipType.Block => RelationshipState.Block,
    //             RelationshipType.FriendRequest => relationship.Member.Role == MemberRole.Source
    //                 ? RelationshipState.ReceivedRequest
    //                 : RelationshipState.SentRequest,
    //             _ => RelationshipState.None
    //         };
    //
    //         var playerInfo = playerInfos[relationship.Member.Id];
    //         playerInfo.UpdateAuthenticateInfo(relationship.Member.Id, relationship.Member.Profile.Name);
    //         RelatedPlayer result;
    //         if (_cachedRelatedPlayers.TryGetValue(relationship.Member.Id, out var player))
    //         {
    //             player.PlayerInfo = playerInfo;
    //             player.RelationshipState = relationshipState;
    //             player.Availability = relationship.Member.Presence?.Availability switch
    //             {
    //                 Availability.Online => UserAvailability.Online,
    //                 Availability.Offline => UserAvailability.Offline,
    //                 Availability.Busy => UserAvailability.InMatch,
    //                 _ => UserAvailability.Unknown
    //             };
    //             player.LastSeen = relationship.Member.Presence?.LastSeen ?? DateTime.Now;
    //             result = player;
    //         }
    //         else
    //         {
    //             result = new RelatedPlayer
    //             {
    //                 PlayerInfo = playerInfo,
    //                 RelationshipState = relationshipState,
    //                 Availability = relationship.Member.Presence?.Availability switch
    //                 {
    //                     Availability.Online => UserAvailability.Online,
    //                     Availability.Offline => UserAvailability.Offline,
    //                     Availability.Busy => UserAvailability.InMatch,
    //                     _ => UserAvailability.Unknown
    //                 },
    //                 LastSeen = relationship.Member.Presence?.LastSeen ?? DateTime.Now
    //             };
    //             _cachedRelatedPlayers.Add(playerInfo.Id, result);
    //         }
    //
    //         return result;
    //     }).ToList();
    // }
    
    private async UniTask<List<RelatedPlayer>> GetRelatedPlayers(IReadOnlyCollection<Relationship> relationships)
    {
        if (_isDirty || DateTime.Now-_lastFetchTime > TimeSpan.FromMinutes(5))
        {
            _cachedRelatedPlayers = await APIGameClient.Instance.GetRelationships();
        }
        
        var playerInfos =
            await MasterManager.Instance.PlayerInfoRepo.GetPlayersInfo(relationships.Select(relationship => relationship.Member.Id)
                .ToList());
        return relationships.Select(relationship =>
        {
            var relationshipState = relationship.Type switch
            {
                RelationshipType.Friend => RelationshipState.Friend,
                RelationshipType.Block => RelationshipState.Block,
                RelationshipType.FriendRequest => relationship.Member.Role == MemberRole.Source
                    ? RelationshipState.ReceivedRequest
                    : RelationshipState.SentRequest,
                _ => RelationshipState.None
            };

            var playerInfo = playerInfos[relationship.Member.Id];
            playerInfo.UpdateAuthenticateInfo(relationship.Member.Id, relationship.Member.Profile.Name);
            RelatedPlayer result;
            if (_cachedRelatedPlayers.TryGetValue(relationship.Member.Id, out var player))
            {
                player.PlayerInfo = playerInfo;
                player.RelationshipState = relationshipState;
                player.Availability = relationship.Member.Presence?.Availability switch
                {
                    Availability.Online => UserAvailability.Online,
                    Availability.Offline => UserAvailability.Offline,
                    Availability.Busy => UserAvailability.InMatch,
                    _ => UserAvailability.Unknown
                };
                player.LastSeen = relationship.Member.Presence?.LastSeen ?? DateTime.Now;
                result = player;
            }
            else
            {
                result = new RelatedPlayer
                {
                    PlayerInfo = playerInfo,
                    RelationshipState = relationshipState,
                    Availability = relationship.Member.Presence?.Availability switch
                    {
                        Availability.Online => UserAvailability.Online,
                        Availability.Offline => UserAvailability.Offline,
                        Availability.Busy => UserAvailability.InMatch,
                        _ => UserAvailability.Unknown
                    },
                    LastSeen = relationship.Member.Presence?.LastSeen ?? DateTime.Now
                };
                _cachedRelatedPlayers.Add(playerInfo.Id, result);
            }

            return result;
        }).ToList();
    }

    private void OnPresenceUpdated(IPresenceUpdatedEvent presenceUpdatedEvent)
    {
        if (!_cachedRelatedPlayers.TryGetValue(presenceUpdatedEvent.ID, out var player)) return;
        var availability = presenceUpdatedEvent.Presence?.Availability switch
        {
            Availability.Online => UserAvailability.Online,
            Availability.Offline => UserAvailability.Offline,
            Availability.Busy => UserAvailability.InMatch,
            _ => UserAvailability.Unknown
        };
        
        player.SetAvailability(availability);
    }

    private void OnMessageReceived(IMessageReceivedEvent messageEvent)
    {
        ActionDispatcher.Dispatch(new FetchNotificationAction());
    }

    private void OnAdded(IRelationshipAddedEvent addedEvent)
    {
        OnAddedAsync(addedEvent);
    }

    private async void OnAddedAsync(IRelationshipAddedEvent addedEvent)
    {
        switch (addedEvent.Relationship.Type)
        {
            case RelationshipType.Friend:
                var member = addedEvent.Relationship.Member;
                _cachedRelatedPlayers[member.Id]?.SetRelationshipState(RelationshipState.Friend);
                break;
            case RelationshipType.FriendRequest:
                if (_cachedRelatedPlayers.TryGetValue(addedEvent.Relationship.Member.Id, out var player))
                {
                    player.SetRelationshipState(RelationshipState.ReceivedRequest);
                }

                await GetFriends();
                break;
        }

        CheckFriendRequest();
    }
    
    private void OnDeleted(IRelationshipDeletedEvent deletedEvent)
    {
        var member = deletedEvent.Relationship.Member;
        _cachedRelatedPlayers[member.Id].RelationshipState = RelationshipState.None;
    }
}

public class RelatedPlayer
{
    public event Action<UserAvailability> OnPresenceUpdated;
    public event Action<RelationshipState> OnRelationshipUpdated;
    public _Golf.Scripts.Core.PlayerInfo PlayerInfo;
    public RelationshipState RelationshipState;
    public UserAvailability Availability;
    public DateTime LastSeen;
    public bool RewardSent;

    public RelatedPlayer() { }

    public RelatedPlayer(PlayerDTO playerDto)
    {
        PlayerInfo = new _Golf.Scripts.Core.PlayerInfo(playerDto);
        RelationshipState = RelationshipState.None;
        Availability = UserAvailability.Unknown;
        LastSeen = default;
    }

    ~RelatedPlayer()
    {
        OnPresenceUpdated = null;
    }

    public void SetAvailability(UserAvailability availability)
    {
        Availability = availability;
        OnPresenceUpdated?.Invoke(availability);
    }

    public void SetRelationshipState(RelationshipState relationshipState)
    {
        RelationshipState = relationshipState;
        OnRelationshipUpdated?.Invoke(RelationshipState);
    }
}

public class UpdateFriendRequestCount : ActionBase
{
    public int Count;
    public RedDotType RedDotType;

    public UpdateFriendRequestCount(int count, RedDotType redDotType)
    {
        Count = count;
        RedDotType = redDotType;
    }
}

public class InternalPlayerMessage
{
    public string Id;
}



public enum RelationshipState
{
    None,
    Friend,
    SentRequest,
    ReceivedRequest,
    Block
}