using System;
using System.Collections;
using System.Threading.Tasks;
using _Golf.Scripts.Common;
using _Golf.Scripts.GlobalManagers;
using _Golf.Scripts.UI;
using Cysharp.Threading.Tasks;
using GolfGame;
using TinyMessenger;
using UnityEngine;
using UnityEngine.AddressableAssets;
using UnityEngine.ResourceManagement.AsyncOperations;
using UnityEngine.ResourceManagement.ResourceProviders;
using UnityEngine.SceneManagement;
namespace _Golf.Scripts.SceneLoader
{
	public struct SceneGuidHandler
	{
		public string guid;
		public AsyncOperationHandle<SceneInstance> handle;
	}
	public class SceneLoader : MonoBehaviour
	{
		private static SceneLoader _instance;
		public static SceneLoader Instance => _instance;

		public static Action<string, Action<float>> OnRequestSingleLoadMap;
		public static Action<SceneAssetReference> OnRequestLoadTitleScene;
		public static Action OnSceneLoaded;
		public static Action<AsyncOperationHandle> OnStartLoadingScene;
#if UNITY_EDITOR
		public static Action<string> OnLoadFromEditor;
#endif

		[SerializeField] private SceneAssetReference managerScene;
		private AsyncOperationHandle<SceneInstance> managerOperationHandle;
		private SceneInstance managerSceneInstance;

		private SceneGuidHandler sceneToLoad;
		private SceneGuidHandler currentLoadedScene;

		private float _transitionTime = 0.5f;
		private SceneGuidHandler _cachedPreloadSceneHandler;
		private void Awake()
		{
			if (_instance == null)
			{
				_instance = this;
				DontDestroyOnLoad(_instance);
			}
			else
				Destroy(this);
		}

		private void OnEnable()
		{
			OnRequestSingleLoadMap += OnMapLoadRequested;
			OnRequestLoadTitleScene += LoadTitleScene;
#if UNITY_EDITOR
			OnLoadFromEditor += Editor_OnLoadSceneRequested;
#endif
		}

		private void OnDisable()
		{
			OnRequestSingleLoadMap -= OnMapLoadRequested;
			OnRequestLoadTitleScene -= LoadTitleScene;
#if UNITY_EDITOR
			OnLoadFromEditor -= Editor_OnLoadSceneRequested;
#endif
		}

		private void Start()
		{

		}

		public void PreloadSceneAsync(string guid)
		{
			_cachedPreloadSceneHandler = new SceneGuidHandler();
			_cachedPreloadSceneHandler.handle = Addressables.LoadSceneAsync(managerScene, LoadSceneMode.Additive, false, 0);
			_cachedPreloadSceneHandler.guid = guid;
		}

		public async UniTask LoadSceneAsync(string guid)
		{
			await GlobalFadeController.Instance.FadeIn();
			await UnloadPreviousSceneAsync();

			var scenePreloaded = guid == _cachedPreloadSceneHandler.guid
								 && _cachedPreloadSceneHandler.handle.Status == AsyncOperationStatus.Succeeded;
			if (scenePreloaded)
			{
				currentLoadedScene = _cachedPreloadSceneHandler;
				await currentLoadedScene.handle.Result.ActivateAsync();
			}
			else
			{
				await LoadNewSceneAsync(guid);
			}

			SceneManager.SetActiveScene(currentLoadedScene.handle.Result.Scene);
			GlobalFadeController.Instance.FadeOut();
		}

		private void LoadTitleScene(SceneAssetReference titleScene)
		{
			//Todo ?

			MasterManager.Instance.SetRenderInterval(1);
			GlobalFadeController.Instance.FadeIn();
			sceneToLoad.guid = titleScene.AssetGUID;
			if (managerSceneInstance.Scene.IsValid())
				Addressables.UnloadSceneAsync(managerOperationHandle);

			StartCoroutine(UnloadPreviousScene());
		}


		private void OnMapLoadRequested(string guid, Action<float> loadingProgress = null)
		{
			StartCoroutine(LoadScene(guid, loadingProgress));
		}

		private IEnumerator LoadScene(string guid, Action<float> loadingProgress = null)
		{
			sceneToLoad.guid = guid;
			if (!managerSceneInstance.Scene.IsValid())
			{
				Debug.Log("Loading gameplay managers scene");
				managerOperationHandle =
					Addressables.LoadSceneAsync(managerScene, LoadSceneMode.Additive);
				StartCoroutine(TrackLoadingProgress(managerOperationHandle, loadingProgress));
			}
			else
			{
				StartCoroutine(UnloadPreviousScene());
			}
			yield break;
		}
		private IEnumerator TrackLoadingProgress(AsyncOperationHandle<SceneInstance> handle, Action<float> loadingProgress)
		{
			loadingProgress?.Invoke(0f);
			yield return new WaitForSeconds(0.2f);

			while (!handle.IsDone)
			{
				loadingProgress?.Invoke(handle.PercentComplete);
				yield return null;
			}

			loadingProgress?.Invoke(1f);
			yield return new WaitForSeconds(0.2f);
			GlobalFadeController.Instance.FadeIn();
			OnGameplayManagersLoaded(handle);
			MasterManager.Instance.OnChangeScreen(EScreenEnum.Empty);
		}

		private void OnGameplayManagersLoaded(AsyncOperationHandle<SceneInstance> obj)
		{
			managerSceneInstance = managerOperationHandle.Result;
			StartCoroutine(UnloadPreviousScene());
		}

		private async UniTask UnloadPreviousSceneAsync()
		{
			if (currentLoadedScene.handle.IsValid())
			{
				var handle = Addressables.UnloadSceneAsync(currentLoadedScene.handle);
				await handle;
				Resources.UnloadUnusedAssets();
			}
		}

		private IEnumerator UnloadPreviousScene()
		{
			LoadingUIContainer loadingUIContainer = new LoadingUIContainer();
			loadingUIContainer.Mode = LoadingMode.NoLogo;

			MasterManager.Instance.OpenUIComponent(UIComponentEnum.LoadingComponentUI,
				new object[]
				{
					loadingUIContainer
				}).Forget();

			loadingUIContainer.OnProgress?.Invoke(0f);

			if (currentLoadedScene.handle.IsValid())
			{
				var handle = Addressables.UnloadSceneAsync(currentLoadedScene.handle);
				handle.Completed += (AsyncOperationHandle<SceneInstance> a) => { Resources.UnloadUnusedAssets(); };
			}

			loadingUIContainer.OnProgress?.Invoke(1f);
			yield return new WaitForSeconds(_transitionTime);
			LoadNewScene();
			yield break;
		}

		private async UniTask LoadNewSceneAsync(string guid)
		{
			var loadingOperationHandle = Addressables.LoadSceneAsync(guid, LoadSceneMode.Additive, true, 0);
			OnStartLoadingScene?.Invoke(loadingOperationHandle);
			await loadingOperationHandle;

			currentLoadedScene = new SceneGuidHandler
			{
				guid = guid,
				handle = loadingOperationHandle
			};

			OnSceneLoaded?.Invoke();
			GlobalFadeController.Instance.FadeOut();
		}

		private void LoadNewScene()
		{
			var loadingOperationHandle = Addressables.LoadSceneAsync(sceneToLoad.guid, LoadSceneMode.Additive, true, 0);
			sceneToLoad.handle = loadingOperationHandle;
			OnStartLoadingScene?.Invoke(loadingOperationHandle);
			loadingOperationHandle.Completed += OnNewSceneLoaded;
		}
		private void OnNewSceneLoaded(AsyncOperationHandle<SceneInstance> handle)
		{
			currentLoadedScene = sceneToLoad;

			var activationOperation = handle.Result.ActivateAsync();
			activationOperation.completed += op =>
			{
				// Set as active scene after activation is complete
				SceneManager.SetActiveScene(currentLoadedScene.handle.Result.Scene);
				OnSceneLoaded?.Invoke();

				// Only fade out after everything is ready
				GlobalFadeController.Instance.FadeOut();
				MasterManager.Instance.HideUIComponent(UIComponentEnum.LoadingComponentUI);
			};

		}

#if UNITY_EDITOR
		private void Editor_OnLoadSceneRequested(string guid)
		{
			StartCoroutine(Editor_CoOnLoadSceneRequested(guid));
		}

		private IEnumerator Editor_CoOnLoadSceneRequested(string guid)
		{
			sceneToLoad.guid = guid;
			yield return CoLoadGameplayManagerSceneIfNotLoaded();
		}
#endif

		private IEnumerator CoLoadGameplayManagerSceneIfNotLoaded()
		{
			if (managerSceneInstance.Scene.isLoaded) yield break;
			managerOperationHandle =
				managerScene.LoadSceneAsync(LoadSceneMode.Additive);
			yield return managerOperationHandle;
			if (managerOperationHandle.Result.Scene.IsValid() == false)
			{
				Debug.LogError("Failed to load Gameplay Manager Scene");
				yield break;
			}

			managerSceneInstance = managerOperationHandle.Result;
		}
	}
}