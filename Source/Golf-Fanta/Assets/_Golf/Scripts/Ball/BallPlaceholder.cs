using _Golf.Scripts.Common;
using _Golf.Scripts.Common.Helper;
using _Golf.Scripts.ScriptableObjects;
using _Golf.Scripts.ScriptableObjects.Item;
using GolfGame;
using UnityEngine;
using UnityEngine.Rendering.Universal;

namespace _Golf.Scripts.Core.CourseController
{
	public class BallPlaceholder : MonoBehaviour
	{
		private const float TEE_HEIGHT = 0.0127f; //0.5 inch tee
		
		[SerializeField] private GameObject _ballWrapperPrefab;
		[SerializeField] private GameObject _teePrefab;

		[SerializeField] private GameObject _sandboxBallPrefab;
		[SerializeField] private GameObject _sandboxTrailPrefab;

        private bool _isSandbox = false;

        private InventoryItemsSO _inventoryItems;

		private Vector3 _cachedStartingPosition;

        private void Start()
		{
			_inventoryItems = GlobalSO.InventoryItemSo;
			
			ActionDispatcher.Dispatch(new BallPlaceHolderSpawnAction(this));
		}

		public void SetSandbox(bool value)
		{
			this._isSandbox = value;
		}

		private Ball ConfigureBall(string id)
		{
			Vector3 spawnPos = _cachedStartingPosition;

            int layerMask = GolfPhysics.Constant.TerrainLayerMask;
            RaycastHit hit;
			bool isHit = false;
			if (UnityEngine.Physics.Raycast(_cachedStartingPosition, Vector3.down, out hit, Mathf.Infinity, layerMask))
			{
				isHit = true;
			}
			else if (UnityEngine.Physics.Raycast(_cachedStartingPosition, Vector3.up, out hit, Mathf.Infinity, layerMask))
			{
				isHit = true;
			}
			if (isHit)
			{
				spawnPos = hit.point;
			}
			
			SetUpTee(spawnPos);
			
			Vector3 ballPosition = new Vector3(spawnPos.x, (float)(spawnPos.y + GolfPhysics.Constant.radiusOfGolfBall_Metric) + TEE_HEIGHT, spawnPos.z);
			Ball ball = Instantiate(_ballWrapperPrefab, ballPosition, transform.rotation).GetComponent<Ball>();
			ball.SetTeePos(spawnPos);
			ConfigureModel(id, ball);
			ball.transform.SetParent(transform);
			return ball;
		}

		public void ConfigureTrail(string id, Ball ball)
		{
			ball.DeleteTrail();
			
			BallSO ballSO = _inventoryItems.getBallsDict()[id];
			GameObject trailPrefab = _isSandbox ? _sandboxTrailPrefab : ballSO.trail.TrailModel;
            GameObject trailModel = Instantiate(trailPrefab, Vector3.zero, transform.rotation);

            var ballTrailContainer = ball.GetTrailContainer();
			trailModel.transform.SetParent(ballTrailContainer.transform);
			trailModel.transform.localPosition = new Vector3(0, 0, 0);

			ball.trail = trailModel.GetComponent<BallTrailWrapper>();
		}

		public void ConfigureTrail(BallTrailSO trail, Ball ball)
		{
            ball.DeleteTrail();
            GameObject trailModel = Instantiate(trail.TrailModel, Vector3.zero, transform.rotation);

            var ballTrailContainer = ball.GetTrailContainer();
            trailModel.transform.SetParent(ballTrailContainer.transform);
            trailModel.transform.localPosition = new Vector3(0, 0, 0);

            ball.trail = trailModel.GetComponent<BallTrailWrapper>();
        }

		public void ConfigureModel(string id, Ball ball)
		{
			ball.ClearModel();
			GameObject ballModel = Instantiate(_isSandbox ? _sandboxBallPrefab : _inventoryItems.getBallsDict()[id].BallModel, new Vector3(0, 0, 0), transform.rotation);
			var ballRenderContainer = ball.GetBallContainer();
			ballModel.transform.SetParent(ballRenderContainer.transform);
            ballModel.transform.localPosition = new Vector3(0, 0, 0);

			ballModel.layer = LayerMask.NameToLayer("GOLFBALL");
            var children = ballModel.GetComponentsInChildren<Transform>(includeInactive: true);
            foreach (var child in children)
            {
                child.gameObject.layer = LayerMask.NameToLayer("GOLFBALL");
            }
			ball.CalculateBallRadiusBasedOnMesh();

            ConfigureTrail(id, ball);
        }

		public Ball ConfigureBall(LocalPlayer localPlayer, string id)
		{
			var ball = ConfigureBall(id);
			ball.SetLocalPlayer(localPlayer);
			return ball;
		}

		public void SetStartPosition(Vector3 position)
		{
			_cachedStartingPosition = position;
		}

		private void SetUpTee(Vector3 spawnPosition)
		{
			var teeTransform = Instantiate(_teePrefab, spawnPosition.OffsetY(-TEE_HEIGHT), Quaternion.identity, transform).transform;
			var teeShadow = teeTransform.GetComponentInChildren<DecalProjector>();
			var teeShadowTransform = teeShadow.transform;
			var sunLightTransform = RenderSettings.sun.transform;
			var angle = Vector3.Angle(Vector3.down, sunLightTransform.forward);
			var shadowLength = //(ball's height * sin(angle) - ball's radius) / cos(angle)
				((TEE_HEIGHT + (float)GolfPhysics.Constant.radiusOfGolfBall_Metric)
				 * Mathf.Sin(angle * Mathf.Deg2Rad)
				 - (float)GolfPhysics.Constant.radiusOfGolfBall_Metric)
				/ Mathf.Cos(angle * Mathf.Deg2Rad);
			teeShadow.size = teeShadow.size.SetY(shadowLength);
			teeShadow.pivot = teeShadow.pivot.SetY(shadowLength / 2);
			teeShadowTransform.rotation = Quaternion.Euler(90f, sunLightTransform.eulerAngles.y, 0f);
		}
	}
}