using System.Threading.Tasks;
using _Golf.Scripts.Common;
using _Golf.Scripts.GlobalManagers;
using _Golf.Scripts.Networking;
using _Golf.Scripts.SceneLoader;
using _Golf.Scripts.ScriptableObjects;
using _Golf.Scripts.Tracking;
using _Golf.Scripts.UI;
using Cysharp.Threading.Tasks;
using GolfGame;
using GolfGame.API;
using GolfPhysics;
using Unity.Services.Authentication;
using Unity.Services.Core;
using Unity.Services.Core.Environments;
using Unity.Services.Economy;
using Unity.Services.Friends;
using Unity.Services.Friends.Models;
using UnityEngine;
using UnityEngine.AddressableAssets;
using UnityEngine.ResourceManagement.AsyncOperations;
using UnityEngine.SceneManagement;

public class AppInitializeController : MonoBehaviour
{
    // [SerializeField] private UGSAuthenticator _ugsAuthenticator;
    [SerializeField] private UGSRemoteConfig _ugsRemoteConfig;
    [SerializeField] private AdsManager _adsManager;
    [SerializeField] private LoadingHandler _loadingHandler;
    [SerializeField] private SceneAssetReference _globalManagerScene;
    [SerializeField] private SceneAssetReference _titleScene;
    [SerializeField] private TextAsset _environmentConfig;
    public const int DelayTime = 2000;

    private void Awake()
    {
        Init();
    }

    private async void Init()
    {
        Application.targetFrameRate = 60;
        await InitUgs();
        
        var signInSuccessfully = UGSAuthenticator.Instance.IsSignedIn();

        if (!signInSuccessfully)
            signInSuccessfully = await UGSAuthenticator.Instance.SignIn();


        if (signInSuccessfully)
        {
            APIGameClient.Instance.Initialize();

            var session = await APIGameClient.Instance.RegisterSession();
            GlobalSO.SessionSO.SetSession(session.data);
            
            string playerName = AuthenticationService.Instance.PlayerName;
            var initPlayerTask = InitPlayer(playerName);
            
            var economySyncConfigTask = EconomyService.Instance.Configuration.SyncConfigurationAsync();
            var fetchConfigTask = _ugsRemoteConfig.FetchConfigs();
            var initFriendServiceTask = InitFriendService();
            await Task.WhenAll(initPlayerTask, economySyncConfigTask, initFriendServiceTask, fetchConfigTask);

            await _loadingHandler.CheckAndDownloadAssets();
            
            await LocalizationManager.Instance.Init();
            
            await LoadManagerScene();
            
            await MasterManager.Instance.InitUiManager();
            
            var loadingUIContainer = new LoadingUIContainer();
            
            await MasterManager.Instance.OpenUIComponent(UIComponentEnum.LoadingComponentUI, new object[]{loadingUIContainer});

            await APIGameClient.Instance.CheckDbVersionAndMigrate();

            await UGSController.Instance.InitialGame(loadingUIContainer.OnProgress);

            await MasterManager.Instance.Initialize();

            GlobalSO.PlayerGadget.InitGadgetData();
            
            _adsManager.Init();
            
            ActionDispatcher.Dispatch(new RequestReleaseFirstScenes());
            
            SceneLoader.OnRequestLoadTitleScene.Invoke(_titleScene);
        }
    }

    private async UniTask InitUgs()
    {
        await SetUpEnvironment();
        TrackingManager.Instance.Init();
    }
    
    private async Task SetUpEnvironment()
    {
        string environmentId = "";
        if (PlayerPrefs.HasKey(Constant.Environment_Key))
        {
            environmentId = PlayerPrefs.GetString(Constant.Environment_Key);
        }
        else
        {
            #if UNITY_EDITOR
                environmentId = Constant.Environment_Dev;   
            #else
                var config = _environmentConfig.text;
                if(!string.IsNullOrEmpty(config))
                {
                    environmentId = _environmentConfig.text.Trim();
                }
                else
                {
                    environmentId = Constant.Environment_QA;   
                }
            #endif
            
            PlayerPrefs.SetString(Constant.Environment_Key, environmentId);
        }
        environmentId = Constant.Environment_QA;   

        GlobalSO.EnvironmentVariables.EnvironmentName = environmentId;
        InitializationOptions options = new InitializationOptions()
            .SetEnvironmentName(environmentId);

        await UnityServices.InitializeAsync(options);
        Debug.Log("Unity Game Services initialized in environment: " + environmentId);
    }
    
    private async Task LoadManagerScene()
    {
        var handle = Addressables.LoadSceneAsync(_globalManagerScene, LoadSceneMode.Additive);
        await handle;
        if(handle.Status == AsyncOperationStatus.Succeeded)
            Debug.Log($"ManagerSceneLoaded Successfully");
    }

    private async Task InitPlayer(string playerName)
    {
        await APIGameClient.Instance.CheckAndInitializeNewPlayer();
        if (string.IsNullOrEmpty(playerName))
        {
            //if no cached player name, it means it's a new player
            UnityEngine.Debug.Log("Player name is empty. Generating guest name.");
            await AuthenticationService.Instance.GetPlayerNameAsync(false);
            GlobalSO.PlayerInfoSO.UpdateAuthenticateInfo();
        }
    }

    private async Task InitFriendService()
    {
        await FriendsService.Instance.InitializeAsync();

        GlobalSO.PlayerInfoSO.UpdateAvailability(UserAvailability.Online);
    }
}
