using System.Collections.Generic;
using _Golf.Scripts.Lobby;
using UnityEngine;
namespace _Golf.Scripts.ScriptableObjects.HeadToHead
{
    public class HeadToHeadProperties : ScriptableObject
    {
        [field: SerializeField] public List<LobbyProperty> Lobbies { get; private set; } = new();

        public void SetLobbiesData(List<LobbyProperty> lobbies)
        {
            Lobbies = lobbies;
        }
    }
}