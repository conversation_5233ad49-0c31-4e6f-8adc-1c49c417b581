using System.Collections;
using System.Collections.Generic;
using _Golf.Physics.Data;
using _Golf.Scripts.CirclePoint;
using _Golf.Scripts.Common;
using _Golf.Scripts.Core.CourseController;
using _Golf.Scripts.GameLogic;
using _Golf.Scripts.Lobby;
using _Golf.Scripts.Networking.Photon.Lobby;
using _Golf.Scripts.ScriptableObjects;
using _Golf.Scripts.UI;
using GolfFantaModule.Models.Economy;
using GolfGame;
using Newtonsoft.Json;
using TinyMessenger;
using UnityEngine;
using UnityEngine.Rendering.Universal;
namespace _Golf.Scripts.Core
{
	[CreateAssetMenu(menuName = "ScriptableObjects/Logic/SinglePlayerNormal", fileName = "SinglePlayerNormal")]
	public class SinglePlayerNormalLogicSO : GolfLogicSO<SinglePlayerNormalLogic> { }

	public class SinglePlayerNormalLogic : BaseGolfLogic
	{
		public new SinglePlayerNormalLogicSO logicData => (SinglePlayerNormalLogicSO)_logicData;
		private TinyMessageSubscriptionToken _gameStartToken;
		private NewGameManager _manager;
		private GameplayBus _bus;

        #region gear usage

        #endregion

        public override void OnInitialize(GameplayBus bus, NewGameManager manager)
		{
			_manager = manager;
			_bus = bus;

            InitializeGear();

            _gameStartToken = ActionDispatcher.Bind<ReadyToPlayAction>(OnGameStartToken);
		}

		private void OnGameStartToken(ReadyToPlayAction obj)
		{
            _ = MasterManager.Instance.OnChangeScreen(EScreenEnum.GameplayScreen, null);
		}
		
        public override void OnGameStart()
        {

        }

        public override void OnEndGame()
        {
            throw new System.NotImplementedException();
        }

        public override void OnOtherPlayerReadyForShot(LocalPlayer player)
        {
	        throw new System.NotImplementedException();
        }

        public override void OnSelfTakeShot(LocalPlayer self, Ball ball, GolfShotData input, BallTrajectory trajectory)
        {
            Debug.Log("On self take shot");

            LocalPlayer player = GlobalSO.GameplayBus.localPlayer;
            player.SetStrokeCount(player.GetStrokeCount() + 1);

            AddGearUsage(input.GearData);
            AddOneUseConsumedOnHitGearUsage(input.GearData, isLocal: true);
            localUnchangeableUponPickGear.Clear(); localUnchangeableUponPickGear = new Dictionary<GearType, GearData>();
        }

        public override void OnOtherPlayerTakeShot(LocalPlayer player)
        {
	        throw new System.NotImplementedException();
        }

        public override void BotTakeShot(LocalPlayer bot)
        {
	        throw new System.NotImplementedException();
        }

        public override void OnBallLanded(Ball ball, BallTrajectory trajectory)
		{
			if (!ball.IsBallInHole)
			{
                ActionDispatcher.Dispatch(new PrepShotAction(ball));
                _manager.StartCoroutine(CinematicsTilNextShot(ball, trajectory));
            }
			else
			{
				ActionDispatcher.Dispatch(new CameraOrbitHoleToken(ball, trajectory));
			}
		}

        private IEnumerator CinematicsTilNextShot(Ball ball, BallTrajectory trajectory)
        {
            yield return new WaitForSeconds(3f);
            ActionDispatcher.Dispatch(new MoveCameraForNextShotAction(ball, trajectory));
            yield return new WaitForSeconds(1f);
            ActionDispatcher.Dispatch(new ContinueShotAction());
        }

        public override bool IsWon(Vector3 ballPos, Vector3 holePos)
		{
			return true;
		}
		public override void OnAllTaskCompleted(List<Ball> balls, HoleBehaviour hole)
		{
			ResultManager.OnShowSingleplayerResult?.Invoke(balls[0]);
		}
		public override void SetGamePlayLogicParam(GameLogicParam param) { }
		public override void OnSelfReadyForShot(LocalPlayer player)
		{
			throw new System.NotImplementedException();
		}

		public override void OnExit()
        {
            base.OnExit();
            _bus.localLobby.SetLocalLobbyState(LobbyState.Lobby);
            ActionDispatcher.Unbind(_gameStartToken);
            RegisterStageScreen();
            _manager.ReturnToTitle();
        }
		public override void OnBallFinished(Ball ball)
		{
			GolfTaskManager.OnAllBallCompleted?.Invoke();
		}

        public override void OnInHoleCinemaFinished(Ball ball, BallTrajectory trajectory)
        {
            
        }

        public override void PreviewCourse()
        {
            base.PreviewCourse();

            //ActionDispatcher.Dispatch(new PreviewCourseInitToken("Sandbox", "Par " + "0"));
        }

        #region Disconnection
        public override void OnRelayDisconnected(LocalPlayer disconnectedPlayer)
        {

        }
        #endregion

        #region ticking update 
        public override void OnPlayerSync(PlayerCommand playerCommand)
        {
            LocalPlayer player = GlobalSO.GameplayBus.localPlayer;
            player.AddCommand(player.GetStrokeCount(), playerCommand);
            Debug.Log("Local Sync Stroke: " + player.GetStrokeCount());
            Debug.Log(JsonConvert.SerializeObject(player.GetPlayerCommands()));
        }
        #endregion

        public override void OnApplicationQuit()
        {

        }
    }
}