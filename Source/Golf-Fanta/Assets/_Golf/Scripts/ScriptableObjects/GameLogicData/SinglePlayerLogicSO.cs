using System.Collections.Generic;
using _Golf.Physics.Data;
using _Golf.Scripts.CirclePoint;
using _Golf.Scripts.Core.CourseController;
using _Golf.Scripts.GameLogic;
using _Golf.Scripts.Lobby;
using GolfGame;
using UnityEngine;
using UnityEngine.Rendering.Universal;
namespace _Golf.Scripts.Core
{
    [CreateAssetMenu(menuName = "ScriptableObjects/Logic/SinglePlayerLogic", fileName = "SinglePlayerLogic")]
    public class SinglePlayerLogicSO : GolfLogicSO<SinglePlayerGolfLogic>
    {
        public int maximumStroke;

    }
    public class SinglePlayerGolfLogic : BaseGolfLogic
    {
        public new SinglePlayerLogicSO logicData => (SinglePlayerLogicSO)_logicData;
        public SinglePlayerGolfLogic() : base() { }

        public SinglePlayerGolfLogic(PlayerSessionInfo playerSession) : base(playerSession)
        {
            currentPlayingPlayer = playerSession;
        }
        public override void OnInitialize(GameplayBus bus, NewGameManager manager) { }

        public override void OnGameStart()
        {
        }
        
        public override void OnEndGame()
        {
            throw new System.NotImplementedException();
        }

        public override void OnOtherPlayerReadyForShot(LocalPlayer player)
        {
            throw new System.NotImplementedException();
        }

        public override void OnSelfTakeShot(LocalPlayer self, Ball ball, GolfShotData input, BallTrajectory trajectory)
        {
            currentPlayingPlayer.TotalStroke += 1;
        }

        public override void OnOtherPlayerTakeShot(LocalPlayer player)
        {
            throw new System.NotImplementedException();
        }

        public override void BotTakeShot(LocalPlayer bot)
        {
            throw new System.NotImplementedException();
        }

        public override void OnBallLanded(Ball ball, BallTrajectory trajectory) { }
        public override bool IsWon(Vector3 ballPos, Vector3 holePos)
        {
            return true;
        }
        public override void OnAllTaskCompleted(List<Ball> balls, HoleBehaviour hole) { }

        public override void SetGamePlayLogicParam(GameLogicParam param)
        {
            param.maximumStrokeCount = logicData.maximumStroke;
        }

        public override void OnSelfReadyForShot(LocalPlayer player)
        {
            throw new System.NotImplementedException();
        }

        public override void OnExit()
        {
            base.OnExit();
        }
        public override void OnBallFinished(Ball ball)
        {
        }

        public override void OnInHoleCinemaFinished(Ball ball, BallTrajectory trajectory)
        {
            throw new System.NotImplementedException();
        }

        public override void OnPlayerSync(PlayerCommand playerCommand)
        {
            
        }

        #region Disconnection
        public override void OnRelayDisconnected(LocalPlayer disconnectedPlayer)
        {

        }
        #endregion

        public override void OnApplicationQuit()
        {

        }
    }
}