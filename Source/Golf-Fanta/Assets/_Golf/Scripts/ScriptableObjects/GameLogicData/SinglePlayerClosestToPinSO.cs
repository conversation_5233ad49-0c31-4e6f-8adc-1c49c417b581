using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using _Golf.Physics.Data;
using _Golf.Scripts.CirclePoint;
using _Golf.Scripts.Common;
using _Golf.Scripts.Core.CourseController;
using _Golf.Scripts.GameLogic;
using _Golf.Scripts.Lobby;
using _Golf.Scripts.Networking.Photon.Lobby;
using _Golf.Scripts.PlayField;
using _Golf.Scripts.ScriptableObjects;
using _Golf.Scripts.ScriptableObjects.Tours;
using _Golf.Scripts.Tracking;
using _Golf.Scripts.UI;
using _Golf.Scripts.UI.GameplayUI;
using GolfFantaModule.Models;
using GolfGame;
using GolfGame.API;
using GolfPhysics;
using Newtonsoft.Json;
using TinyMessenger;
using Unity.Services.Economy;
using UnityEngine;

namespace _Golf.Scripts.Core
{
    [CreateAssetMenu(menuName = "ScriptableObjects/Logic/SinglePlayerClosestToPin", fileName = "SinglePlayerClosestToPin")]
    public class SinglePlayerClosestToPinSO : GolfLogicSO<SinglePlayerClosestToPin>
    {
        public float ShotCountTimer = 60f;
    }

    public class SinglePlayerClosestToPin : BaseGolfLogic
    {
        public new SinglePlayerClosestToPinSO logicData => (SinglePlayerClosestToPinSO)_logicData;
        private NewGameManager _manager;
        private GameplayBus _bus;
        private TinyMessageSubscriptionToken _gameStartToken;
        private TinyMessageSubscriptionToken _holeSpawnToken;

        private TinyMessageSubscriptionToken _resetGameToken;
        private TinyMessageSubscriptionToken _configClosestToPinToken;
        private TinyMessageSubscriptionToken _showPanelEndGameToken;
        private ClosestToPinReciveRequestModel _requestModel;
        private ClosesToPinRankReward _rewardbyRank;
        private Coroutine _timerCoroutine;
        private bool _isLucky;
        private ClosestToPinRewardRespone _cachedResponeData = null;
        private System.Action _rewardDataRespone;

        CirclePointMediator _circlePointMediator;

        private Vector3 _holePosition;

        public SinglePlayerClosestToPin()
        {
            activityRegistered = false;
            _holeSpawnToken = ActionDispatcher.Bind<HoleSpawnAction>(OnHoleSpawn);
        }

        private void OnHoleSpawn(HoleSpawnAction action)
        {
            _holePosition = action.holeBehaviour.transform.position;
        }

        #region Activity registration
        /// <summary>
        /// Initialize activity, this happens when game starts after golf scene is loaded and payment is complete
        /// </summary>
        public override void InitializeActivity()
        {
            // activity isnt registered yet, initialze a new one
            if (!activityRegistered)
            {
                Course course = GlobalSO.PlayFieldSO.Course;
                HoleInfo holeInfo = GlobalSO.PlayFieldSO.HoleInfo;
                GlobalSO.SessionSO.InitializeActivity(
                    GameMode.ClosestToPin,
                    course.CourseId,
                    holeInfo.Guid,
                    GlobalSO.GameplayBus.localPlayer.GetPlayerRankInfo().rankId,
                    additionalData: null
                );
            }
        }

        /// <summary>
        /// Register activity, this happens when game starts after golf scene is loaded and payment is complete
        /// Set registered activity to false because in CTP when you load a new map (by playing again)
        /// That value is still false, it leads to a new activity being registered
        /// </summary>
        public override void RegisterActivity()
        {
            // only register activity if it is not registered yet
            if (!activityRegistered)
            {
                GlobalSO.SessionSO.UploadActivity();

                activityRegistered = false;
                golfShotDatas.Clear(); golfShotDatas = new List<GolfShotDataModel>();
            }
        }
        #endregion

        public override void OnInitialize(GameplayBus bus, NewGameManager manager)
        {
            //InitializeActivity();

            _manager = manager;
            _bus = bus;
        
            _gameStartToken = ActionDispatcher.Bind<ReadyToPlayAction>(OnGameStartToken);
            _resetGameToken = ActionDispatcher.Bind<Common.ResetGameAction>(OnResetGame);
            _configClosestToPinToken = ActionDispatcher.Bind<ClosestToPinConfigAction>(OnSetConfigToken);
            _showPanelEndGameToken = ActionDispatcher.Bind<ShowEndGamePanelAction>(OnShowPanelEndGame);
            _requestModel = new ClosestToPinReciveRequestModel();

            InitializeGear();
        }
        
        public override bool CheckIfSceneObjectsAreComplete()
        {
            return (
                holeBehaviour != null &&
                flagBehaviour != null &&
                _balls != null && _balls.Count == 1 &&
                setPinBallPathLine != null &&
                ballPathLineRenderer != null &&
                ballPredictionLineRenderer != null &&
                pin != null &&
                pinCenter != null &&
                pinRadius != null &&
                circles != null
            );
        }

        public override void PreviewCourse()
        {
            ActionDispatcher.Dispatch(new ClosestToPinEnableAction(true));
            
            var playFied = GlobalSO.PlayFieldSO;
            ActionDispatcher.Dispatch(new PreviewCourseInitToken(playFied.HoleInfo.Name, ""));
            
            base.PreviewCourse();
        }

        public override void OnPreviewCourseFinished()
        {
            base.OnPreviewCourseFinished();

            ActionDispatcher.Dispatch(new ClosestToPinEnableAction(false));
        }

        public override void InitMediators()
        {
            base.InitMediators();

            _circlePointMediator = new CirclePointMediator();
            _circlePointMediator.SetCircles(circles);
            _circlePointMediator.SetLineRenderer(setPinBallPathLine);
            _circlePointMediator.SetHole(holeBehaviour.gameObject);

            MasterManager.Instance.AddMediator(_circlePointMediator);
        }

        public override void RemoveMediators()
        {
            base.RemoveMediators();

            MasterManager.Instance.RemoveMediator(_circlePointMediator);
        }

        public override void OnGameStart()
        {
            
        }

        private async void OnResetGame(Common.ResetGameAction ctx)
        {
            ActionDispatcher.Dispatch(new CompleteResetShotAction());
            GetCameraMediator().ResetAim();
            ActionDispatcher.Dispatch(new ReadyToPlayAction());
            await MasterManager.Instance.OnChangeScreen(EScreenEnum.Empty, null);
            await MasterManager.Instance.OnChangeScreen(EScreenEnum.ClosestToPinMenu, null);
            //MasterManager.Instance.OpenUIComponent(UIComponentEnum.ClosestToPinMenuComponentUI, null);
            ActionDispatcher.Dispatch(new ClosestToPinEnableAction(false));
        }

        /// <summary>
        /// Game starts after payment is complete
        /// </summary>
        /// <param name="token"></param>
        private void OnSetConfigToken(ClosestToPinConfigAction token)
        {
            InitializeActivity();
            RegisterActivity();

            ActionDispatcher.Dispatch(new ClosestToPinEnableAction(true));
            
            // Affect Wind 
            var ctpConfig = GlobalSO.RemoteConfigData.ClosestToPinConfig;
            var weatherConfig = ctpConfig.closesToPinConditionConfigData.difficulties[ctpConfig.CurrentDifficult].weatherConditions;
            var windSpeed = GolfUtility.GenerateWindSpeedLimit((int)ctpConfig.CurrentTour.GetWindSpeedMin(), (int)ctpConfig.CurrentTour.GetWindSpeedMax());
            if (weatherConfig.enabled)
            {
                WeatherData weatherData = new WeatherData();
                GolfUtility.GenerateWindDirectionCtp(out float windDirection, ctpConfig.CurrentDifficult);
                weatherData.WindSpeed = windSpeed;
                weatherData.WindDirection = windDirection;
                weatherData.isEnable = true;
                ActionDispatcher.Dispatch(new SetWindAction(weatherData));
                GlobalSO.PlayFieldSO.SetWind(weatherData);
            }
            else
            {
                WeatherData weatherData = new WeatherData();
                weatherData.Reset();
                ActionDispatcher.Dispatch(new SetWindAction(weatherData));
                GlobalSO.PlayFieldSO.SetWind(weatherData);
            }
            
            MasterManager.Instance.OnChangeScreen(EScreenEnum.GameplayScreen, null, true);
            
            _requestModel.ModePlayed = token.Mode;
            _rewardbyRank = token.RewardModel;
            _isLucky = token.IsLucky;
            if (_timerCoroutine != null)
            { 
                _manager.StopCoroutine(_timerCoroutine); 
            }
            _timerCoroutine = _manager.StartCoroutine(CoShotCountdown(() => OnShotClockExpire()));
        }

        private void OnShotClockExpire()
        {
            if (_timerCoroutine != null)
            { 
                _manager.StopCoroutine(_timerCoroutine); 
            }
            ActionDispatcher.Dispatch(new Common.ResetGameAction());
        }

        private IEnumerator CoShotCountdown(Action callback = null)
        {
            float totalTime = logicData.ShotCountTimer;
            float timer = logicData.ShotCountTimer;
            while (true)
            {
                timer -= Time.deltaTime;
                ActionDispatcher.Dispatch(new SetShotClockAction(timer, totalTime, 0));
                if (timer <= 0)
                {
                    ActionDispatcher.Dispatch(new ShotClockTimeOut());
                    callback?.Invoke();
                    break;
                }
                yield return null;
            }
        }
        
        private async void OnGameStartToken(ReadyToPlayAction token)
        {
            await MasterManager.Instance.OnChangeScreen(EScreenEnum.Empty, null, true);
            await MasterManager.Instance.OnChangeScreen(EScreenEnum.ClosestToPinMenu, null, true);
            // MasterManager.Instance.OpenUIComponent(UIComponentEnum.ClosestToPinMenuComponentUI, null);
            ActionDispatcher.Dispatch(new ClosestToPinEnableAction(false));
        }
        public override void OnEndGame()
        {
            
        }

        public override void OnOtherPlayerReadyForShot(LocalPlayer player)
        {
            throw new System.NotImplementedException();
        }

        float distanceFromHoleToBall;
        int maxDistance;
        bool receiveReward = false;

        public override void OnSelfTakeShot(LocalPlayer self, Ball ball, GolfShotData input, BallTrajectory trajectory)
        {
            var data = input;
            InputParam inputParam = new InputParam();
            var position = input.GetBallPosition();
            inputParam.BallPositionX = position.X;
            inputParam.BallPositionY = position.Y;
            inputParam.BallPositionZ = position.Z;

            inputParam.Velocity = input.Velocity;
            inputParam.LaunchAngle = input.LaunchAngle;
            inputParam.SideAngle = input.SideAngle;
            inputParam.BackSpin = input.BackSpin;
            inputParam.SideSpin = input.SideSpin;

            var forwardDirection = input.GetFowardDirection();
            inputParam.ForwardDirectionX = forwardDirection.X;
            inputParam.ForwardDirectionY = forwardDirection.Y;
            inputParam.ForwardDirectionZ = forwardDirection.Z;

            string json = JsonConvert.SerializeObject(inputParam);
            Debug.Log(json);

            var newData = JsonConvert.DeserializeObject<InputParam>(json);
            Debug.Log(newData.ToString());

            if (_timerCoroutine != null)
            {
                _manager.StopCoroutine(_timerCoroutine);
            }
            ActionDispatcher.Dispatch(new LocalPlayerHitBall(input));

            RegisterShotData(self, ball, input, trajectory);
            UploadShotData();

            // calculate result right away because i said so

            // CONFIG REWARD BY DISTANCE
            _rewardbyRank.rewards = _rewardbyRank.rewards.OrderBy(reward => reward.distance).ToList();
            distanceFromHoleToBall = UnitsConverter.MetersToFeet(Vector3.Distance(trajectory.Last.Position.ToVector3(), _holePosition));
            maxDistance = _rewardbyRank.rewards.Last().distance;
            _requestModel.RankRewadModel = _rewardbyRank;

            if (ball.IsBallInHole)
            {
                _requestModel.IsBallInHole = true;
            }
            else
            {
                _requestModel.IsBallInHole = false;
            }
            _requestModel.IsLucky = _isLucky;
            _requestModel.DistanceToHole = (float)distanceFromHoleToBall;

            receiveReward = false;
        }

        public override void OnOtherPlayerTakeShot(LocalPlayer player)
        {
            throw new System.NotImplementedException();
        }

        public override void BotTakeShot(LocalPlayer bot)
        {
            throw new System.NotImplementedException();
        }

        public override void OnBallLanded(Ball ball, BallTrajectory trajectory)
        {
            // If Distance > max ==>> lose
            if (distanceFromHoleToBall > maxDistance)
            {
                HandleLoseGame();
                return;
            }

            ActionDispatcher.Dispatch(new ClosestToPinEffectAction());

            var player = ball.localPlayer;
            player.SetUserStatus(PlayerStatus.FinishGame);
            GolfTaskManager.OnAllBallCompleted?.Invoke();
        }
        
        public override bool IsWon(Vector3 ballPos, Vector3 holePos)
        {
            return true;
        }
        public override void OnAllTaskCompleted(List<Ball> balls, HoleBehaviour hole)
        {
            RewardManager.OnCaculateResource?.Invoke(BallUsage, gearUsage, false);
            OnClaimRewardFromSever();
        }

        private async void OnClaimRewardFromSever()
        {
            _requestModel.ConfigAssignmentHash = EconomyService.Instance.Configuration.GetConfigAssignmentHash();
            var respone = await APIGameClient.Instance.ClosestToPinReward(_requestModel);
            _cachedResponeData = respone.data;
            
            TrackReward(_cachedResponeData);
            _rewardDataRespone?.Invoke();

            receiveReward = true;
        }

        private async void HandleLoseGame()
        {
            var timeDelay = (int)Constant.DelayTimeToChangeStage * 1000;
            await Task.Delay(timeDelay);
            ActionDispatcher.Dispatch(new Common.ResetGameAction());
        }
        
        private void OnShowPanelEndGame(ShowEndGamePanelAction ctx)
        {
            if (_cachedResponeData == null)
                _rewardDataRespone += HandleShowEndGame;
            else
                HandleShowEndGame();
        }

        private void HandleShowEndGame()
        {
            if(_cachedResponeData == null) return;
            ResultManager.OnShowClosestToPinResult?.Invoke(_cachedResponeData);
            _cachedResponeData = null;
            _rewardDataRespone -= HandleShowEndGame;
        }

        public override void SetGamePlayLogicParam(GameLogicParam param) { }
        public override void OnSelfReadyForShot(LocalPlayer player)
        {
            
        }

        public override void OnExit()
        {
            base.OnExit();
            ResetActivity();

            MasterManager.Instance.DestroyUIComponent(UIComponentEnum.GameplayGolfBallSwingUIComponent);
            MasterManager.Instance.DestroyUIComponent(UIComponentEnum.GameplayDebugUIComponent);
            MasterManager.Instance.DestroyUIComponent(UIComponentEnum.GameplayPlayersInfoUIComponent);
            MasterManager.Instance.DestroyUIComponent(UIComponentEnum.GameplayEffectsUIComponent);
            _bus.localLobby.SetLocalLobbyState(LobbyState.Lobby);
            _bus.localPlayer.ResetPlayer();
            RegisterStageScreen();
            _manager.ReturnToTitle();
            
            ActionDispatcher.Unbind(_gameStartToken);
            ActionDispatcher.Unbind(_configClosestToPinToken);
            ActionDispatcher.Unbind(_resetGameToken);
            ActionDispatcher.Unbind(_showPanelEndGameToken);
            ActionDispatcher.Unbind(_holeSpawnToken);
        }

        public override void OnBallFinished(Ball ball)
        {
            
        }
        
        public override void OnInHoleCinemaFinished(Ball ball, BallTrajectory trajectory)
        {
            
        }

        public override void OnPlayerSync(PlayerCommand playerCommand)
        {
            
        }

        #region Disconnection
        public override void OnRelayDisconnected(LocalPlayer disconnectedPlayer)
        {

        }
        #endregion

        /// <summary>
        /// In case game is shutdown abruptly, CTP to-cache-list:
        /// - Gear (No) [This mode does not allow gear]
        /// - Shots (No) [Shot data is uploaded immediately after a shot is taken]
        /// - Result (No) [Fuck your pass]
        /// </summary>
        public override void OnApplicationQuit()
        {
            if (distanceFromHoleToBall <= maxDistance && receiveReward == false)
            {
                // won, cache result
                CryptoHelper.AESEncryptedText encryptedResultDatas = CryptoHelper.Encrypt(JsonConvert.SerializeObject(_requestModel), CryptoHelper.Password);

                PlayerPrefs.SetString(CryptoHelper.EncryptedTextKey3, encryptedResultDatas.EncryptedText);
                PlayerPrefs.SetString(CryptoHelper.EncryptedIV3, encryptedResultDatas.IV);
            }

        }
        
        private void TrackReward(ClosestToPinRewardRespone reward)
        {
            const string earnSource = "CTP";
            
            TrackingManager.Instance.TrackEarnCoin(earnSource, reward.coin);
            
            foreach (var fragmentReward in reward.fragment)
                TrackingManager.Instance.TrackGetClub(fragmentReward.fragmentId, earnSource, fragmentReward.quantity);
        }
    }
}