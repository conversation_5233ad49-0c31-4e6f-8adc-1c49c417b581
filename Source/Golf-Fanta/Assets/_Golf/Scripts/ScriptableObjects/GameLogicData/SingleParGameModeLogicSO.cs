using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using _Golf.Physics.Data;
using _Golf.Scripts.CirclePoint;
using _Golf.Scripts.Common;
using _Golf.Scripts.Common.Helper;
using _Golf.Scripts.Core.CourseController;
using _Golf.Scripts.GameLogic;
using _Golf.Scripts.Lobby;
using _Golf.Scripts.UI.UIComponent.MatchMaking;
using GolfGame;
using UnityEngine;
using UnityEngine.Rendering.Universal;
namespace _Golf.Scripts.Core
{
    [CreateAssetMenu(menuName = "ScriptableObjects/Logic/SingleParGameMode", fileName = "SingleParGameMode")]
    public class SingleParGameModeLogicSO : GolfLogicSO<SingleParGameModeLogic>
    {
        public float winningDistance;
    }

    public class SingleParGameModeLogic : BaseGolfLogic
    {
        public new SingleParGameModeLogicSO logicData => (SingleParGameModeLogicSO)_logicData;
        private GameplayBus _gameplayBus;

        private List<Ball> _completedBalls = new();
        private NewGameManager _manager;
        public override void OnInitialize(GameplayBus bus, NewGameManager manager)
        {
            _completedBalls.Clear();
            _gameplayBus = bus;
            _manager = manager;
            bus.localPlayer.SetUserStatus(PlayerStatus.InGame);
            _ = bus.lobbyHandler.SyncPlayerData(bus.localPlayer);
        }

        public override void OnGameStart()
        {
        }
        public override void OnEndGame()
        {
            throw new System.NotImplementedException();
        }

        public override void OnOtherPlayerReadyForShot(LocalPlayer player)
        {
            throw new System.NotImplementedException();
        }
        
        public override void OnSelfTakeShot(LocalPlayer self, Ball ball, GolfShotData input, BallTrajectory trajectory)
        {
            var data = input;
            var localPlayer = _gameplayBus.localPlayer;
            InputParam inputParam = InputParamHelper.Get(data);
            // var position = input.GetBallPosition();
            // inputParam.BallPositionX = position.X;
            // inputParam.BallPositionY = position.Y;
            // inputParam.BallPositionZ = position.Z;
            //
            // inputParam.Velocity = input.Velocity;
            // inputParam.LaunchAngle = input.LaunchAngle;
            // inputParam.SideAngle = input.SideAngle;
            // inputParam.BackSpin = input.BackSpin;
            // inputParam.SideSpin = input.SideSpin;
            //
            // var forwardDirection = input.GetFowardDirection();
            // inputParam.ForwardDirectionX = forwardDirection.X;
            // inputParam.ForwardDirectionY = forwardDirection.Y;
            // inputParam.ForwardDirectionZ = forwardDirection.Z;

            localPlayer.SetInputParam(inputParam);
            localPlayer.SetUserStatus(PlayerStatus.HitBall);
            Debug.Log(inputParam.ToString());
            _ = _gameplayBus.lobbyHandler.SyncPlayerData(localPlayer);
        }

        public override void OnOtherPlayerTakeShot(LocalPlayer player)
        {
            _manager.StartCoroutine(OnOpponentHitBall(player));
        }

        public override void BotTakeShot(LocalPlayer bot)
        {
            throw new System.NotImplementedException();
        }

        public override void OnBallLanded(Ball ball, BallTrajectory trajectory)
        {
            Debug.Log("some ball landed " + ball.localPlayer.GetId());
            var ballId = ball.localPlayer.GetId();
            var localPlayer = _gameplayBus.localPlayer;
            if (ballId == localPlayer.GetId())
            {
                localPlayer.SetBallPos(ball.transform.position);
                localPlayer.SetStrokeCount(localPlayer.GetStrokeCount() + 1);
                _ = _gameplayBus.lobbyHandler.SyncPlayerData(localPlayer);
            }

            _completedBalls.Add(ball);
            if (_completedBalls.Count == _gameplayBus.localLobby.LocalPlayers.Count)
            {
                GolfTaskManager.OnAllBallCompleted?.Invoke();
            }
        }
        public override bool IsWon(Vector3 ballPos, Vector3 holePos)
        {
            var distance = DistanceCalculation.BallDistanceFromHole(ballPos, holePos);
            return distance < logicData.winningDistance;
        }
        public override void OnAllTaskCompleted(List<Ball> balls, HoleBehaviour hole)
        {
            HeadToHeadMatchProperties headToHeadMatchProperties = new HeadToHeadMatchProperties();
            headToHeadMatchProperties.playerIds = new List<string>();

            Ball closetToHole = balls[0];
            foreach (var ball in balls)
            {
                var ballDistanceToHole = ball.GetDistanceFromHole(hole);
                if (ballDistanceToHole > closetToHole.GetDistanceFromHole(hole)) continue;
                closetToHole = ball;

                headToHeadMatchProperties.playerIds.Add(ball.localPlayer.GetId());
            }

            int i = 0;
            foreach (var player in headToHeadMatchProperties.playerIds)
            {
                if (player.Equals(closetToHole.localPlayer.GetId()))
                {
                    headToHeadMatchProperties.winnerIndex = i;
                }
                i++;
            }

            Debug.Log("ball all task completed " + closetToHole.localPlayer.GetId());
            RewardManager.OnRewardWinners?.Invoke(closetToHole.localPlayer.GetId(), headToHeadMatchProperties);
            
            MultiPlayerWinUIResultData resultData = new MultiPlayerWinUIResultData
            {
                Winner = closetToHole,
                Loser = balls.FirstOrDefault(b => b.localPlayer.GetStrokeCount() < closetToHole.localPlayer.GetStrokeCount()),
                CurrentLobbyProperty = _gameplayBus.currentLobbyProperty 
            };
            ResultManager.OnShowMultiplayerResult?.Invoke(resultData);
        }

        private IEnumerator OnOpponentHitBall(LocalPlayer player)
        {
            yield return null;
            object[] input = new object[7];

            var inputParam = player.GetInputParam();
            
            input[0] = new DoubleVector3(inputParam.BallPositionX, inputParam.BallPositionY, inputParam.BallPositionZ);
            input[1] = inputParam.Velocity;
            input[2] = inputParam.LaunchAngle;
            input[3] = inputParam.SideAngle;
            input[4] = inputParam.BackSpin;
            input[5] = inputParam.SideSpin;
            input[6] = new DoubleVector3(inputParam.ForwardDirectionX, inputParam.ForwardDirectionY, inputParam.ForwardDirectionZ);
            
            var affectData = TransferAffectHelper.Get(inputParam.CurrentBall,
                inputParam.CurrentGear, inputParam.CurrentClub);
            
            GolfShotData shotData = new GolfShotData(
                BallPositionX: inputParam.BallPositionX, BallPositionY: inputParam.BallPositionY, BallPositionZ: inputParam.BallPositionZ,                         //placeholder, swing UI does not control this value
                ForwardDirectionX: inputParam.ForwardDirectionX, ForwardDirectionY: inputParam.ForwardDirectionY, ForwardDirectionZ: inputParam.ForwardDirectionZ,
                Velocity: inputParam.Velocity,
                LaunchAngle: inputParam.LaunchAngle,
                SideAngle: inputParam.SideAngle,
                BackSpin: inputParam.BackSpin,
                SideSpin: inputParam.SideSpin,
                BallData: affectData.BallData,
                ClubData: null,
                ClubLevel: null,
                GearData: affectData.GearData
            );
            
            foreach (var ball in _balls)
            {
                if (ball.localPlayer.GetId() != player.GetId()) continue;
                Debug.Log("opponent hit ball");
                ActionDispatcher.Dispatch(new OpponentHitBallAction(player, shotData, ball, false));
                break;
            }
        }

        public override void SetGamePlayLogicParam(GameLogicParam param) { }
        public override void OnSelfReadyForShot(LocalPlayer player)
        {
            throw new System.NotImplementedException();
        }

        public override void OnExit()
        {
            base.OnExit();
            EndGameAsync();
        }
        public override void OnBallFinished(Ball ball) { }
        private async void EndGameAsync()
        {
            await _gameplayBus.lobbyHandler.LeaveLobbyAsync();
            _gameplayBus.localPlayer.ResetPlayer();
            RegisterStageScreen();
            _manager.ReturnToTitle();
        }

        public override void OnInHoleCinemaFinished(Ball ball, BallTrajectory trajectory)
        {
            throw new System.NotImplementedException();
        }

        public override void OnPlayerSync(PlayerCommand playerCommand)
        {
            
        }

        #region Disconnection
        public override void OnRelayDisconnected(LocalPlayer disconnectedPlayer)
        {

        }
        #endregion

        public override void OnApplicationQuit()
        {

        }
    }
}