using _Golf.Scripts.Lobby;
using UnityEngine;

namespace _Golf.Scripts.Core
{
    public abstract class GolfLogicSO : ScriptableObject
    {
        public GameMode gameMode;
        public abstract BaseGolfLogic CreateLogic();
    }

    public abstract class GolfLogicSO<T> : GolfLogicSO where T : BaseGolfLogic, new()
    {
        public override BaseGolfLogic CreateLogic()
        {
            BaseGolfLogic logic = new T();
            logic.SetData(this);
            return logic;
        }
    }
    
    public enum GameMode
    {
        Sandbox = 0,
        HeadToHead = 1,
        ClosestToPin = 2,
        Tournament = 3,
        RaceChallenge = 4,
    }

    public enum ShotRating
    {
        None,
        Perfect,
        Great
    }
}