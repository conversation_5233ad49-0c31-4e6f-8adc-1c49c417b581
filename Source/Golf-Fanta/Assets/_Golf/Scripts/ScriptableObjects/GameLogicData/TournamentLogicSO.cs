using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using _Golf.Physics.Data;
using _Golf.Scripts.Common;
using _Golf.Scripts.Common.Helper;
using _Golf.Scripts.Core.CourseController;
using _Golf.Scripts.GameLogic;
using _Golf.Scripts.GlobalManagers;
using _Golf.Scripts.Lobby;
using _Golf.Scripts.Networking.Photon.Lobby;
using _Golf.Scripts.PlayField;
using _Golf.Scripts.ScriptableObjects;
using _Golf.Scripts.ScriptableObjects.Tours;
using _Golf.Scripts.ScriptableObjects.PlayField;
using _Golf.Scripts.UI;
using _Golf.Scripts.UI.GameplayUI;
using Cysharp.Threading.Tasks;
using GolfFantaModule.Models;
using GolfFantaModule.Models.Economy;
using GolfGame;
using GolfGame.API;
using GolfPhysics.UnitySurface;
using Newtonsoft.Json;
using TinyMessenger;
using Unity.Services.Matchmaker.Models;
using UnityEngine;

namespace _Golf.Scripts.Core
{
    [CreateAssetMenu(menuName = "ScriptableObjects/Logic/Tournament", fileName = "TournamentLogicSO")]
    public class TournamentLogicSO : GolfLogicSO<TournamentLogic> { }

    public class TournamentLogic : BaseGolfLogic
    {
        #region Managers
        private GameplayBus _gameplayBus;
        private NewGameManager _manager;
        private PlayFieldScriptableObject _playField;
        private LocalLobby LocalLobby => _gameplayBus.localLobby;
        #endregion
        
        #region Actions
        private TinyMessageSubscriptionToken _holeSpawnToken;
        private TinyMessageSubscriptionToken _gameStartToken;
        private TinyMessageSubscriptionToken _readyToPreviewCourseToken;
        #endregion

        #region Calculate Data
        private List<Ball> _inHoleBalls = new();
        private float? _tieBreakPoint;
        private Vector3 _holePosition;
        private float _longestShotDistance;
        private LocalPlayer _localPlayer;
        private GolfShotData cachedLocalinput;
        #endregion

        #region gear usage
        public override void InitializeGear()
        {
            base.InitializeGear();
            GlobalSO.PlayerGadget.ChosenChip = null;
        }

        public override bool CheckIfGameModeAllowThisGear(GearData gearData)
        {
            return gearData.IsTournamentUsable();
        }
        #endregion

        #region Activity registration
        /// <summary>
        /// Initialize activity, this happens when golf scene is loaded
        /// </summary>
        public override void InitializeActivity()
        {
            // activity isnt registered yet, initialze a new one
            if (!activityRegistered)
            {
                Course course = GlobalSO.PlayFieldSO.Course;
                HoleInfo holeInfo = GlobalSO.PlayFieldSO.HoleInfo;
                GlobalSO.SessionSO.InitializeActivity(
                    GameMode.Tournament,
                    course.CourseId,
                    holeInfo.Guid,
                    GlobalSO.GameplayBus.localPlayer.GetPlayerRankInfo().rankId,
                    additionalData: null
                );
            }
            else
            {
                // activity is already registered, this means player just advanced to another hole
                // => update activity by adding new hole
                GlobalSO.SessionSO.AddHoleToCurrentActivity(GlobalSO.PlayFieldSO.HoleInfo.Guid);
            }
        }

        /// <summary>
        /// Register activity, this happens when golf scene is loaded
        /// Set registered activity to true because in TOURNAMENT when you load a new map (by advancing to the next hole)
        /// That value is true so a new activity isnt created
        /// </summary>
        public override void RegisterActivity()
        {
            if (!activityRegistered)
            {
                GlobalSO.SessionSO.UploadActivity();

                activityRegistered = true;
                golfShotDatas.Clear(); golfShotDatas = new List<GolfShotDataModel>();
            }
        }

        /// <summary>
        /// In Tournament mode, we do not used the regular api to upload shot data
        /// instead it is combined with the api to save hole progress
        /// So shot data is reset after hole progress is saved
        /// </summary>
        public override void UploadShotData()
        {
            golfShotDatas.Clear(); golfShotDatas = new List<GolfShotDataModel>();
        }
        #endregion

        public TournamentLogic()
        {
            _playField = GlobalSO.PlayFieldSO;

            activityRegistered = false;

            _gameStartToken = ActionDispatcher.Bind<ReadyToPlayAction>(OnGameStartToken);
            _holeSpawnToken = ActionDispatcher.Bind<HoleSpawnAction>(OnHoleSpawned);
            _readyToPreviewCourseToken = ActionDispatcher.Bind<PreviewCourseToken>(OnStartPreview);
        }
        public override void Dispose()
        {
            ActionDispatcher.Unbind(_gameStartToken);
            ActionDispatcher.Unbind(_holeSpawnToken);
            ActionDispatcher.Unbind(_readyToPreviewCourseToken);
        }

        private void OnStartPreview(PreviewCourseToken obj)
        {
            ActionDispatcher.Dispatch(new PreviewCourseInitToken(_playField.HoleInfo.Name, "PAR " + _playField.HoleInfo.Par));
        }

        private void OnGameStartToken(ReadyToPlayAction obj)
        {
            _gameplayBus = GlobalSO.GameplayBus;
            
            MasterManager.Instance.OnChangeScreen(EScreenEnum.GameplayScreen, null, true);
            
            // Reset Stroke
            _localPlayer?.SetStrokeCount(0);

            // Affect Wind 
            // ALso Need Get Chosen Tournament, Get First For Testing 
            var difficulty = GlobalSO.TournamentPlaySO.Difficulty;
            var windSpeedMph = GolfUtility.GenerateWindSpeedLimit((int)difficulty.WindSpeedMin, (int)difficulty.WindSpeedMax);
            ///////
            _tieBreakPoint = null;
            _longestShotDistance = 0;
            var weatherData = new WeatherData();
            var windDirection = GolfUtility.GenerateWindDirection();
            weatherData.WindSpeed = windSpeedMph;
            weatherData.WindDirection = windDirection;
            weatherData.isEnable = true;
            ActionDispatcher.Dispatch(new SetWindAction(weatherData));
            GlobalSO.PlayFieldSO.SetWind(weatherData);
            // Affect Needle Speed
            ActionDispatcher.Dispatch(new TournamentEnableAction(true));
            // Init Stroke UI
            ActionDispatcher.Dispatch(new SetShotClockAction(0, 0, 0));
            ActionDispatcher.Dispatch(new TurnOffCountDownAction());

            var _currentTournament = GlobalSO.TournamentPlaySO.CurrentTournament;
            int index = _currentTournament.HoleGuids.IndexOf(_playField.HoleInfo.Guid);
            if (index == 0)
            {
                var noti = LocalizationManager.Instance.GetString("tournament_start");
                ActionDispatcher.Dispatch(new TournamentAnouncementAction(noti));
            }
            else
            {
                var localizeNoti = LocalizationManager.Instance.GetLocalizedString("tournament_hole_start");
                if (localizeNoti != null)
                {
                    localizeNoti.Arguments = new object[] { index + 1 };
                    ActionDispatcher.Dispatch(new TournamentAnouncementAction(localizeNoti.GetLocalizedString()));
                }
                else
                    ActionDispatcher.Dispatch(new TournamentAnouncementAction("HOLE " + (index + 1) + " START"));
            }
        }

        public override void OnInitialize(GameplayBus bus, NewGameManager manager)
        {
            InitializeActivity();
            RegisterActivity();

            _gameplayBus = bus;
            _manager = manager;
            _localPlayer = _gameplayBus.localLobby.LocalPlayers.FirstOrDefault();
            MasterManager.Instance.HideUIComponentAsync(UIComponentEnum.TournamentsScoreComponentUI);
            InitializeBallUsage();
            InitializeGear();
        }

        public override void OnGameStart()
        {
            
        }
        
        public override void OnEndGame()
        {
            _manager.StartCoroutine(EndGameCoroutine());
        }

        private IEnumerator EndGameCoroutine()
        {
            yield return new WaitForSeconds(1f);
            GolfTaskManager.OnAllBallCompleted?.Invoke();
        }

        public override void OnBallLanded(Ball ball, BallTrajectory trajectory)
        {
            _longestShotDistance = Mathf.Max(_longestShotDistance, (float)trajectory.GetTotalDistance());
            
            if (ball.IsPenalty)
            {
                AddBallToConsumeList(GlobalSO.PlayerGadget.GetCurrentBallId(), ball.IsPenalty);
            }

            if (ball.IsBallInHole)
            {
                ActionDispatcher.Dispatch(new CameraOrbitHoleToken(ball, trajectory));
            }
            else
                _manager.StartCoroutine(CinematicsTilNextShot(ball, trajectory));
        }

        public override bool IsWon(Vector3 ballPos, Vector3 holePos)
        {
            return true;
        }

        public override void OnAllTaskCompleted(List<Ball> balls, HoleBehaviour hole)
        {
            //TODO: SHOW RESULT
            MasterManager.Instance.OpenUIComponent(UIComponentEnum.TournamentFinishedComponentUI);
        }

        public override void SetGamePlayLogicParam(GameLogicParam param)
        {
            
        }

        public override void OnSelfReadyForShot(LocalPlayer player)
        {
           
        }

        public override void OnOtherPlayerReadyForShot(LocalPlayer player)
        {
            
        }
        
        private IEnumerator CinematicsTilNextShot(Ball ball, BallTrajectory trajectory)
        {
            yield return new WaitForSeconds(1.0f);
            if (ball.IsPenalty)
            {
                ActionDispatcher.Dispatch(new ResetBallLastPositionAction(ball.localPlayer.GetId(), isTieBreak: false));
            }
            if (cachedLocalinput?.GearData != null)
            {
                if (cachedLocalinput.GearData.type == GearType.Trial)
                {
                    ActionDispatcher.Dispatch(new ResetBallLastPositionAction(ball.localPlayer.GetId(), isTieBreak: false));
                }
            }
            ActionDispatcher.Dispatch(new PrepShotAction(ball));
            yield return new WaitForSeconds(1.5f);
            ActionDispatcher.Dispatch(new MoveCameraForNextShotAction(ball, trajectory));
            yield return new WaitForSeconds(0.5f);
            ActionDispatcher.Dispatch(new ContinueShotAction());
            ball.localPlayer.SetUserStatus(PlayerStatus.ReadyForShot);
        }

        public override void OnSelfTakeShot(LocalPlayer player, Ball ball, GolfShotData input, BallTrajectory trajectory)
        {
            var inputParam = InputParamHelper.Get(input);

            if (input.GearData != null)
            {
                if (input.GearData.type != GearType.Trial && input.GearData.type != GearType.DownPoint)
                {
                    player.SetStrokeCount(player.GetStrokeCount() + 1);
                }
            }
            else
            {
                player.SetStrokeCount(player.GetStrokeCount() + 1);
            }

            player.SetInputParam(inputParam);
            player.SetUserStatus(PlayerStatus.HitBall);
            player.SetBallPos(new Vector3((float)inputParam.BallPositionX, (float)inputParam.BallPositionY, (float)inputParam.BallPositionZ));

            cachedLocalinput = input;

            AddGearUsage(input.GearData);
            AddBallToConsumeList(input.BallData.id.ToUpper());
            AddOneUseConsumedOnHitGearUsage(input.GearData, isLocal: true);
            localUnchangeableUponPickGear.Clear(); localUnchangeableUponPickGear = new Dictionary<GearType, GearData>();

            ActionDispatcher.Dispatch(new LocalPlayerHitBall(input));
            
            //First green and chip case
            if (ball.RestingSurfaceType == SurfaceType.Green || ball.RestingSurfaceType == SurfaceType.HoleCup)
                UpdateTieBreakPoint(ball.RestingPosition, player.GetStrokeCount());

            RegisterShotData(player, ball, input, trajectory);
        }

        public override void OnOtherPlayerTakeShot(LocalPlayer player)
        {
            
        }

        public override void BotTakeShot(LocalPlayer bot)
        {
            
        }

        public override void OnExit()
        {
            base.OnExit();
            EndGameAsync();
        }

        public override void OnBallFinished(Ball ball)
        {
            ball.ResetShotData();
        }
        
        private async void EndGameAsync()
        {
            ResetActivity();

            //ActionDispatcher.Unbind(_holeSpawnToken);
            ActionDispatcher.Unbind(_gameStartToken);
            ActionDispatcher.Unbind(_readyToPreviewCourseToken);
            MasterManager.Instance.DestroyUIComponent(UIComponentEnum.GameplayGolfBallSwingUIComponent);
            MasterManager.Instance.DestroyUIComponent(UIComponentEnum.GameplayDebugUIComponent);
            MasterManager.Instance.DestroyUIComponent(UIComponentEnum.GameplayPlayersInfoUIComponent);
            MasterManager.Instance.DestroyUIComponent(UIComponentEnum.GameplayEffectsUIComponent);
            _gameplayBus.localPlayer.ResetPlayer();
            RegisterStageScreen();
            _manager.ReturnToTitle();
        }

        private async void FinishHole(int strokesCount)
        {
            const int timeDelayBeforeLoadNextHole = 2;
            await UniTask.WaitForSeconds(timeDelayBeforeLoadNextHole);
            MasterManager.Instance.OnChangeScreen(EScreenEnum.Empty, null).Forget();
            MasterManager.Instance.DestroyUIComponent(UIComponentEnum.GameplayGolfBallSwingUIComponent);
            MasterManager.Instance.DestroyUIComponent(UIComponentEnum.GameplayDebugUIComponent);
            MasterManager.Instance.DestroyUIComponent(UIComponentEnum.GameplayPlayersInfoUIComponent);
            //MasterManager.Instance.DestroyUIComponent(UIComponentEnum.GameplayEffectsUIComponent);
            _localPlayer?.ResetPlayer();

            #region update hole progress + shot data
            List<GolfShotDataModel> cloneShotDatas = new List<GolfShotDataModel>(golfShotDatas);

            ActionDispatcher.Dispatch(new FinishHoleAction(strokesCount, _tieBreakPoint ?? 0f, _longestShotDistance, cloneShotDatas));

            // reset shot data, this function is overridden in tournament
            UploadShotData();
            #endregion

            RewardManager.OnCaculateResource?.Invoke(BallUsage, gearUsage, false);
            _balls.Clear();
            _inHoleBalls.Clear();
        }

        private void UpdateTieBreakPoint(Vector3 ballPosition, int strokeCount)
        {
            if(_tieBreakPoint != null) return;
            
            var par = GlobalSO.PlayFieldSO.HoleInfo.Par;
            var score = strokeCount - par;
            var countTbp = score <= GlobalSO.RemoteConfigData.TournamentConfig.tbpScoreThreshold;
            if (!countTbp) return;
            var distanceToPinInMeter = Vector3.Distance(ballPosition, _holePosition);
            _tieBreakPoint = Mathf.RoundToInt(1000f - UnitsConverter.MetersToFeet(distanceToPinInMeter) * 9f);
            
            ActionDispatcher.Dispatch(new ShowTieBreakPointAction(_tieBreakPoint.Value));
        }

        private void OnHoleSpawned(HoleSpawnAction action)
        {
            _holePosition = action.holeBehaviour.transform.position;
        }

        public override void OnInHoleCinemaFinished(Ball ball, BallTrajectory trajectory)
        {
            FinishHole(ball.localPlayer.GetStrokeCount());
        }

        public override void OnPlayerSync(PlayerCommand playerCommand)
        {
            
        }

        #region Disconnection
        public override void OnRelayDisconnected(LocalPlayer disconnectedPlayer)
        {

        }
        #endregion

        /// <summary>
        /// In case game is shutdown abruptly, TOURNAMENT to-cache-list:
        /// - Gear (Yes)
        /// - Shots (No) [Shot data is uploaded only when a hole is finished]
        /// - Result (No) [Progress of a hole is lost]
        /// </summary>
        public override void OnApplicationQuit()
        {
            RewardManager.OnCaculateResource?.Invoke(BallUsage, gearUsage, true);
        }
    }
}