using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using _Golf.Physics.Data;
using _Golf.Scripts.Common;
using _Golf.Scripts.Common.Helper;
using _Golf.Scripts.Core.CourseController;
using _Golf.Scripts.GameLogic;
using _Golf.Scripts.Lobby;
using _Golf.Scripts.Networking.Photon.Lobby;
using _Golf.Scripts.PlayField;
using _Golf.Scripts.ScriptableObjects;
using _Golf.Scripts.Tracking;
using _Golf.Scripts.UI;
using _Golf.Scripts.UI.GameplayUI;
using _Golf.Scripts.UI.UIComponent.MatchMaking;
using Cysharp.Threading.Tasks;
using GolfFantaModule.Models;
using GolfFantaModule.Models.Economy;
using GolfFantaModule.Models.Economy.GearCustom;
using GolfGame;
using GolfPhysics;
using GolfPhysics.UnitySurface;
using Newtonsoft.Json;
using TinyMessenger;
using UnityEngine;

namespace _Golf.Scripts.Core
{
    [CreateAssetMenu(menuName = "ScriptableObjects/Logic/MultiplayerNormalGameMode", fileName = "MultiplayerNormalGameMode")]
    public class MultiplayerNormalLogicSO : GolfLogicSO<MultiplayerNormalLogic>
    {
        public float ShotCountTimer = 30f;
    }

    public class MultiplayerNormalLogic : BaseGolfLogic
    {
        // logic data
        public new MultiplayerNormalLogicSO logicData => (MultiplayerNormalLogicSO)_logicData;

        private GameplayBus _gameplayBus;
        private NewGameManager _manager;
        
        private LocalLobby LocalLobby => _gameplayBus.localLobby;
        private List<LocalPlayer> Players => LocalLobby.LocalPlayers;
        private LocalPlayer SelfPlayer => Players.FirstOrDefault(player => player.GetId() == _gameplayBus.localPlayer.GetId());
        private bool IsSelfHost => SelfPlayer?.GetId() == LocalLobby.GetHostID();

        private TinyMessageSubscriptionToken _holeSpawnToken;
        private TinyMessageSubscriptionToken _gameStartToken;

        private List<Ball> _inHoleBalls = new();

        private List<Ball> _finishedTieBreakBalls = new();
        private List<Ball> _inHoleTieBreakBalls = new();

        private float localTieBreakDistanceToHole = 0f;
        private float oppoTieBreakDistanceToHole = 0f;
        private float _timerWarningThreshold = 10f;

        private Vector3 _holePosition;
        private float _timeFromConfig;
        private Coroutine _timerCoroutine;

        private GameStats _gameStats;

        private GolfShotData cachedLocalinput;
        private GolfShotData cachedOppoInput;
        
        private UniTask _calculateBotShotTask;

        #region result calculation
        private bool isTieBreak;
        private MultiPlayerWinUIResultData result;
        #endregion

        #region skip opponent shot when won
        private const bool skipShotAfterWinning = true;
        private bool gameEnded = false;
        #endregion
        
        #region gear usage
        private Dictionary<GearType, GearData> opponentOneUseConsumedOnHitGear = new Dictionary<GearType, GearData>();

        public override void InitializeGear()
        {
            base.InitializeGear();
            opponentOneUseConsumedOnHitGear = new Dictionary<GearType, GearData>();
            GlobalSO.PlayerGadget.ChosenChip = null;
        }

        public override void AddOneUseConsumedOnHitGearUsage(GearData gearData, bool isLocal)
        {
            if (gearData == null) return;

            if (gearData.IsOneUseConsumedOnHitGear())
            {
                if (isLocal)
                {
                    if (!localOneUseConsumedOnHitGear.ContainsKey(gearData.type))
                    {
                        localOneUseConsumedOnHitGear.Add(gearData.type, gearData);
                    }
                }
                else
                {
                    if (!opponentOneUseConsumedOnHitGear.ContainsKey(gearData.type))
                    {
                        opponentOneUseConsumedOnHitGear.Add(gearData.type, gearData);
                    }
                }
            }
        }

        public override bool IsOneUseConsumedOnHitGearUsed(GearData gearData, bool isLocal)
        {
            if (isLocal)
            {
                return localOneUseConsumedOnHitGear.ContainsKey(gearData.type);
            }
            else
            {
                return opponentOneUseConsumedOnHitGear.ContainsKey(gearData.type);
            }
        }
        #endregion

        #region Calculate winning coin and losing coin
        public int CalculateLocalResultCoin()
        {
            int baseAmount = _gameplayBus.currentLobbyProperty.WinningAmount;

            if (result == null)
            {
                return 0;
            }

            if (result.Winner.IsLocalBall())
            {
                // local wins
                if (localOneUseConsumedOnHitGear.ContainsKey(GearType.CoinBoost))
                {
                    GearData gearData = localOneUseConsumedOnHitGear[GearType.CoinBoost];
                    CoinBoostGearData coinBoostGearData = (CoinBoostGearData)gearData.GetGearData();

                    int strokeCount = result.Winner.localPlayer.GetStrokeCount();
                    int par = GlobalSO.PlayFieldSO.HoleInfo.Par;

                    int scoreDiff = strokeCount - par;

                    if (strokeCount == 1)
                    {
                        return (int)(baseAmount * (coinBoostGearData.finalResultBonus4 + 1f));
                    }

                    switch (scoreDiff)
                    {
                        case -1:
                            {
                                return (int)(baseAmount * (coinBoostGearData.finalResultBonus1 + 1f));
                            }
                        case -2:
                            {
                                return (int)(baseAmount * (coinBoostGearData.finalResultBonus2 + 1f));
                            }
                        case -3:
                            {
                                return (int)(baseAmount * (coinBoostGearData.finalResultBonus3 + 1f));
                            }
                    }

                    return baseAmount;
                }
                else
                {
                    return baseAmount;
                }
            }
            else
            {
                // local loses
                if (localOneUseConsumedOnHitGear.ContainsKey(GearType.CoinProtection))
                {
                    return _gameplayBus.currentLobbyProperty.TourInfo.GetEntryFee();
                }
                else
                {
                    return 0;
                }
            }
        }
        #endregion

        #region Activity registration
        /// <summary>
        /// Initialize activity, this happens when golf scene is loaded
        /// </summary>
        public override void InitializeActivity()
        {
            // activity isnt registered yet, initialze a new one
            if (!activityRegistered)
            {
                LobbyProperty lobbyProperties = GlobalSO.GameplayBus.currentLobbyProperty;
                GlobalSO.SessionSO.InitializeActivity(
                    lobbyProperties.GameMode,
                    lobbyProperties.CourseInfo.Id,
                    lobbyProperties.CourseInfo.CurrentHole.Guid,
                    GlobalSO.GameplayBus.localPlayer.GetPlayerRankInfo().rankId,
                    additionalData: null
                );
            }
        }

        /// <summary>
        /// Register activity, this happens when golf scene is loaded
        /// Set registered activity to false because in H2H when you load a new map (by rematching)
        /// That value is still false, it leads to a new activity being registered
        /// </summary>
        public override void RegisterActivity()
        {
            // only register activity if it is not registered yet
            if (!activityRegistered)
            {
                GlobalSO.SessionSO.UploadActivity();

                activityRegistered = false;
                golfShotDatas.Clear(); golfShotDatas = new List<GolfShotDataModel>();
            }
        }
        #endregion

        public MultiplayerNormalLogic()
        {
            activityRegistered = false;

            _holeSpawnToken = ActionDispatcher.Bind<HoleSpawnAction>(OnHoleSpawn);
            _gameStartToken = ActionDispatcher.Bind<ReadyToPlayAction>(OnGameStartToken);
            _timerWarningThreshold = GlobalSO.RemoteConfigData.CommonSettings.TimerWarningThreshold;
        }

        public override bool CheckIfSceneObjectsAreComplete()
        {
            return (
                holeBehaviour != null &&
                flagBehaviour != null &&
                _balls != null && _balls.Count == 2 &&
                setPinBallPathLine != null &&
                ballPathLineRenderer != null &&
                ballPredictionLineRenderer != null &&
                pin != null &&
                pinCenter != null &&
                pinRadius != null
            );
        }

        public override void OnPreviewCourseFinished()
        {
            base.OnPreviewCourseFinished();
        }

        public override void PreviewCourse()
        {
            var courseName = GlobalSO.GameplayBus.currentLobbyProperty.CourseInfo.CurrentHole.Name;
            
            var par = GlobalSO.PlayFieldSO.HoleInfo.Par;
            
            ActionDispatcher.Dispatch(new PreviewCourseInitToken(courseName, "Par " + par));
            
            base.PreviewCourse();
        }

        #region BaseLogicEvent
        public override void OnInitialize(GameplayBus bus, NewGameManager manager)
        {
            InitializeActivity();
            RegisterActivity();

            _balls.Clear();
            _inHoleBalls.Clear();

            _finishedTieBreakBalls.Clear();
            _inHoleTieBreakBalls.Clear();

            localTieBreakDistanceToHole = 0f;
            oppoTieBreakDistanceToHole = 0f;

            gameEnded = false;

            isTieBreak = false;
            result = null;

            InitializeGear();
			InitializeBallUsage();

            _gameplayBus = bus;
            _manager = manager;
            var rankInfo = GlobalSO.PlayerInfoSO.Info.H2hRank.Info;
            _timeFromConfig = rankInfo.turnDuration;
            GlobalSO.NeedleProperties.GameModeModifier = rankInfo.needleSpeed -1;
            _gameStats = new GameStats();
            
            _ = bus.lobbyHandler.SyncPlayerData(bus.localPlayer);

            tickingUpdateLocal = TickingUpdateLocal();
            _manager.StartCoroutine(tickingUpdateLocal);

            localOpponentActionTracker = new List<int>();
            tickingUpdateOpponent = TickingUpdateOpponent();
            _manager.StartCoroutine(tickingUpdateOpponent);
        }

        protected override void OnSceneObjectsComplete()
        {
            if (CheckIfSceneObjectsAreComplete())
            {
                MasterManager.Instance.CurrentBallInCamera = LocalPlayerIndex;

                InitCameras();
                InitMediators();

                var weatherData = new WeatherData
                {
                    isEnable = true,
                    WindSpeed = _gameplayBus.localLobby.GetLocalLobbyWeather().windSpeed,
                    WindDirection = _gameplayBus.localLobby.GetLocalLobbyWeather().windDirection
                };
                GlobalSO.PlayFieldSO.SetWind(weatherData);
                ActionDispatcher.Dispatch(new SetWindAction(weatherData));

                GlobalSO.PlayerInfoSO.UpdateAvailability(UserAvailability.InMatch);

                PreviewCourse();
            }
        }
        
        public override void OnGameStart()
        {
            _gameplayBus = GlobalSO.GameplayBus;
            _ = _gameplayBus.lobbyHandler.SyncPlayerData(_gameplayBus.localPlayer);
        }
        
        public override void OnEndGame()
        {
            if (gameEnded) { return; }
            gameEnded = true;
            _manager.StartCoroutine(CoEndGame());
        }
        
        public override void OnSelfTakeShot(LocalPlayer self, Ball ball, GolfShotData input, BallTrajectory trajectory)
        {
            InputParam inputParam = InputParamHelper.Get(input); 
            Debug.Log("Self shot param: " + JsonConvert.SerializeObject(inputParam));

            if (input.GearData != null)
            {
                if (input.GearData.type != GearType.Trial && input.GearData.type != GearType.DownPoint)
                {
                    self.SetStrokeCount(self.GetStrokeCount() + 1);
                }
            }
            else
            {
                self.SetStrokeCount(self.GetStrokeCount() + 1);
            }
            Debug.Log("Self shot param stroke: " + self.GetStrokeCount());

            self.SetInputParam(inputParam);
            self.SetBallPos(new Vector3((float)inputParam.BallPositionX, (float)inputParam.BallPositionY, (float)inputParam.BallPositionZ));
            self.SetUserStatus(PlayerStatus.HitBall);

            cachedLocalinput = input;

            AddGearUsage(input.GearData);
            AddOneUseConsumedOnHitGearUsage(input.GearData, isLocal: true);
            AddBallToConsumeList(input.BallData.id.ToUpper());           
            localUnchangeableUponPickGear.Clear(); localUnchangeableUponPickGear = new Dictionary<GearType, GearData>();

            if (_timerCoroutine != null)
            {
                _manager.StopCoroutine(_timerCoroutine);
            }
            
            ActionDispatcher.Dispatch(new LocalPlayerHitBall(input));
            _ = _gameplayBus.lobbyHandler.SyncPlayerData(self);

            RegisterShotData(self, ball, input, trajectory);
        }
        
        public override void OnBallLanded(Ball ball, BallTrajectory trajectory)
        {
            if (ball.IsLocalBall())
            {
                var strokeCount = ball.localPlayer.GetStrokeCount();
                
                _gameStats.longestShotDistance = Mathf.Max(_gameStats.longestShotDistance, (float)trajectory.GetTotalDistance());
                _gameStats.strokeCount = strokeCount;
                _gameStats.score = strokeCount - GlobalSO.PlayFieldSO.HoleInfo.Par;
            }

            if (ball.IsPenalty && ball.IsLocalBall())
            {
                AddBallToConsumeList(GlobalSO.PlayerGadget.GetCurrentBallId(), ball.IsPenalty);
            }
            
            if (trajectory.RestingSurfaceType == SurfaceType.HoleCup)
            {
                if (ball.IsLocalBall())
                {
                    _gameStats.isHoleFinished = true;
                    ActionDispatcher.Dispatch(new CameraOrbitHoleToken(ball, trajectory));
                }
                else
                {
                    if (_balls[MasterManager.Instance.CurrentBallInCamera].localPlayer.GetId() != localBall.localPlayer.GetId())
                    {
                        // currently spectating opponent
                        ActionDispatcher.Dispatch(new CameraOrbitHoleToken(ball, trajectory));
                    }
                    else
                    {
                        AddBallToFinishList(ball, trajectory);
                        CalculateResult(ball, trajectory);
                    }
                }
            }
            else
            {
                AddBallToFinishList(ball, trajectory);
                CalculateResult(ball, trajectory);
            }
        }

        public override void OnInHoleCinemaFinished(Ball ball, BallTrajectory trajectory)
        {
            AddBallToFinishList(ball, trajectory);
            CalculateResult(ball, trajectory);
        }

        /// <summary>
        /// Add ball to finished (in hole) list depends on game status
        /// </summary>
        /// <param name="ball"></param>
        /// <param name="trajectory"></param>
        private void AddBallToFinishList(Ball ball, BallTrajectory trajectory)
        {
            // add this ball to list of ball in hole if it is in the hole
            if (!isTieBreak)
            {
                if (ball.IsBallInHole)
                {
                    _inHoleBalls.Add(ball);
                }
            }
            else
            {
                _finishedTieBreakBalls.Add(ball);
                if (ball.IsBallInHole)
                {
                    _inHoleTieBreakBalls.Add(ball);
                }
            }
        }

        /// <summary>
        /// Determine result
        /// </summary>
        /// <param name="ball"></param>
        /// <param name="trajectory">trajectory is null means the result is calculated when stroke timer
        /// runs out</param>
        private void CalculateResult(Ball ball, BallTrajectory trajectory, bool clockTimeOut = false)
        {
            ball.StopMoving();

            if (!isTieBreak)
            {
                // count all players stroke count
                int localPlayerStrokeCount = 0;
                int opponentStrokeCount = 0;

                foreach (Ball itrBall in _balls)
                {
                    if (itrBall.localPlayer.GetId().Equals(_gameplayBus.localPlayer.GetId()))
                    {
                        // local ball
                        localPlayerStrokeCount = itrBall.localPlayer.GetStrokeCount();
                        if (itrBall.IsMoving)
                        {
                            localPlayerStrokeCount = localPlayerStrokeCount - 1;
                        }
                    }
                    else
                    {
                        // opponent ball
                        opponentStrokeCount = itrBall.localPlayer.GetStrokeCount();
                        if (itrBall.IsMoving)
                        {
                            opponentStrokeCount = opponentStrokeCount - 1;
                        }
                    }
                }

                int completedBallCount = _inHoleBalls.Count;

                // check if end game and game result
                if (completedBallCount == 0)
                {
                    CheckIfLocalContinueHitting(ball, trajectory, clockTimeOut);
                    CheckIfOpponentContinueHitting(ball, trajectory);

                    Debug.Log(String.Format("Balls in hole: {0}. Stroke (local/oppo): {1}/{2}.",
                        completedBallCount, localPlayerStrokeCount, opponentStrokeCount)
                    );
                }
                else if (completedBallCount == 1)
                {
                    Ball ballInHole = _inHoleBalls[0];
                    bool localBallInHole = ballInHole.localPlayer.GetId().Equals(_gameplayBus.localPlayer.GetId());

                    if (localPlayerStrokeCount == opponentStrokeCount)
                    {
                        if (localBallInHole)
                        {
                            // local won
                            GenerateResult(localWins: true);

                            SetFinishGameForLandedBall(ball);

                            if (!skipShotAfterWinning)
                            {
                                if (ball.IsLocalBall())
                                {
                                    MasterManager.Instance.ChangeFromLocalToOpponentSpectate(_balls, localBall);
                                }
                            }
                            else
                            {
                                OnEndGame();
                            }

                            Debug.Log(String.Format("Balls in hole: {0}. Stroke (local/oppo): {1}/{2}.",
                                completedBallCount, localPlayerStrokeCount, opponentStrokeCount)
                            );

                            return;
                        }
                        else
                        {
                            // opponent won
                            GenerateResult(localWins: false);

                            SetFinishGameForLandedBall(ball);

                            if (skipShotAfterWinning)
                            {
                                OnEndGame();
                            }

                            Debug.Log(String.Format("Balls in hole: {0}. Stroke (local/oppo): {1}/{2}.",
                                completedBallCount, localPlayerStrokeCount, opponentStrokeCount)
                            );

                            return;
                        }
                    }
                    else if (localPlayerStrokeCount > opponentStrokeCount)
                    {
                        if (localBallInHole)
                        {
                            // local in hole but more stroke than opponent, wait for opponent to finish
                            if (ball.IsLocalBall())
                            {
                                SetFinishGameForLandedBall(ball);
                            }

                            CheckIfOpponentContinueHitting(ball, trajectory);

                            if (ball.IsLocalBall())
                            {
                                MasterManager.Instance.ChangeFromLocalToOpponentSpectate(_balls, localBall);
                            }

                            Debug.Log(String.Format("Balls in hole: {0}. Stroke (local/oppo): {1}/{2}.",
                                completedBallCount, localPlayerStrokeCount, opponentStrokeCount)
                            );

                            return;
                        }
                        else
                        {
                            // opponent won
                            GenerateResult(localWins: false);

                            SetFinishGameForLandedBall(ball);

                            if (skipShotAfterWinning)
                            {
                                OnEndGame();
                            }

                            Debug.Log(String.Format("Balls in hole: {0}. Stroke (local/oppo): {1}/{2}.",
                                completedBallCount, localPlayerStrokeCount, opponentStrokeCount)
                            );

                            return;
                        }
                    }
                    else if (localPlayerStrokeCount < opponentStrokeCount)
                    {
                        if (localBallInHole)
                        {
                            // local won
                            GenerateResult(localWins: true);

                            SetFinishGameForLandedBall(ball);

                            if (!skipShotAfterWinning)
                            {
                                if (ball.IsLocalBall())
                                {
                                    MasterManager.Instance.ChangeFromLocalToOpponentSpectate(_balls, localBall);
                                }
                            }
                            else
                            {
                                OnEndGame();
                            }

                            Debug.Log(String.Format("Balls in hole: {0}. Stroke (local/oppo): {1}/{2}.",
                                completedBallCount, localPlayerStrokeCount, opponentStrokeCount)
                            );

                            return;
                        }
                        else
                        {
                            // opponent in hole but more stroke than local, wait for local to finish
                            if (!ball.IsLocalBall())
                            {
                                SetFinishGameForLandedBall(ball);
                            }

                            CheckIfLocalContinueHitting(ball, trajectory, clockTimeOut);

                            Debug.Log(String.Format("Balls in hole: {0}. Stroke (local/oppo): {1}/{2}.",
                                completedBallCount, localPlayerStrokeCount, opponentStrokeCount)
                            );

                            return;
                        }
                    }
                }
                else if (completedBallCount == 2)
                {
                    if (localPlayerStrokeCount == opponentStrokeCount)
                    {
                        // tie break
                        isTieBreak = true;

                        ProcessTieBreak(ball, trajectory);

                        Debug.Log(String.Format("Balls in hole: {0}. Stroke (local/oppo): {1}/{2}.",
                            completedBallCount, localPlayerStrokeCount, opponentStrokeCount)
                        );

                        return;
                    }
                    else if (localPlayerStrokeCount > opponentStrokeCount)
                    {
                        // opponent won
                        GenerateResult(localWins: false);

                        SetFinishGameForLandedBall(ball);

                        Debug.Log(String.Format("Balls in hole: {0}. Stroke (local/oppo): {1}/{2}.",
                            completedBallCount, localPlayerStrokeCount, opponentStrokeCount)
                        );

                        return;
                    }
                    else if (localPlayerStrokeCount < opponentStrokeCount)
                    {
                        // local won
                        GenerateResult(localWins: true);

                        SetFinishGameForLandedBall(ball);

                        Debug.Log(String.Format("Balls in hole: {0}. Stroke (local/oppo): {1}/{2}.",
                            completedBallCount, localPlayerStrokeCount, opponentStrokeCount)
                        );

                        return;
                    }
                }
            }
            else
            {
                if (ball.IsLocalBall())
                {
                    localTieBreakDistanceToHole =
                        (ball.gameObject.transform.position - _holePosition).magnitude;
                }
                else
                {
                    oppoTieBreakDistanceToHole =
                        (ball.gameObject.transform.position - _holePosition).magnitude;
                }

                if (_finishedTieBreakBalls.Count == 2)
                {
                    Ball _localBall = null;
                    Ball _oppoBall = null;

                    foreach (var _ball in _balls)
                    {
                        if (_ball.IsLocalBall())
                        {
                            _localBall = _ball;
                        }
                        else
                        {
                            _oppoBall = _ball;
                        }
                    }

                    int inHoleCount = _inHoleTieBreakBalls.Count;

                    switch (inHoleCount)
                    {
                        case 0:
                            {
                                if (localTieBreakDistanceToHole <= oppoTieBreakDistanceToHole)
                                {
                                    result = new MultiPlayerWinUIResultData
                                    {
                                        Winner = _localBall,
                                        Loser = _oppoBall,
                                        CurrentLobbyProperty = _gameplayBus.currentLobbyProperty,
                                        localTieBreakDistanceToHole = localTieBreakDistanceToHole,
                                        oppoTieBreakDistanceToHole = oppoTieBreakDistanceToHole,
                                        isTieBreak = true,
                                        isTieBreakTie = false,
                                    };
                                }
                                else
                                {
                                    result = new MultiPlayerWinUIResultData
                                    {
                                        Winner = _oppoBall,
                                        Loser = _localBall,
                                        CurrentLobbyProperty = _gameplayBus.currentLobbyProperty,
                                        localTieBreakDistanceToHole = localTieBreakDistanceToHole,
                                        oppoTieBreakDistanceToHole = oppoTieBreakDistanceToHole,
                                        isTieBreak = true,
                                        isTieBreakTie = false,
                                    };
                                }

                                break;
                            }
                        case 1: 
                            {
                                Ball ballInHole = _inHoleTieBreakBalls[0];
                                bool localBallInHole = ballInHole.localPlayer.GetId().Equals(_gameplayBus.localPlayer.GetId());

                                if (localBallInHole)
                                {
                                    result = new MultiPlayerWinUIResultData
                                    {
                                        Winner = _localBall,
                                        Loser = _oppoBall,
                                        CurrentLobbyProperty = _gameplayBus.currentLobbyProperty,
                                        localTieBreakDistanceToHole = localTieBreakDistanceToHole,
                                        oppoTieBreakDistanceToHole = oppoTieBreakDistanceToHole,
                                        isTieBreak = true,
                                        isTieBreakTie = false,
                                    };
                                }
                                else
                                {
                                    result = new MultiPlayerWinUIResultData
                                    {
                                        Winner = _oppoBall,
                                        Loser = _localBall,
                                        CurrentLobbyProperty = _gameplayBus.currentLobbyProperty,
                                        localTieBreakDistanceToHole = localTieBreakDistanceToHole,
                                        oppoTieBreakDistanceToHole = oppoTieBreakDistanceToHole,
                                        isTieBreak = true,
                                        isTieBreakTie = false,
                                    };
                                }

                                break;
                            }
                        case 2: 
                            {
                                result = new MultiPlayerWinUIResultData
                                {
                                    Winner = _localBall,
                                    Loser = _oppoBall,
                                    CurrentLobbyProperty = _gameplayBus.currentLobbyProperty,
                                    localTieBreakDistanceToHole = localTieBreakDistanceToHole,
                                    oppoTieBreakDistanceToHole = oppoTieBreakDistanceToHole,
                                    isTieBreak = true,
                                    isTieBreakTie = true,
                                };
                                break;
                            }
                    }
                }

                SetFinishGameForLandedBall(ball);
            }
        }

        #region On Ball Landed (Normal)
        /// <summary>
        /// Check if local can continue hitting
        /// </summary>
        /// <param name="ball"></param>
        private void CheckIfLocalContinueHitting(Ball ball, BallTrajectory trajectory, bool clockTimeOut = false)
        {
            if (!ball.IsBallInHole && ball.IsLocalBall())
            {
                var localPlayer = ball.localPlayer;

                if (localPlayer.GetId() == _gameplayBus.localPlayer.GetId())
                {
                    _manager.StartCoroutine(CinematicsTilNextShot(ball, trajectory, clockTimeOut));
                }
            }
        }

        private IEnumerator CinematicsTilNextShot(Ball ball, BallTrajectory trajectory, bool clockTimeOut = false)
        {
            yield return new WaitForSeconds(1.0f);

            if (ball.IsPenalty)
            {
                yield return new WaitForSeconds(1.5f);
                ActionDispatcher.Dispatch(new ResetBallLastPositionAction(ball.localPlayer.GetId(), isTieBreak: false));
            }

            if (ball.IsLocalBall())
            {
                if (cachedLocalinput?.GearData != null)
                {
                    if (cachedLocalinput.GearData.type == GearType.Trial)
                    {
                        ActionDispatcher.Dispatch(new ResetBallLastPositionAction(ball.localPlayer.GetId(), isTieBreak: false));
                    }
                }
            }
            else
            {
                if (cachedOppoInput?.GearData != null)
                {
                    if (cachedOppoInput.GearData.type == GearType.Trial)
                    {
                        ActionDispatcher.Dispatch(new ResetBallLastPositionAction(ball.localPlayer.GetId(), isTieBreak: false));
                    }
                }
            }

            ActionDispatcher.Dispatch(new PrepShotAction(ball));

            yield return new WaitForSeconds(1.5f);

            // move camera to top view
            ActionDispatcher.Dispatch(new MoveCameraForNextShotAction(ball, trajectory));

            yield return new WaitForSeconds(0.5f);

            if (ball.IsLocalBall())
            {
                ActionDispatcher.Dispatch(new ContinueShotAction());

                ball.localPlayer.SetUserStatus(PlayerStatus.ReadyForShot);

                _ = _gameplayBus.lobbyHandler.SyncPlayerData(ball.localPlayer);

                // Setting ReadyForShot status for player whose shot clock timed out does not restart the timer
                // because player status never changed from ReadyForShot to HitBall => no event trigger
                // so a bool is used to restart the timer manually
                if (clockTimeOut)
                {
                    _timerCoroutine = _manager.StartCoroutine(CoShotCountdown(ball.localPlayer, () => OnShotClockExpire(ball.localPlayer)));
                }

                yield break;
            }

            if (ball.localPlayer.IsBot && IsSelfHost)
            {
                ball.localPlayer.SetUserStatus(PlayerStatus.ReadyForShot);
            }
        }

        /// <summary>
        /// Check if opponent is a bot and can continue hitting
        /// </summary>
        /// <param name="ball"></param>
        private void CheckIfOpponentContinueHitting(Ball ball, BallTrajectory trajectory)
        {
            var player = ball.localPlayer;

            if (player.IsBot && IsSelfHost)
            {
                _manager.StartCoroutine(CinematicsTilNextShot(ball, trajectory));
                return;
            }
            // opponent
            if (player.GetId() != GlobalSO.GameplayBus.localPlayer.GetId())
            {
                _manager.StartCoroutine(CinematicsTilNextShot(ball, trajectory));

                return;
            }
        }

        /// <summary>
        /// set finish game for designated player, both player finish game will end the game for this lobby
        /// </summary>
        /// <param name="ball"></param>
        /// <param name="setForLocal"></param>
        private void SetFinishGameForLandedBall(Ball ball)
        {
            var player = ball.localPlayer;

            if (ball.IsLocalBall())
            {
                player.SetUserStatus(PlayerStatus.FinishGame);
                _ = _gameplayBus.lobbyHandler.SyncPlayerData(ball.localPlayer);
            }
            else
            {
                if (player.IsBot && IsSelfHost)
                {
                    player.SetUserStatus(PlayerStatus.FinishGame);
                }
            }
        }

        private void GenerateResult(bool localWins)
        {
            Ball local = null; Ball opponent = null;
            foreach (var ball in _balls)
            {
                if (ball.localPlayer.GetId().Equals(_gameplayBus.localPlayer.GetId()))
                {
                    // this ball is local
                    local = ball;
                }
                else
                {
                    // this ball is opponent
                    opponent = ball;
                }
            }
            result = new MultiPlayerWinUIResultData
            {
                Winner = localWins ? local : opponent,
                Loser = localWins ? opponent : local,
                CurrentLobbyProperty = _gameplayBus.currentLobbyProperty
            };
        }
        #endregion

        #region On Ball Landed (TieBreak)
        private void ProcessTieBreak(Ball ball, BallTrajectory trajectory)
        {
            foreach (var _ball in _balls)
            {
                if (_ball.IsLocalBall())
                {
                    CheckIfLocalContinueHittingTieBreak(_ball, trajectory);
                }
                else
                {
                    CheckIfOpponentContinueHittingTieBreak(_ball);
                }
            }
        }

        private void CheckIfLocalContinueHittingTieBreak(Ball ball, BallTrajectory trajectory)
        {
            if (ball.IsLocalBall())
            {
                var localPlayer = ball.localPlayer;

                if (localPlayer.GetId() == _gameplayBus.localPlayer.GetId())
                {
                    _manager.StartCoroutine(CinematicsTilNextShotTieBreak(ball, trajectory));
                }
            }
        }

        private IEnumerator CinematicsTilNextShotTieBreak(Ball ball, BallTrajectory trajectory)
        {
            yield return new WaitForSeconds(1.0f);

            ActionDispatcher.Dispatch(new ResetBallLastPositionAction(ball.localPlayer.GetId(), isTieBreak: true));
            ActionDispatcher.Dispatch(new PrepShotAction(ball));
            ActionDispatcher.Dispatch(new HeadToHeadTieBreakAction());

            yield return new WaitForSeconds(1.5f);

            MasterManager.Instance.ChangeFromOpponentToLocalSpectate(_balls, localBall);
            ActionDispatcher.Dispatch(new MoveCameraForNextShotAction(ball, trajectory));

            yield return new WaitForSeconds(0.5f);

            ActionDispatcher.Dispatch(new ContinueShotAction());

            ball.localPlayer.SetUserStatus(PlayerStatus.ReadyForShot);
            _ = _gameplayBus.lobbyHandler.SyncPlayerData(ball.localPlayer);
        }

        /// <summary>
        /// Check if opponent is a bot and can continue hitting in tie break
        /// </summary>
        /// <param name="ball"></param>
        private void CheckIfOpponentContinueHittingTieBreak(Ball ball)
        {
            LocalPlayer player = ball.localPlayer;

            // bot
            if (player.IsBot && IsSelfHost)
            {
                ActionDispatcher.Dispatch(new PrepShotAction(ball));
                ball.ResetShotData();
                ball.gameObject.transform.position = GlobalSO.PlayFieldSO.Data.CurrentCtpTeeingArea.Position;

                // scale down opponent's ball to hide under local ball
                ball.Hide();

                player.SetUserStatus(PlayerStatus.ReadyForShot);
                return;
            }

            // opponent
            if (player.GetId() != GlobalSO.GameplayBus.localPlayer.GetId())
            {
                ActionDispatcher.Dispatch(new PrepShotAction(ball));
                ball.ResetShotData();

                // scale down opponent's ball to hide under local ball
                ball.Hide();
                return;
            }
        }
        #endregion

        public override bool IsWon(Vector3 ballPos, Vector3 holePos)
        {
            return true;
        }

        public override void OnReset()
        {
            _manager.StopAllCoroutines();
        }

        #region Disconnection
        public override void OnRelayDisconnected(LocalPlayer disconnectedPlayer)
        {
            ForceAPlayerToLose(disconnectedPlayer);
        }
        #endregion

        #region Flow End Game

        public override void OnForfeit(LocalPlayer whoseForfeit)
        {
            ForceAPlayerToLose(whoseForfeit);
        }

        private HeadToHeadMatchProperties headToHeadMatchProperties;

        private void ForceAPlayerToLose(LocalPlayer losingPlayer, bool isApplicationQuit = false)
        {
            headToHeadMatchProperties = new HeadToHeadMatchProperties();
            headToHeadMatchProperties.playerIds = new List<string>();
            headToHeadMatchProperties.gameStats = _gameStats;

            result = new MultiPlayerWinUIResultData
            {
                CurrentLobbyProperty = _gameplayBus.currentLobbyProperty,
                localTieBreakDistanceToHole = localTieBreakDistanceToHole,
                oppoTieBreakDistanceToHole = oppoTieBreakDistanceToHole,
                isTieBreak = false,
                isTieBreakTie = false,
            };

            foreach (var ball in _balls)
            {
                if (ball.localPlayer.GetId() == losingPlayer.GetId())
                {
                    result.Loser = ball;
                }
                else
                {
                    result.Winner = ball;
                }
                headToHeadMatchProperties.playerIds.Add(ball.localPlayer.GetId());
            }

            int i = 0;

            foreach (var player in headToHeadMatchProperties.playerIds)
            {
                if (player != losingPlayer.GetId())
                {
                    headToHeadMatchProperties.winnerIndex = i;
                }
                i++;
            }

            headToHeadMatchProperties.tourUuid = _gameplayBus.currentLobbyProperty.TourInfo.GetUuid();
            headToHeadMatchProperties.gearBonus = GetGearBonusData();
            
            _ = MasterManager.Instance.OnChangeScreen(EScreenEnum.Empty);

            if (isApplicationQuit == false)
            {
                ShowEndGame(result.Winner.localPlayer.GetId(), headToHeadMatchProperties);
            }
        }

        private GearBonusData GetGearBonusData()
        {
            if (result == null)
            {
                return new GearBonusData();
            }
            
            var gearData = new GearBonusData
            {
                strokeCount = _gameplayBus.localPlayer.GetStrokeCount(),
                par = GlobalSO.PlayFieldSO.HoleInfo.Par,
                gearIds = localOneUseConsumedOnHitGear.Select(item => item.Value.Id).ToList()
            };
            
            return gearData;
        }
        
        public override void OnAllTaskCompleted(List<Ball> balls, HoleBehaviour hole)
        {
            ActionDispatcher.Dispatch(new PopupCloseMessage());
            ActionDispatcher.Dispatch(new OnFinishGameAction());

            headToHeadMatchProperties = new HeadToHeadMatchProperties();
            headToHeadMatchProperties.playerIds = new List<string>();
            headToHeadMatchProperties.gameStats = _gameStats;
            headToHeadMatchProperties.tourUuid = _gameplayBus.currentLobbyProperty.TourInfo.GetUuid();
            headToHeadMatchProperties.gearBonus = GetGearBonusData();
            foreach (var ball in balls)
            {
                headToHeadMatchProperties.playerIds.Add(ball.localPlayer.GetId());
            }

            int i = 0;
            
            foreach (var player in headToHeadMatchProperties.playerIds)
            {
                if (player.Equals(result.Winner.localPlayer.GetId()))
                {
                    headToHeadMatchProperties.winnerIndex = i;
                }
                i++;
            }
            ShowEndGame(result.Winner.localPlayer.GetId(), headToHeadMatchProperties);
        }

        private void ShowEndGame(string winnerIndex, HeadToHeadMatchProperties matchProperties)
        {
            warningShotAction.TurnOn = false;
            ActionDispatcher.Dispatch(warningShotAction);

            ActionDispatcher.Dispatch(new PopupCloseMessage());
            RewardManager.OnCaculateResource?.Invoke(BallUsage, gearUsage, false);
            RewardManager.OnRewardWinners?.Invoke(winnerIndex, matchProperties);
            var isSelfWinner = result.Winner.localPlayer.GetId() == _gameplayBus.localPlayer.GetId();
            if (isSelfWinner)
            {
                ResultManager.OnShowMultiplayerResult?.Invoke(result);
            }
            else
            {
                ResultManager.OnShowMultiplayerLostResult?.Invoke(result);
            }
            TrackingManager.Instance.TrackH2hGameEnd(isSelfWinner);
        }

        #endregion
        
        
        public override void SetGamePlayLogicParam(GameLogicParam param)
        {
        }

        public override void OnSelfReadyForShot(LocalPlayer player)
        {
            var ball = _balls.FirstOrDefault(ball => ball.localPlayer.GetId() == player.GetId());

            _timerCoroutine = _manager.StartCoroutine(CoShotCountdown(player, () => OnShotClockExpire(player)));
        }
        
        public override void OnOtherPlayerReadyForShot(LocalPlayer player)
        {
        }
        
        public override void OnOtherPlayerTakeShot(LocalPlayer player)
        {
            InputParam inputParam = player.GetInputParam();
            
            var affectData = TransferAffectHelper.Get(inputParam.CurrentBall,
                inputParam.CurrentGear, inputParam.CurrentClub);
            
            GolfShotData shotData = new GolfShotData(
                BallPositionX: inputParam.BallPositionX, BallPositionY: inputParam.BallPositionY, BallPositionZ: inputParam.BallPositionZ,
                ForwardDirectionX: inputParam.ForwardDirectionX, ForwardDirectionY: inputParam.ForwardDirectionY, ForwardDirectionZ: inputParam.ForwardDirectionZ,
                Velocity: inputParam.Velocity,
                LaunchAngle: inputParam.LaunchAngle,
                SideAngle: inputParam.SideAngle,
                BackSpin: inputParam.BackSpin,
                SideSpin: inputParam.SideSpin,
                BallData: affectData.BallData,
                ClubData: null,
                ClubLevel: null,
                GearData: affectData.GearData
            );

            foreach (var ball in _balls)
            {
                if (ball.localPlayer.GetId() != player.GetId()) continue;

                // scale opponent's ball to normal size
                ball.Show();

                Debug.Log("Opponent hit ball: " + inputParam.CurrentBall + " / " + inputParam.CurrentClub + " / " + inputParam.CurrentGear);

                ActionDispatcher.Dispatch(new OpponentHitBallPrepAction(player, shotData, ball, false));
                
                AddOneUseConsumedOnHitGearUsage(shotData.GearData, isLocal: false);

                cachedOppoInput = shotData;

                break;
            }
        }
        
        public override void OnExit()
        {
            base.OnExit();
            EndGameAsync();
        }

        public override void OnBallFinished(Ball ball)
        {
        }
        
        public override void BotTakeShot(LocalPlayer bot)
        {
            _calculateBotShotTask = CalculateBotShotInputParam(bot);
            _manager.StartCoroutine(WaitForBotTakeShot(bot));
        }

        private IEnumerator WaitForBotTakeShot(LocalPlayer bot)
        {
            if (!isBotPutting)
            {
                yield return new WaitForSeconds(1.5f);
                bot.AddCommand(bot.GetStrokeCount(), new DragAimCommand(botAimPoint.x, botAimPoint.y, botAimPoint.z));
                yield return new WaitForSeconds(1.5f);
                bot.AddCommand(bot.GetStrokeCount(), new GoToRearViewCommand());
                yield return new WaitForSeconds(1.5f);
                bot.AddCommand(bot.GetStrokeCount(), new DragBallCommand(0.9f, -5f));
                yield return new WaitForSeconds(1.5f);
                bot.AddCommand(bot.GetStrokeCount(), new DragBallCommand(botRandomDragAccuracy, 0f));
                yield return new WaitForSeconds(1.5f);
            }
            else
            {
                yield return new WaitForSeconds(1.5f);
                bot.AddCommand(bot.GetStrokeCount(), new ChangeBallCommand("BALL_106"));
                yield return new WaitForSeconds(1.5f);
                bot.AddCommand(bot.GetStrokeCount(), new PuttBallCommand(-1f, -2.5f, 100f));
                yield return new WaitForSeconds(1.5f);
                bot.AddCommand(bot.GetStrokeCount(), new PuttBallCommand(0f, -3.5f, 100f));
                yield return new WaitForSeconds(1.5f);
                bot.AddCommand(bot.GetStrokeCount(), new PuttBallCommand(0.5f, -3f, 100f));
                yield return new WaitForSeconds(1.5f);
            }

            yield return _calculateBotShotTask.ToCoroutine();
            StartBotTakeShot(bot);
        }

        private void StartBotTakeShot(LocalPlayer bot)
        {
            bot.SetStrokeCount(bot.GetStrokeCount() + 1);
            bot.SetInputParam(botInputParam);
            bot.SetUserStatus(PlayerStatus.HitBall);
        }

        private InputParam botInputParam;
        private Vector3 botAimPoint;
        private bool isBotPutting;
        private float botRandomDragAccuracy;
        private float botRandomNeedleDegree;

        private async UniTask CalculateBotShotInputParam(LocalPlayer bot)
        {
            var ball = _balls.FirstOrDefault(ball => ball.localPlayer == bot);
            if (ball == null) return;

            var isPutting = ball.RestingSurfaceType == SurfaceType.Green; isBotPutting = isPutting;

            botInputParam = new InputParam();
            var aimPoint = AimHelper.FindInitialAimPoint(bot.BotLevelDefine.Gadget, ball, _holePosition, out var finalDistance);
            var ballPosition = ball.transform.position;

            botAimPoint = aimPoint;

            botInputParam.BallPositionX = ballPosition.x;
            botInputParam.BallPositionY = ballPosition.y;
            botInputParam.BallPositionZ = ballPosition.z;

            botInputParam.BackSpin = 0;
            botInputParam.SideSpin = 0;
            
            if (!isPutting)
            {
                botRandomDragAccuracy = bot.RandomDragAccuracy();
                botRandomNeedleDegree = bot.RandomNeedleDegree();

                var hashResult = AimHelper.HashLaunchAngleAndVelocity(aimPoint, ball.gameObject, finalDistance);
                botInputParam.LaunchAngle = hashResult.angle;
                botInputParam.Velocity = hashResult.velocity * botRandomDragAccuracy;
                botInputParam.SideAngle = botRandomNeedleDegree;
                var forwardDirection = (aimPoint - ballPosition).normalized;
                botInputParam.ForwardDirectionX = forwardDirection.x;
                botInputParam.ForwardDirectionY = forwardDirection.y;
                botInputParam.ForwardDirectionZ = forwardDirection.z;
            }
            else
            {
                botRandomDragAccuracy = bot.RandomDragAccuracy();
                botRandomNeedleDegree = bot.RandomNeedleDegree();

                aimPoint = _holePosition;
                var forwardDirection = (aimPoint - ballPosition).normalized;
                botInputParam.ForwardDirectionX = forwardDirection.x;
                botInputParam.ForwardDirectionY = forwardDirection.y;
                botInputParam.ForwardDirectionZ = forwardDirection.z;
                botInputParam.LaunchAngle = 1f;
                BallTrajectory ballTrajectory = null;
                var velocity = 0f;
                var sideAngle = 0f;
                if (finalDistance < Vector3.Distance(ballPosition, _holePosition))
                {
                    velocity = AimHelper.FindPuttingMaxVelocityInIdealCondition(finalDistance);
                }
                else
                {
                    const int maxLoopSteps = 20;
                    const float maxAngleAdjustment = 1f;
                    const float minAngleAdjustment = 0.1f;
                    var angleAdjustment = maxAngleAdjustment;
                    var isPrevRestPointLeftOfTheHole = false;
                    for (int i = 0; i < maxLoopSteps; i++)
                    {
                        if (ballTrajectory != null)
                        {
                            var lastPoint = ballTrajectory[^1];
                            var isRestPointLeftOfTheHole = GolfUtility.IsToTheLeftOfTwoPoint(ballPosition,
                                _holePosition, lastPoint.Position.ToVector3());
                            var isAdjustedTooMuch = isPrevRestPointLeftOfTheHole != isRestPointLeftOfTheHole;
                            if (isAdjustedTooMuch)
                            {
                                angleAdjustment = angleAdjustment / 2 > minAngleAdjustment
                                    ? angleAdjustment / 2
                                    : maxAngleAdjustment;
                            }
                            sideAngle += isRestPointLeftOfTheHole ? angleAdjustment : -angleAdjustment;
                            isPrevRestPointLeftOfTheHole = isRestPointLeftOfTheHole;
                        }

                        botInputParam.SideAngle = sideAngle;
                        var hashResult = await AimHelper.HashVelocityToReachHole(TrajectoryInput.ConvertFrom(botInputParam), _holePosition);
                        velocity = hashResult.velocity;
                        ballTrajectory = hashResult.trajectory;
                        
                        if (ballTrajectory.BallHoleStatus == BallHoleStatus.FallInHole)
                            break;
                    }
                }

                botInputParam.SideAngle = sideAngle + botRandomNeedleDegree;
                botInputParam.Velocity = velocity * botRandomDragAccuracy;
            }
        }
        #endregion

        private void OnHoleSpawn(HoleSpawnAction action)
        {
            _holePosition = action.holeBehaviour.transform.position;
        }
        
        private void OnGameStartToken(ReadyToPlayAction action)
        {
            _ = MasterManager.Instance.OnChangeScreen(EScreenEnum.GameplayScreen, null);
        }

        private IEnumerator CoEndGame()
        {
            yield return new WaitUntil(() => result != null);
            yield return new WaitForSeconds(3f);
            GolfTaskManager.OnAllBallCompleted?.Invoke();
        }
        
        private void OnShotClockExpire(LocalPlayer self)
        {
            self.SetStrokeCount(self.GetStrokeCount() + 1); 
            Debug.Log("Self shot param stroke (OT): " + self.GetStrokeCount());

            _ = _gameplayBus.lobbyHandler.SyncPlayerData(self);

            ActionDispatcher.Dispatch(new StrokeTimerTimeUp());
            ActionDispatcher.Dispatch(new PopupCloseMessage());

            Ball localBall = null;
            foreach (var ball in _balls)
            {
                if (ball.localPlayer.GetId() == self.GetId())
                {
                    localBall = ball;
                }
            }

            if (isTieBreak)
            {
                _finishedTieBreakBalls.Add(localBall);
            }
            CalculateResult(localBall, null, true);

            if (_timerCoroutine != null)
            {
                _manager.StopCoroutine(_timerCoroutine);
            }
        }

        private async void EndGameAsync()
        {
            UploadShotData(); ResetActivity();

            ActionDispatcher.Unbind(_holeSpawnToken);
            ActionDispatcher.Unbind(_gameStartToken);

            _manager.StopCoroutine(tickingUpdateLocal);
            _manager.StopCoroutine(tickingUpdateOpponent);

            await _gameplayBus.lobbyHandler.LeaveLobbyAsync();
            _gameplayBus.relayHandler.CluelessDisconnect();

            _gameplayBus.localPlayer.ResetPlayer();

            RegisterStageScreen();
            _manager.ReturnToTitle();
        }

        #region Count down coroutine
        private float currentCountdownTime;
        private SetShotClockAction setShotClockAction = new SetShotClockAction(0, 0, 0);
        private WarningShotAction warningShotAction = new WarningShotAction();

        public float GetCurrentCountdownTime()
        {
            return currentCountdownTime;
        }

        private IEnumerator CoShotCountdown(LocalPlayer player, Action callback = null)
        {
            bool isPastStopSpectate = false;

            float totalTime = _timeFromConfig;
            currentCountdownTime = _timeFromConfig;

            bool warningShot = false;
            bool islocal = localBall.localPlayer.GetId() == player.GetId();

            while (true)
            {
                currentCountdownTime -= Time.unscaledDeltaTime;

                if (currentCountdownTime < 5f)
                {
                    if (!isPastStopSpectate)
                    {
                        MasterManager.Instance.ChangeFromOpponentToLocalSpectate(_balls, localBall);

                        isPastStopSpectate = true;
                    }
                }

                if (currentCountdownTime < _timerWarningThreshold)
                {
                    if (!warningShot && islocal)
                    {
                        warningShotAction.TurnOn = true;
                        ActionDispatcher.Dispatch(warningShotAction);
                        warningShot = true;
                    }
                }

                setShotClockAction.RemainTime = currentCountdownTime;
                setShotClockAction.TotalTime = totalTime;
                setShotClockAction.StrokeCount = player.GetStrokeCount();
                ActionDispatcher.Dispatch(setShotClockAction);

                if (currentCountdownTime <= 0)
                {
                    callback?.Invoke();
                    warningShotAction.TurnOn = false;
                    ActionDispatcher.Dispatch(warningShotAction);
                    break;
                }

                yield return null;
            }
        }
        #endregion

        #region Ticking update players' action
        private IEnumerator tickingUpdateLocal;
        private bool needToUpdateLocal = false;

        private IEnumerator TickingUpdateLocal()
        {
            while (true)
            {
                yield return new WaitForSeconds(1);

                if (!needToUpdateLocal) { continue; }

                Debug.Log("[Local Sync Stroke] " + SelfPlayer.GetStrokeCount());
                _ = _gameplayBus.lobbyHandler.SyncPlayerData(SelfPlayer, OnUpdateLocalPlayerSuccess, OnUpdateLocalPlayerFailed);

                needToUpdateLocal = false;
            }
        }

        private void OnUpdateLocalPlayerSuccess()
        {
            // clear current lobby command from local player
            SelfPlayer.ClearLobbyCommand();

            needToUpdateLocal = false;
        }

        private void OnUpdateLocalPlayerFailed()
        {
            needToUpdateLocal = true;
        }

        public override void OnPlayerSync(PlayerCommand playerCommand)
        {
            Debug.Log("OnPlayerSync");
            SelfPlayer.AddCommand(SelfPlayer.GetStrokeCount(), playerCommand);
            SelfPlayer.AddLobbyCommand(SelfPlayer.GetStrokeCount(), playerCommand);
            needToUpdateLocal = true;
        }

        List<int> localOpponentActionTracker;

        private IEnumerator tickingUpdateOpponent;

        private IEnumerator TickingUpdateOpponent()
        {
            while (true)
            {
                yield return new WaitForSeconds(1);

                int opponentStrokeCount = 0;
                LocalPlayer opponent = null;
                Ball opponentBall = null;

                foreach (var itrBall in _balls)
                {
                    if (!itrBall.IsLocalBall())
                    {
                        opponentBall = itrBall;
                        opponent = itrBall.localPlayer;
                        opponentStrokeCount = opponent.GetStrokeCount();
                        break;
                    }
                }

                if (opponentBall.IsMoving)
                {
                    Debug.Log("[TickingUpdateOpponent]: Opponent ball is in trajectory, do not receive commands.");
                    continue;
                }
                else
                {
                    List<List<PlayerCommand>> lobbyOpponentCommands = opponent.GetPlayerCommands();

                    int localOpponentStrokeProgress = 0;

                    if (opponentStrokeCount < 0 || opponentStrokeCount >= localOpponentActionTracker.Count)
                    {
                        int difference = opponentStrokeCount - localOpponentActionTracker.Count + 1;

                        for (int i = 0; i < difference; i++)
                        {
                            localOpponentActionTracker.Add(0);
                        }
                    }
                    localOpponentStrokeProgress = localOpponentActionTracker[opponentStrokeCount];
                    
                    if (opponentStrokeCount >= lobbyOpponentCommands.Count)
                    {
                        Debug.Log(String.Format("[TickingUpdateOpponent]: Opponent commands have no data for this stroke {0}/{1}", opponentStrokeCount, lobbyOpponentCommands.Count));
                        continue;
                    }
                    else
                    {
                        if (localOpponentStrokeProgress >= 0 && localOpponentStrokeProgress < lobbyOpponentCommands[opponentStrokeCount].Count)
                        {
                            Debug.Log(String.Format("[TickingUpdateOpponent]: Local stroke progress in range of lobby data {0}/{1}", localOpponentStrokeProgress, lobbyOpponentCommands[opponentStrokeCount].Count));
                            
                            List<PlayerCommand> dragAndPuttBallCommands = new List<PlayerCommand>();
                            List<PlayerCommand> dragAimCommands = new List<PlayerCommand>();
                            List<PlayerCommand> topAndRearViewCommands = new List<PlayerCommand>();
                            List<PlayerCommand> changeBallCommands = new List<PlayerCommand>();

                            for (int i = lobbyOpponentCommands[opponentStrokeCount].Count - 1; i >= localOpponentStrokeProgress; i--)
                            {
                                localOpponentActionTracker[opponentStrokeCount]++; // increase progress by 1

                                if (lobbyOpponentCommands[opponentStrokeCount][i] is DragBallCommand || lobbyOpponentCommands[opponentStrokeCount][i] is PuttBallCommand)
                                {
                                    dragAndPuttBallCommands.Add(lobbyOpponentCommands[opponentStrokeCount][i]);
                                    continue;
                                }
                                if (lobbyOpponentCommands[opponentStrokeCount][i] is GoToRearViewCommand || lobbyOpponentCommands[opponentStrokeCount][i] is GoToTopViewCommand)
                                {
                                    topAndRearViewCommands.Add(lobbyOpponentCommands[opponentStrokeCount][i]);
                                    continue;
                                }
                                if (lobbyOpponentCommands[opponentStrokeCount][i] is DragAimCommand)
                                {
                                    dragAimCommands.Add(lobbyOpponentCommands[opponentStrokeCount][i]);
                                    continue;
                                }
                                if (lobbyOpponentCommands[opponentStrokeCount][i] is ChangeBallCommand)
                                {
                                    changeBallCommands.Add(lobbyOpponentCommands[opponentStrokeCount][i]);
                                    continue;
                                }
                            }

                            if (dragAndPuttBallCommands.Count > 0)
                            {
                                ActionDispatcher.Dispatch(new SyncOpponentAction(dragAndPuttBallCommands[0]));
                            }
                            if (topAndRearViewCommands.Count > 0)
                            {
                                ActionDispatcher.Dispatch(new SyncOpponentAction(topAndRearViewCommands[0]));
                            }
                            if (dragAimCommands.Count > 0)
                            {
                                ActionDispatcher.Dispatch(new SyncOpponentAction(dragAimCommands[0]));
                            }
                            if (changeBallCommands.Count > 0)
                            {
                                ActionDispatcher.Dispatch(new SyncOpponentAction(changeBallCommands[0]));
                            }
                            //
                        }
                        else
                        {
                            Debug.Log(String.Format("[TickingUpdateOpponent]: Already at the end of commands list for this stroke {0}/{1}", localOpponentStrokeProgress, lobbyOpponentCommands[opponentStrokeCount].Count));
                            continue;
                        }
                    }
                }
            }
        }
        #endregion

        /// <summary>
        /// In case game is shutdown abruptly, H2H to-cache-list:
        /// - Gear (Yes)
        /// - Shots (Yes)
        /// - Result (Yes) [Specifically lost result]
        /// </summary>
        public override void OnApplicationQuit()
        {
            // handle gear usage
            RewardManager.OnCaculateResource?.Invoke(BallUsage, gearUsage, true);

            // handle shots
            CryptoHelper.AESEncryptedText encryptedShotDatas = CryptoHelper.Encrypt(JsonConvert.SerializeObject(golfShotDatas), CryptoHelper.Password);

            PlayerPrefs.SetString(CryptoHelper.EncryptedTextKey1, encryptedShotDatas.EncryptedText);
            PlayerPrefs.SetString(CryptoHelper.EncryptedIV1, encryptedShotDatas.IV);

            // handle result
            if (result == null)
            {
                // result is not determined yet, force to lose
                ForceAPlayerToLose(localBall.localPlayer, isApplicationQuit: true);
            }

            CryptoHelper.AESEncryptedText encryptedResultDatas = CryptoHelper.Encrypt(JsonConvert.SerializeObject(headToHeadMatchProperties), CryptoHelper.Password);

            PlayerPrefs.SetString(CryptoHelper.EncryptedTextKey2, encryptedResultDatas.EncryptedText);
            PlayerPrefs.SetString(CryptoHelper.EncryptedIV2, encryptedResultDatas.IV);
        }
    }
}
