using System.Collections.Generic;
using System.Linq;
using _Golf.Scripts.Common.Helper;
using _Golf.Scripts.Core;
using _Golf.Scripts.PlayField;
using _Golf.Scripts.ScriptableObjects.Tours;
using GolfPhysics;
using UnityEngine;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;
using UnityEngine.Splines;
using Random = UnityEngine.Random;

namespace _Golf.Scripts.ScriptableObjects.PlayField
{
    [CreateAssetMenu(fileName = "PlayFieldSO", menuName = "ScriptableObjects/PlayFieldSO")]
    public class PlayFieldScriptableObject : ScriptableObject
    {
        public Scripts.PlayField.PlayField Data = new Scripts.PlayField.PlayField();
        public HoleInfo HoleInfo { get; private set; }
        public Course Course { get; private set; }

        private SplineContainer _bound;

        public SplineContainer Bound => _bound;

        private Dictionary<Collider, Renderer> _treeRenderers;

        public void Init(GameObject teeContainer, GameObject ctpTeeTransform, GameObject pinContainer,
            GameObject fairwayPointContainer
            , GameObject reviewPointContainer, GameObject cameraAnim = null, GameObject bound = null,
            GameObject localVolume = null)
        {
            Data.Reset();
            InitTees(teeContainer);
            InitCtpTees(ctpTeeTransform);
            InitPins(pinContainer);
            InitFairwayPath(fairwayPointContainer);
            InitReviewPath(reviewPointContainer);
            if (cameraAnim != null)
            {
                InitCameraAnimation(cameraAnim);
            }

            _treeRenderers = new Dictionary<Collider, Renderer>();

            if (bound != null) _bound = bound.GetComponent<SplineContainer>();

            Data.LocalVolume = localVolume.GetComponent<Volume>();
            Data.OriginalPPColorAdjustmentValue = GetLocalVolumeColorAdjustment()?.postExposure.value ?? 0f;
        }

        public float GetOriginalPPColorAdjustmentValue()
        {
            return Data.OriginalPPColorAdjustmentValue;
        }

        public ColorAdjustments GetLocalVolumeColorAdjustment()
        {
            Volume volume = Data.LocalVolume;
            if (volume.profile.TryGet<ColorAdjustments>(out var colorAdjustments))
            {
                return colorAdjustments;
            }
            return null;
        }

        public void AdjustLocalVolumePostExposure(float targetValue)
        {
            Volume volume = Data.LocalVolume;
            if (volume.profile.TryGet<ColorAdjustments>(out var colorAdjustments))
            {
                colorAdjustments.postExposure.overrideState = true;
                colorAdjustments.postExposure.value = targetValue;
            }
        }

        public void SetTreeRenderers(Dictionary<Collider, Renderer> values)
        {
            _treeRenderers = values;
        }

        public Dictionary<Collider, Renderer> GetTreeRenderers()
        {
            return _treeRenderers;
        }
        
        public float GetNearestDistance(Vector3 point)
        {
            if (_bound == null) return 0f;
            
            var localSplinePoint = _bound.transform.InverseTransformPoint(point.SetY(_bound.Spline.GetBounds().center.y));
            var nearestPoint = _bound.Spline.GetBounds().ClosestPoint(localSplinePoint);
            var distance = Vector3.Distance(localSplinePoint, nearestPoint);
            return distance;
        }
        
        public bool IsInBound(Vector3 point)
        {
            var pointVt3 = _bound.transform.InverseTransformPoint(point);
            var p = new Vector2(pointVt3.x, pointVt3.z);
            var polyPoints = _bound.Spline.Knots.Select(knot => new Vector2(knot.Position.x, knot.Position.z)).ToArray();
            var inside = false;
            var j = polyPoints.Length - 1;
            for (int i = 0; i < polyPoints.Length; j = i++)
            {
                var pi = polyPoints[i];
                var pj = polyPoints[j];
                if(((pi.y <= p.y && p.y < pj.y) || (pj.y <= p.y && p.y < pi.y)) &&
                   (p.x < (pj.x - pi.x) * (p.y - pi.y) / (pj.y - pi.y) + pi.x))
                inside = !inside;
            }
            return inside;
        }

        private HoleInfo GetHoleInfoByGuid(string guid)
        {
            var holeLists = GlobalSO.RemoteConfigData.HoleInfos;
            return holeLists.Find((holeInfo => holeInfo.Guid == guid));
        }

        public void SetCourseInfo(Course courseInfo)
        {
            Course = courseInfo;
        }

        public void SetHoleInfo(HoleInfo courseInfo)
        {
            HoleInfo = courseInfo;
        }

        public void SetHoleInfo(string guid)
        {
            var hole = GetHoleInfoByGuid(guid);
            HoleInfo = hole;
        }
        
        public void SetWind(WeatherData weatherData)
        {
            Data.Weather = weatherData;
            Data.Flag.UpdateWindAndShadow(weatherData);
        }

        private void InitCameraAnimation(GameObject cameraInScene)
        {
            var animator = cameraInScene.GetComponent<Animator>();
            if(animator != null)
            {
                Data.PreviewAnimator = animator.runtimeAnimatorController;
            }
        }
        
        private void InitTees(GameObject teeContainer)
        {
            if (teeContainer == null) return;
            
            var containerTransform = teeContainer.transform;
            for (var i = 0; i < containerTransform.childCount; i++)
            {
                var teeTransform = containerTransform.GetChild(i);
                RaycastHit hit;
                if (UnityEngine.Physics.Raycast(teeTransform.transform.position.OffsetY(500f), Vector3.down, out hit, Mathf.Infinity, Constant.GroundLayerMask))
                {
                    teeTransform.transform.position = hit.point + new Vector3(0, (float)Constant.radiusOfGolfBall_Metric, 0);
                }
                var teeingArea = teeTransform.gameObject.AddComponent<TeeingArea>();
                teeingArea.Init();
                Data.TeeingAreas.Add(teeingArea);
                
                if (teeTransform.gameObject.activeInHierarchy)
                    Data.CurrentTeeingArea ??= teeingArea;
            }
            var playerRank = GlobalSO.PlayerInfoSO.Info.H2hRank.MajorRank;
            var teeByRank = Data.TeeingAreas.FirstOrDefault(tee => (int)tee.Difficulty == (int)playerRank+1);
            if(teeByRank != null)
                Data.CurrentTeeingArea = teeByRank;
        }

        private void InitCtpTees(GameObject ctpTeeContainer)
        {
            if (ctpTeeContainer == null) return;
            
            var containerTransform = ctpTeeContainer.transform;
            for (var i = 0; i < containerTransform.childCount; i++)
            {
                var teeTransform = containerTransform.GetChild(i);
                RaycastHit hit;
                if (UnityEngine.Physics.Raycast(teeTransform.transform.position.OffsetY(500f), Vector3.down, out hit, Mathf.Infinity, Constant.GroundLayerMask))
                {
                    teeTransform.transform.position = hit.point + new Vector3(0, (float)Constant.radiusOfGolfBall_Metric, 0);
                }
                var teeingArea = teeTransform.gameObject.AddComponent<TeeingArea>();
                teeingArea.Init();
                Data.CtpTeeingAreas.Add(teeingArea);
            }
            
            
            var playerRank = GlobalSO.PlayerInfoSO.Info.H2hRank.MajorRank;
            var teeByRank = Data.CtpTeeingAreas.FirstOrDefault(tee => (int)tee.Difficulty == (int)playerRank+1);
            if(teeByRank != null)
            {
                Data.CurrentCtpTeeingArea = teeByRank;
            }
            else
            {
                var randomIndex = Random.Range(0, Data.CtpTeeingAreas.Count);
                Data.CurrentCtpTeeingArea = Data.CtpTeeingAreas[randomIndex];
            }
        }

        private void InitPins(GameObject pinContainer)
        {
            if (pinContainer == null) return;
            
            var isClosestToPinMode = GlobalSO.GameplayBus.currentLogic.logicData is SinglePlayerClosestToPinSO;
            
            var containerTransform = pinContainer.transform;
            for (var i = 0; i < containerTransform.childCount; i++)
            {
                var pinTransform = containerTransform.GetChild(i);
                var pin = pinTransform.gameObject.AddComponent<Pin>();
                pin.Init(i);
                Data.Pins.Add(pin);
                
                
                // level 2 is hole in center green
                if (isClosestToPinMode)
                {
                    if(pin.Difficulty == PinDifficulty.Level1)
                        Data.CurrentPin ??= pin;
                }
                else
                {
                    if (pinTransform.gameObject.activeInHierarchy)
                        Data.CurrentPin ??= pin;
                }
            }
        }

        private void InitFairwayPath(GameObject fairwayPointContainer)
        {
            if (fairwayPointContainer == null) return;
            
            var containerTransform = fairwayPointContainer.transform;
            for (var i = 0; i < containerTransform.childCount; i++)
            {
                var fairwayPoint = containerTransform.GetChild(i);
                Data.FairwayPath.Add(fairwayPoint.position);
            }
        }
        
        private void InitReviewPath(GameObject paths)
        {
            if (paths == null) return;
            
            var containerTransform = paths.transform;
            for (var i = 0; i < containerTransform.childCount; i++)
            {
                var fairwayPoint = containerTransform.GetChild(i);
                Data.ReviewPoint.Add(fairwayPoint);
            }
        }
    }
}