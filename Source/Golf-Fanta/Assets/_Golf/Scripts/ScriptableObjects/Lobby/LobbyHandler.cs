using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using _Golf.Scripts.Common;
using _Golf.Scripts.Common.Helper;
using _Golf.Scripts.Core;
using _Golf.Scripts.Networking.Photon.Lobby;
using Newtonsoft.Json;
using Unity.Services.Authentication;
using Unity.Services.Lobbies;
using Unity.Services.Lobbies.Models;
using Unity.Services.Multiplayer;
using Unity.Services.Relay.Models;
using UnityEngine;
using Avatar = _Golf.Scripts.Core.Avatar;

namespace _Golf.Scripts.Lobby
{
    public static class UnityLobbyDataKey
    {
        public const string k_Elo = "elo";
        public const string k_CourseId = "courseId";
    }

    public class LobbyHandler
    {
        private LobbyEventCallbacks lobbyEventCallbacks = new LobbyEventCallbacks();
        private Unity.Services.Lobbies.Models.Lobby remoteLobby;
        private LocalLobby localLobby;

        public LobbyHandler()
        {
            localLobby = new LocalLobby();
        }

        #region Create lobby
        public async Task<LocalLobby> CreateLobbyAsync(LobbyProperty lobbyProperty, LocalPlayer player)
        {
            var lobby = await CreateLobby(lobbyProperty, player);
            LobbyConverter.RemoteToLocal(lobby, localLobby);
            return localLobby;
        }

        private async Task<Unity.Services.Lobbies.Models.Lobby> CreateLobby(LobbyProperty lobbyProperty, LocalPlayer player)
        {
            CreateLobbyOptions options = new CreateLobbyOptions();
            string playerId = player.GetId();
            options.Player = new Player(id: playerId, data: CreateInitialPlayerData(player));

            options.Data = new Dictionary<string, DataObject>()
            {
                {
                    UnityLobbyDataKey.k_Elo, new DataObject(
                        visibility: DataObject.VisibilityOptions.Public,
                        // value: elo.ToString(),
                        value: "0",
                        index: DataObject.IndexOptions.S1)
                },
                {
                    UnityLobbyDataKey.k_CourseId, new DataObject(
                        visibility: DataObject.VisibilityOptions.Public,
                        value: lobbyProperty.CourseInfo.Id,
                        index: DataObject.IndexOptions.S2)
                },
            };

            string lobbyName = lobbyProperty.CourseInfo.Id + " - " + player.GetId().Substring(player.GetId().Length - 10);

            remoteLobby = await LobbyService.Instance.CreateLobbyAsync(lobbyName, 2, options);

            if (remoteLobby != null)
            {
                await BindLobbyRemote(remoteLobby.Id);
            }

            Debug.Log("Lobby created with ID: " + remoteLobby.Id);

            return remoteLobby;
        }
        #endregion

        #region Join Lobby
        public async Task<LocalLobby> JoinLobbyAsync(string courseId, LocalPlayer player)
        {
            var lobby = await JoinLobby(courseId, player);
            LobbyConverter.RemoteToLocal(lobby, localLobby);
            return localLobby;
        }

        private async Task<Unity.Services.Lobbies.Models.Lobby> JoinLobby(string courseId, LocalPlayer localPlayer)
        {
            var elo = localPlayer.GetPlayerRankInfo().currentPoint;

            // var matchingDiff = _ranks.Find(x => x.minElo <= elo && x.maxElo >= elo).machingDiff;
            // var lowerDiff = elo - matchingDiff;
            // var upperDiff = elo + matchingDiff;

            var queryFilter = new List<QueryFilter>()
            {
                new QueryFilter(
                    field: QueryFilter.FieldOptions.S1,
                    op: QueryFilter.OpOptions.GT,
                    // value: lowerDiff.ToString()),
                    value: "-1"),
                new QueryFilter(
                    field: QueryFilter.FieldOptions.S1,
                    op: QueryFilter.OpOptions.LT,
                    // value: upperDiff.ToString()),
                    value: "1"),
                new QueryFilter(field: QueryFilter.FieldOptions.S2,
                    op: QueryFilter.OpOptions.EQ,
                    value: courseId)
            };

            remoteLobby = null;

            Dictionary<string, PlayerDataObject> playerData = CreateInitialPlayerData(localPlayer);
            try
            {
                remoteLobby = await LobbyService.Instance.QuickJoinLobbyAsync(
                    new QuickJoinLobbyOptions()
                    {
                        Player = new Player(id: localPlayer.GetId(), data: playerData),
                        Filter = queryFilter
                    }
                );
            }
            catch (Exception e)
            {
                Debug.Log(e);
            }

            if (remoteLobby != null)
            {
                await BindLobbyRemote(remoteLobby.Id);
            }

            Debug.Log("Joined lobby with ID: " + remoteLobby?.Id);

            return remoteLobby;
        }
        #endregion

        public void SendHeartBeat(string lobbyId)
        {
            LobbyService.Instance.SendHeartbeatPingAsync(remoteLobby.Id);
        }

        public async Task DeleteLobby(string lobbyId)
        {
            if (localLobby == null || string.IsNullOrEmpty(localLobby.GetLobbyID())) return;
            await LobbyService.Instance.DeleteLobbyAsync(lobbyId);
            Dispose();
        }

        #region Update (Sync) Lobby Data
        public async Task SyncLobbyData(LocalLobby localLobby)
        {
            var data = LobbyConverter.LocalToRemoteLobbyData(localLobby);
            await UpdateLobbyDataAsync(data);
        }

        private async Task UpdateLobbyDataAsync(Dictionary<string, string> data)
        {
            Dictionary<string, DataObject> dataCurr = remoteLobby.Data ?? new Dictionary<string, DataObject>();

            var shouldLock = false;
            foreach (var dataNew in data)
            {
                // lobby filter color, but this logic does not make sense yet, to fix later if needed
                DataObject.IndexOptions index = dataNew.Key == LocalLobbyData.k_LobbyState ? DataObject.IndexOptions.N1 : 0;
                DataObject dataObj = new DataObject(DataObject.VisibilityOptions.Public, dataNew.Value, index);

                if (dataCurr.ContainsKey(dataNew.Key))
                {
                    dataCurr[dataNew.Key] = dataObj;
                }
                else
                {
                    dataCurr.Add(dataNew.Key, dataObj);
                }

                if (dataNew.Key == LocalLobbyData.k_LobbyState)
                {
                    Enum.TryParse(dataNew.Value, out LobbyState lobbyState);
                    shouldLock = lobbyState != LobbyState.Lobby;
                }
            }

            UpdateLobbyOptions updateOptions = new UpdateLobbyOptions
            {
                Data = dataCurr,
                IsLocked = shouldLock
            };

            try
            {
                remoteLobby = await LobbyService.Instance.UpdateLobbyAsync(remoteLobby.Id, updateOptions);
            }
            catch (Exception ex) when (ex is ArgumentNullException ||
                           ex is LobbyServiceException)
            {
                Debug.LogException(ex);
            }
        }

        public async Task LockLobby()
        {
            UpdateLobbyOptions updateOptions = new UpdateLobbyOptions
            {
                IsLocked = true
            };
            remoteLobby = await LobbyService.Instance.UpdateLobbyAsync(remoteLobby.Id, updateOptions);
        }
        #endregion

        #region Update (Sync) Player Data
        public async Task SyncPlayerData(LocalPlayer player, Action onSuccess = null, Action onFailed = null)
        {
            var data = LobbyConverter.LocalToRemoteUserData(player);
            await UpdatePlayerDataAsync(data, onSuccess, onFailed);
        }

        private async Task UpdatePlayerDataAsync(Dictionary<string, string> data, Action onSuccess = null, Action onFailed = null)
        {
            string playerId = AuthenticationService.Instance.PlayerId;

            Dictionary<string, PlayerDataObject> dataCurr = new Dictionary<string, PlayerDataObject>();

            foreach (var dataNew in data)
            {
                PlayerDataObject dataObj = new PlayerDataObject(visibility: PlayerDataObject.VisibilityOptions.Member,
                    value: dataNew.Value);

                if (dataCurr.ContainsKey(dataNew.Key))
                {
                    dataCurr[dataNew.Key] = dataObj;
                }
                else
                {
                    dataCurr.Add(dataNew.Key, dataObj);
                }
            }

            Debug.Log("lobby handler updates local player data!");

            UpdatePlayerOptions updateOptions = new UpdatePlayerOptions
            {
                Data = dataCurr,
            };

            try
            {
                remoteLobby = await LobbyService.Instance.UpdatePlayerAsync(remoteLobby.Id, playerId, updateOptions);

                onSuccess?.Invoke();
            }
            catch (Exception ex) when (ex is ArgumentNullException ||
                                       ex is LobbyServiceException)
            {
                onFailed?.Invoke();
            }
        }

        public async Task UpdatePlayerConnectionData(string relayJoinCode, string allocationId = null)
        {
            string playerId = AuthenticationService.Instance.PlayerId;

            Debug.Log("lobby handler updates local player connection info!");

            UpdatePlayerOptions updateOptions = new UpdatePlayerOptions
            {
                AllocationId = allocationId,
                ConnectionInfo = relayJoinCode
            };

            try
            {
                remoteLobby = await LobbyService.Instance.UpdatePlayerAsync(remoteLobby.Id, playerId, updateOptions);
            }
            catch (Exception ex) when (ex is ArgumentNullException ||
                                       ex is LobbyServiceException)
            {

            }
        }
        #endregion

        #region Lobby events
        private async Task<ILobbyEvents> BindLobbyRemote(string lobbyId)
        {
            lobbyEventCallbacks.LobbyChanged += OnLobbyDefaultDataChanged;
            lobbyEventCallbacks.DataChanged += OnLobbyCustomDataChanged;
            lobbyEventCallbacks.LobbyDeleted += OnLobbyDeleted;

            lobbyEventCallbacks.PlayerJoined += OnPlayerJoined;
            lobbyEventCallbacks.PlayerLeft += OnPlayerLeft;

            lobbyEventCallbacks.PlayerDataAdded += OnPlayerDataChanged;
            lobbyEventCallbacks.PlayerDataChanged += OnPlayerDataChanged;
            lobbyEventCallbacks.PlayerDataRemoved += OnPlayerDataChanged;

            return await LobbyService.Instance.SubscribeToLobbyEventsAsync(lobbyId, lobbyEventCallbacks);
        }

        private void OnLobbyDefaultDataChanged(ILobbyChanges changes)
        {
            if (changes.Name.Changed)
            {
                localLobby.SetLobbyName(changes.Name.Value);
                Debug.Log("Lobby Default Data Changed [Name]: " + changes.Name.Value);
            }
            if (changes.HostId.Changed)
            {
                localLobby.SetHostID(changes.HostId.Value);
                Debug.Log("Lobby Default Data Changed [HostId]: " + changes.HostId.Value);
            }
            if (changes.IsPrivate.Changed)
            {
                localLobby.SetPrivateStatus(changes.IsPrivate.Value);
                Debug.Log("Lobby Default Data Changed [IsPrivate]: " + changes.IsPrivate.Value);
            }
            if (changes.IsLocked.Changed)
            {
                localLobby.SetLockedValue(changes.IsLocked.Value);
                Debug.Log("Lobby Default Data Changed [IsLocked]: " + changes.IsLocked.Value);
            }
            if (changes.AvailableSlots.Changed)
            {
                localLobby.SetAvailableSlots(changes.AvailableSlots.Value);
                Debug.Log("Lobby Default Data Changed [AvailableSlots]: " + changes.AvailableSlots.Value);
            }
            if (changes.MaxPlayers.Changed)
            {
                localLobby.SetMaxPlayerCount(changes.MaxPlayers.Value);
                Debug.Log("Lobby Default Data Changed [MaxPlayers]: " + changes.MaxPlayers.Value);
            }

            ActionDispatcher.Dispatch(new LobbyChangedAction(changes));
        }

        private void OnLobbyCustomDataChanged(Dictionary<string, ChangedOrRemovedLobbyValue<DataObject>> changes)
        {
            foreach (KeyValuePair<string, ChangedOrRemovedLobbyValue<DataObject>> change in changes)
            {
                string changedKey = change.Key;
                ChangedOrRemovedLobbyValue<DataObject> changedValue = change.Value;

                switch (changedKey)
                {
                    case LocalLobbyData.k_LobbyWeather:
                        {
                            localLobby.SetLocalLobbyWeather(JsonConvert.DeserializeObject<LocalLobbyWeather>(changedValue.Value.Value));
                            Debug.Log("Lobby Custom Data Changed [Weather]: " + changedValue.Value.Value);
                            break;
                        }
                    case LocalLobbyData.k_LobbyTee:
                        {
                            localLobby.SetTee(int.Parse(changedValue.Value.Value));
                            Debug.Log("Lobby Custom Data Changed [Tee]: " + changedValue.Value.Value);
                            break;
                        }
                    case LocalLobbyData.k_LobbyCurrentHole:
                        {
                            localLobby.SetCurrentHole(int.Parse(changedValue.Value.Value));
                            Debug.Log("Lobby Custom Data Changed [Hole]: " + changedValue.Value.Value);
                            break;
                        }
                    case LocalLobbyData.k_LobbyState:
                        {
                            LobbyState state = (LobbyState)int.Parse(changedValue.Value.Value);
                            localLobby.SetLocalLobbyState(state);
                            Debug.Log("Lobby Custom Data Changed [Hole]: " + state.ToString());
                            break;
                        }
                }
            }
        }

        private void OnPlayerJoined(List<LobbyPlayerJoined> ctx)
        {
            Debug.Log("Player Joined");
            foreach (var playerChanges in ctx)
            {
                Player joinedPlayer = playerChanges.Player;

                var id = joinedPlayer.Id;
                var index = playerChanges.PlayerIndex;
                var isHost = localLobby.GetHostID() == id;

                var newPlayer = new LocalPlayer(id, index, isHost, new PlayerDataRank(), new PlayerAvatarData());

                foreach (var dataEntry in joinedPlayer.Data)
                {
                    var dataObject = dataEntry.Value;
                    ParseCustomPlayerData(newPlayer, dataEntry.Key, dataObject.Value);
                }

                localLobby.AddPlayer(index, newPlayer);
            }

            ActionDispatcher.Dispatch(new PlayerJoinedAction(ctx));
        }

        private void OnPlayerLeft(List<int> ctx)
        {
            LocalPlayer opponentPlayer = null;
            foreach (var playerIndex in ctx)
            {
                opponentPlayer = new LocalPlayer(localLobby.GetPlayerByIndex(playerIndex));
            }
            
            //Opponent Quit Game When End Game => Do Nothing
            var playerId = AuthenticationService.Instance.PlayerId;
            var localPlayer = localLobby.LocalPlayers.Find((player) => player.GetId() == playerId);
            if(localPlayer.GetUserStatus() != PlayerStatus.FinishGame)
                ActionDispatcher.Dispatch(new LocalLobbyOpponentLeft(opponentPlayer));
        }

        private void OnPlayerDataChanged(Dictionary<int, Dictionary<string, ChangedOrRemovedLobbyValue<PlayerDataObject>>> ctx)
        {
            Debug.Log("Player Data Changed");

            foreach (var lobbyPlayerChanges in ctx)
            {
                var playerIndex = lobbyPlayerChanges.Key;
                var localPlayer = localLobby.GetPlayerByIndex(playerIndex);

                if (localPlayer == null) continue;
                
                var playerChanges = lobbyPlayerChanges.Value;
                if (playerChanges == null) continue;

                bool isHitBall = false;
                foreach (var playerChange in playerChanges)
                {
                    var changedValue = playerChange.Value;
                    var playerDataObject = changedValue.Value;

                    Debug.Log(String.Format("Key: {0}, Data: {1}", playerChange.Key, playerDataObject.Value));

                    string dataKey = playerChange.Key; string playerDataValue = playerDataObject.Value;
                    if (dataKey == LocalPlayerData.k_UserStatus)
                    {
                        if ((PlayerStatus)int.Parse(playerDataValue) == PlayerStatus.HitBall)
                        {
                            Debug.Log("OPPONENT SIGNAL HIT BALL!");
                            isHitBall = true;
                        }
                    }
                }

                if (isHitBall)
                {
                    // make status hit ball to the last to be updated to local player when all shot datas have been updated
                    KeyValuePair<string, ChangedOrRemovedLobbyValue<PlayerDataObject>> hitBallChange = new KeyValuePair<string, ChangedOrRemovedLobbyValue<PlayerDataObject>>();
                    foreach (var playerChange in playerChanges)
                    {
                        var changedValue = playerChange.Value;
                        var playerDataObject = changedValue.Value;

                        string dataKey = playerChange.Key; string playerDataValue = playerDataObject.Value;
                        if (dataKey == LocalPlayerData.k_UserStatus)
                        {
                            hitBallChange = playerChange;
                            continue;
                        }
                        else
                        {
                            ParseCustomPlayerData(localPlayer, playerChange.Key, playerDataObject.Value);
                        }
                    }
                    ParseCustomPlayerData(localPlayer, hitBallChange.Key, hitBallChange.Value.Value.Value);
                }
                else
                {
                    foreach (var playerChange in playerChanges)
                    {
                        var changedValue = playerChange.Value;
                        var playerDataObject = changedValue.Value;
                        ParseCustomPlayerData(localPlayer, playerChange.Key, playerDataObject.Value);
                    }
                }
            }
        }

        private void OnLobbyDeleted()
        {
            Debug.Log("Lobby Deleted");
        }
        #endregion

        public async Task LeaveLobbyAsync(bool isBotMatch = false, Action onSucess = null)
        {
            string playerId = AuthenticationService.Instance.PlayerId;
            try
            {
                await LobbyService.Instance.RemovePlayerAsync(remoteLobby.Id, playerId);

                onSucess?.Invoke();
            }
            catch (ArgumentNullException ex)
            {
                Debug.Log(ex);
            }
            catch (LobbyServiceException ex)
            {
                Debug.Log(ex);
            }

            if (!isBotMatch)
            {
                Dispose();
            }
        }
 
        private void Dispose()
        {
            Debug.Log("Dispose");
            lobbyEventCallbacks = new LobbyEventCallbacks();
            localLobby.ResetLobby();
            localLobby = new LocalLobby();
        }

        void ParseCustomPlayerData(LocalPlayer player, string dataKey, string playerDataValue)
        {
            if (dataKey == LocalPlayerData.k_DisplayName)
            {
                player.SetDisplayName(playerDataValue);
                Debug.Log("Opponent status name:" + player.GetDisplayName());
            }
            if (dataKey == LocalPlayerData.k_UserStatus)
            {
                player.SetUserStatus((PlayerStatus)int.Parse(playerDataValue));
                Debug.Log("Opponent status:" + player.GetUserStatus());
            }
            if (dataKey == LocalPlayerData.k_BallPos)
            {
                player.SetBallPos(playerDataValue.StringToVector3());
                Debug.Log("Opponent ball pos:" + player.GetBallPos().ToString() + "[Stroke " + player.GetStrokeCount() + "]");
            }
            if (dataKey == LocalPlayerData.k_StrokeCount)
            {
                player.SetStrokeCount(int.Parse(playerDataValue));
                Debug.Log("Opponent stroke change: " + player.GetStrokeCount());
            }
            if (dataKey == LocalPlayerData.k_InputParam)
            {
                player.SetInputParam(JsonConvert.DeserializeObject<InputParam>(playerDataValue));
                Debug.Log("Opponent input param:" + JsonConvert.SerializeObject(player.GetInputParam()) + "[Stroke " + player.GetStrokeCount() +"]");
            }
            if (dataKey == LocalPlayerData.k_RankInfo)
            {
                player.SetPlayerRankInfo(JsonConvert.DeserializeObject<PlayerDataRank>(playerDataValue));
            }
            if (dataKey == LocalPlayerData.k_Commands)
            {
                List<List<PlayerCommandJsonWrapper>> parsedWrapper = JsonConvert.DeserializeObject<List<List<PlayerCommandJsonWrapper>>>(playerDataValue);

                List<List<PlayerCommand>> playerCommands = new List<List<PlayerCommand>>();

                foreach (var listCommand in parsedWrapper)
                {
                    List<PlayerCommand> temp = new List<PlayerCommand>();
                    foreach (var command in listCommand)
                    {
                        temp.Add(command.ToPlayerCommand());
                    }
                    playerCommands.Add(temp);
                }

                player.ReceiveLobbyPlayerCommands(playerCommands);
            }
            if (dataKey == LocalPlayerData.k_AllocationId)
            {
                player.SetAllocationID(playerDataValue);
            }
            if (dataKey == LocalPlayerData.k_RelayJoinCodeId)
            {
                player.SetRelayJoinCodeID(playerDataValue);
            }
            if (dataKey == LocalPlayerData.k_AvatarInfo)
            {
                player.SetPlayerAvatarInfo(JsonConvert.DeserializeObject<PlayerAvatarData>(playerDataValue));
            }
        }

        Dictionary<string, PlayerDataObject> CreateInitialPlayerData(LocalPlayer user)
        {
            Dictionary<string, PlayerDataObject> data = new Dictionary<string, PlayerDataObject>();

            PlayerDataObject displayNameObject = 
                new PlayerDataObject(PlayerDataObject.VisibilityOptions.Member, user.GetDisplayName());

            data.Add("DisplayName", displayNameObject);

            InputParam inputParam = user.GetInitialInputParam();
            PlayerDataObject inputParamObject = 
                new PlayerDataObject(PlayerDataObject.VisibilityOptions.Member, JsonConvert.SerializeObject(inputParam));

            data.Add(LocalPlayerData.k_InputParam, inputParamObject);

            PlayerDataRank rankData = user.GetPlayerRankInfo();
            PlayerDataObject rankDataObject = 
                new PlayerDataObject(PlayerDataObject.VisibilityOptions.Member, JsonConvert.SerializeObject(rankData));

            var avatarData = user.GetPlayerAvatarInfo();
            PlayerDataObject avatarObject = new PlayerDataObject(PlayerDataObject.VisibilityOptions.Member,
                JsonConvert.SerializeObject(avatarData));

            data.Add(LocalPlayerData.k_RankInfo, rankDataObject);
            
            data.Add(LocalPlayerData.k_AvatarInfo, avatarObject);
            
            data.Add(LocalPlayerData.k_UserStatus, new PlayerDataObject(PlayerDataObject.VisibilityOptions.Member, ((int)PlayerStatus.Lobby).ToString()));
            return data;
        }
    }
}