using System.Collections.Generic;
using _Golf.Scripts.Core;
using _Golf.Scripts.Networking;
using _Golf.Scripts.ScriptableObjects;
using Unity.Services.Lobbies;
using Unity.Services.Lobbies.Models;
#if UNITY_EDITOR
using UnityEditor;
  #endif
using UnityEngine;
namespace _Golf.Scripts.Lobby
{
    public class LobbyManager : MonoBehaviour
    {
        public PlayerInfo playerFantaInfo;
        public RankingConfig rankingConfig;
        [SerializeField] private int dummyElo = 150;

        private void Awake()
        {
            playerFantaInfo = GlobalSO.PlayerInfoSO.Info;
        }

        public async void QueryLobby()
        {
            var matchingDiff = rankingConfig.Ranks.Find(x => x.id == playerFantaInfo.H2hRank.Id).machingDiff;
            var lowerDiff = dummyElo - matchingDiff;
            var upperDiff = dummyElo + matchingDiff;

            // Query the lobby

            var queryFilter = new List<QueryFilter>()
            {
                new QueryFilter(
                    field: QueryFilter.FieldOptions.S1,
                    op: QueryFilter.OpOptions.GT,
                    value: lowerDiff.ToString()),
                new QueryFilter(
                    field: QueryFilter.FieldOptions.S1,
                    op: QueryFilter.OpOptions.LT,
                    value: upperDiff.ToString())
            };

            var lobby = await LobbyService.Instance.QueryLobbiesAsync(
                new QueryLobbiesOptions()
                {
                    Count = 10,
                    Filters = queryFilter
                });


            Debug.Log("Lobby found with ID: " + lobby.Results.Count);


            var joinLobby = await LobbyService.Instance.QuickJoinLobbyAsync(
                new QuickJoinLobbyOptions()
                {
                    Filter = queryFilter
                });
            Debug.Log("Joined lobby with ID: " + joinLobby.Id);
        }

        public async void CreateLobby()
        {
            CreateLobbyOptions options = new CreateLobbyOptions();
            options.Data = new Dictionary<string, DataObject>()
            {
                {
                    "elo", new DataObject(
                        visibility: DataObject.VisibilityOptions.Public, // Visible publicly.
                        value: dummyElo.ToString(),
                        index: DataObject.IndexOptions.S1)
                },
            };

            Unity.Services.Lobbies.Models.Lobby lobby = await LobbyService.Instance.CreateLobbyAsync("lobby name", 2, options);
            var callback = new LobbyEventCallbacks();
            callback.PlayerJoined += OnPlayerJoined;
            _ = LobbyService.Instance.SubscribeToLobbyEventsAsync(lobby.Id, callback);
            
            Debug.Log("Lobby created with ID: " + lobby.Id);
        }
        private void OnPlayerJoined(List<LobbyPlayerJoined> obj)
        {
            Debug.Log(obj.Count);
        }

        public void OnLobbyChange(ILobbyChanges changes)
        {
            Debug.Log("Lobby changed: " + changes);
        }

    }

    #if UNITY_EDITOR

    [CustomEditor(typeof(LobbyManager))]
    public class LobbyManagerEditor : Editor
    {
        public override void OnInspectorGUI()
        {
            base.OnInspectorGUI();
            if (GUILayout.Button("Create Lobby"))
            {
                ((LobbyManager)target).CreateLobby();
            }

            if (GUILayout.Button("Query Lobby"))
            {
                ((LobbyManager)target).QueryLobby();
            }
        }
    }
    #endif
}