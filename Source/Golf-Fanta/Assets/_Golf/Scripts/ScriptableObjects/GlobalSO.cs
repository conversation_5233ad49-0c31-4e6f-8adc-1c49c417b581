using System.Threading.Tasks;
using _Golf.Scripts.Core;
using _Golf.Scripts.ScriptableObjects.GolfGameSettings;
using _Golf.Scripts.ScriptableObjects.HeadToHead;
using _Golf.Scripts.ScriptableObjects.Item;
using _Golf.Scripts.ScriptableObjects.PlayField;
using _Golf.Scripts.ScriptableObjects.Session;
using UnityEngine;
using UnityEngine.AddressableAssets;
using UnityEngine.ResourceManagement.AsyncOperations;
using UnityEngine.Serialization;

namespace _Golf.Scripts.ScriptableObjects
{
    /// <summary>
    /// A singleton scriptable object, which hold reference to other scriptable objects 
    /// </summary>
    [CreateAssetMenu(fileName = "GlobalSO", menuName = "ScriptableObjects/GlobalSO")]
    public class GlobalSO : ScriptableObject
    {
        private static GlobalSO _instance;
        public static GlobalSO Instance => _instance;
            
        public void Init()
        {
            _instance ??= this;

            InitComponent();
        }

        public void InitComponent()
        {
            LocalGameSetting.Init();
        }

        public async Task LoadAddressableInventoryItemSO()
        {
            var retryAttempts = 10;
            while (_inventoryItemSo == null && retryAttempts>0)
            {
                retryAttempts--;
                var loadHandle = Addressables.LoadAssetAsync<InventoryItemsSO>(_inventoryItemsAssetRefernce);
                await loadHandle.Task;
            
                if (loadHandle.Status == AsyncOperationStatus.Succeeded)
                    _inventoryItemSo = loadHandle.Result;
            }
            if(_inventoryItemSo == null)
                Debug.LogError($"Failed to load InventoryItemsSO");
        }

        #region GamePlay
        [SerializeField] private SessionScriptableObject _sessionData;
        public static SessionScriptableObject SessionSO => _instance != null ? _instance._sessionData : null;

        [SerializeField] private PlayFieldScriptableObject _playFieldData;
        public static PlayFieldScriptableObject PlayFieldSO => _instance != null ? _instance._playFieldData : null;

        [SerializeField] private GameplayBus _gameplayBus;
        public static GameplayBus GameplayBus => _instance != null ? _instance._gameplayBus : null;
        
        [SerializeField] private BotLevelScriptableObject _botLevel;
        public static BotLevelScriptableObject BotLevel => _instance != null ? _instance._botLevel : null;
        
        [SerializeField] private RemoteConfigScriptableObject _remoteConfigData;
        public static RemoteConfigScriptableObject RemoteConfigData => _instance != null ? _instance._remoteConfigData : null;
        
        [SerializeField] private EconomyConfigurationVirtualPurchases _economyConfigurationVirtualPurchases;
        public static EconomyConfigurationVirtualPurchases EconomyConfigurationVirtualPurchases => _instance != null ? _instance._economyConfigurationVirtualPurchases : null;
        
        [SerializeField] private EconomyConfigurationRealMoneyPurchases _economyConfigurationRealMoneyPurchasesPurchases;
        public static EconomyConfigurationRealMoneyPurchases EconomyConfigurationRealMoneyPurchasesPurchases => _instance != null ? _instance._economyConfigurationRealMoneyPurchasesPurchases : null;

        [SerializeField] private AssetReferenceT<InventoryItemsSO> _inventoryItemsAssetRefernce;
        private InventoryItemsSO _inventoryItemSo;
        public static InventoryItemsSO InventoryItemSo => _instance != null ? _instance._inventoryItemSo : null;
        
        [SerializeField] private EnvironmentVariablesScriptableObject environmentVariables;
        public static EnvironmentVariablesScriptableObject EnvironmentVariables => _instance != null ? _instance.environmentVariables : null;
        
        [SerializeField] private PlayerBagInventory _playerBagInventory;
        public static PlayerBagInventory PlayerBagInventory => _instance != null ? _instance._playerBagInventory : null;
        
        [SerializeField] private DefaultBundleUISettings _defaultBundleSetting;
        public static DefaultBundleUISettings DefaultBundleSetting => _instance != null ? _instance._defaultBundleSetting : null;
        
        [SerializeField] private PlayerInventorySO _playerInventory;
        public static PlayerInventorySO PlayerInventory => _instance != null ? _instance._playerInventory : null;
        
        [SerializeField] private PlayerGadget _playerGadget;
        public static PlayerGadget PlayerGadget => _instance != null ? _instance._playerGadget : null;
        
        [SerializeField] private HeadToHeadProperties _headToHeadProperties;
        public static HeadToHeadProperties HeadToHeadProperties => _instance != null ? _instance._headToHeadProperties : null;
        
        [SerializeField] private SinglePlayProperties _singlePlayProperties;
        public static SinglePlayProperties SinglePlayProperties => _instance != null ? _instance._singlePlayProperties : null;

        [SerializeField] private PlayerSaveScriptableObject playerSaves;
        public static PlayerSaveScriptableObject PlayerSaves => _instance != null ? _instance.playerSaves : null;

        [SerializeField] private TournamentPlaySO _tournamentPlaySO;
        public static TournamentPlaySO TournamentPlaySO => _instance != null ? _instance._tournamentPlaySO : null;
        
        [SerializeField] private NeedleProperties _needlePropertiesSO;
        public static NeedleProperties NeedleProperties => _instance != null ? _instance._needlePropertiesSO : null;
        
        #endregion

        [SerializeField] private DailyBagData _dailyBagData;
        public static DailyBagData DailyBagData => _instance != null ? _instance._dailyBagData : null;
        
        [SerializeField] private PlayerInfoSO _playerInfo;
        public static PlayerInfoSO PlayerInfoSO => _instance != null ? _instance._playerInfo : null;

        [SerializeField] private GolfGameSettingSO _golfGameSetting;
        public static GolfGameSettingSO GolfGameSetting => _instance != null ? _instance._golfGameSetting : null;

        [SerializeField] private LocalGameSettingSO _localGameSetting;
        public static LocalGameSettingSO LocalGameSetting => _instance != null ? _instance._localGameSetting : null;

        [SerializeField] private CameraSettingSO _cameraSetting;
        public static CameraSettingSO CameraSettingSO => _instance != null ? _instance._cameraSetting : null;
    }
}