using System;
using _Golf.Scripts.Core;
using GolfFantaModule.Models.Economy;
using GolfGame.API;
using UnityEngine;

namespace _Golf.Scripts.ScriptableObjects.Session
{
    [CreateAssetMenu(fileName = "SessionSO", menuName = "ScriptableObjects/SessionSO")]
    public class SessionScriptableObject : ScriptableObject
    {
        [SerializeField] private SessionData Session;

        public void SetSession(string SessionUuid)
        {
            Session = new SessionData(SessionUuid);
        }

        public string GetSessionUuid()
        {
            if (Session == null)
            {
                return "none";
            }
            else
            {
                return Session.SessionUuid;
            }
        }

        [SerializeField] private ClientActivity currentActivity;
        [SerializeField] private string currentActivityUuid;

        public void InitializeActivity(GameMode gameMode, string courseId, string hole, string rankId, string additionalData)
        {
            currentActivity = new ClientActivity(
                sessionUuid: GlobalSO.SessionSO.GetSessionUuid(),
                gameMode: gameMode,
                courseId: courseId,
                holeIds: new string[1] { hole },
                rankId: rankId,
                additionalData: additionalData
            );
        }

        public void AddHoleToCurrentActivity(string hole)
        {
            if (currentActivity == null)
            {
                Debug.LogError("Current activity is null");
                return;
            }
            else
            {
                currentActivity.AddHole(hole);
            }
        }

        public async void UploadActivity()
        {
            try
            {
                var result = await APIGameClient.Instance.AddActivityToSession(currentActivity);
                currentActivityUuid = result.data;
            }
            catch (Exception e)
            {
                Debug.LogError(e);
            }
        }

        public string GetCurrentActivityUuid()
        {
            return currentActivityUuid;
        }
    }

    [Serializable]
    public class SessionData
    {
        public readonly string SessionUuid;
        public SessionData(string sessionUuid)
        {
            SessionUuid = sessionUuid;
        }
    }
}
