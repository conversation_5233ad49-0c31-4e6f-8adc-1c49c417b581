using System;
using System.Linq;
using _Golf.Scripts.Common;
using _Golf.Scripts.Models.DTOs;
using _Golf.Scripts.ScriptableObjects;
using Cysharp.Threading.Tasks;
using GolfGame;
using GolfGame.API;
using GolfGame.API.Models.PlayerMessage;
using Unity.Services.Authentication;
using Unity.Services.Friends;
using Unity.Services.Friends.Models;
using UnityEngine;

namespace _Golf.Scripts.Core
{
    [CreateAssetMenu(fileName = "PlayerInfoSO", menuName = "ScriptableObjects/PlayerInfoSO")]
    public class PlayerInfoSO : ScriptableObject
    {
        public PlayerInfo Info = new PlayerInfo();
        
        [SerializeField] private UserAvailability _currentAvailability = UserAvailability.Online;

        public void UpdateAuthenticateInfo()
        {
            Info.UpdateAuthenticateInfo(AuthenticationService.Instance.PlayerId, AuthenticationService.Instance.PlayerName);
            ActionDispatcher.Dispatch(new UpdatePlayerInfoAction(Info));
        }

        public async UniTask ChangeAvatar(string newAvatarId)
        {
            if (Info.Avatar?.Id == newAvatarId) return;
            var response = await APIGameClient.Instance.SetPlayerAvatar(newAvatarId);
            if (response.responseCode == APIResponseCode.Fail)
            {
                Debug.LogError(response.message);
                return;
            }

            UpdateInfo(response.data);
        }

        public async UniTask ChangeName(string newName)
        {
            var response = await APIGameClient.Instance.SetPlayerName(newName);
            if (response.responseCode == APIResponseCode.Fail)
            {
                Debug.LogError(response.message);
                return;
            }
            await AuthenticationService.Instance.GetPlayerNameAsync();

            UpdateAuthenticateInfo();
        }

        public void UpdateInfo(PlayerInfoModel cloudSaveModel)
        {
            Info.UpdateInfo(cloudSaveModel);
            UpdateAuthenticateInfo();

            Debug.Log("Player Info Changed");
            ActionDispatcher.Dispatch(new UpdatePlayerInfoAction(Info));
        }
        
        public async void UpdateAvailability(UserAvailability inernalAvailability)
        {
            await FriendsService.Instance.SetPresenceAvailabilityAsync(inernalAvailability switch
            {
                UserAvailability.Online => Availability.Online,
                UserAvailability.Offline => Availability.Offline,
                UserAvailability.Unknown => Availability.Away,
                UserAvailability.InMatch => Availability.Busy,
                _ => Availability.Offline
            });
            _currentAvailability = inernalAvailability;
            
            ActionDispatcher.Dispatch(new SetPlayerAvaiblityAction(inernalAvailability));
        }
    }

    [Serializable]
    public class PlayerInfo
    {
        public string Id => _id;
        private string _id;
        public string DisplayName => _displayName;

        [SerializeField] private string _displayName;
        public Rank H2hRank => _h2hRank;
        private Rank _h2hRank = new Rank();

        [SerializeField] private Avatar _avatar;
        public Avatar Avatar
        {
            get => _avatar;
            private set => _avatar = value;
        }
        
        public PlayerStats PlayerStats => _playerStats;
        private PlayerStats _playerStats;

        public PlayerInfo()
        {
            
        }

        public PlayerInfo(PlayerDTO playerDto)
        {
            _id = playerDto.playerId;
            _displayName = playerDto.playerName;
            _h2hRank = new Rank(playerDto.elo, playerDto.rankId);
            _avatar = new Avatar(playerDto.avatarId);
            _playerStats = new PlayerStats(playerDto);
        }

        public PlayerInfo(string playerId, string displayName, PlayerInfoModel cloudSaveModel) : this()
        {
            _id = playerId;
            _displayName = GolfUtility.RemovePartStartingWithHash(displayName);
            UpdateInfo(cloudSaveModel);
        }

        public PlayerInfo(PlayerModel playerModel) : this()
        {
            _id = playerModel.UnityPlayerGuid;
            _displayName = playerModel.PlayerName;
            _h2hRank = new Rank(playerModel.Elo, playerModel.RankId);
            _avatar = new Avatar(playerModel.AvatarId);
            _playerStats = new PlayerStats(playerModel);
        }
        
        public void UpdateInfo(PlayerInfoModel cloudSaveModel)
        {
            _h2hRank = new Rank(cloudSaveModel.rank);
            _playerStats = new PlayerStats(cloudSaveModel);
            Avatar = new Avatar(cloudSaveModel.avatarId);
        }

        public void UpdateAuthenticateInfo(string id, string displayName)
        {
            _id = id;
            _displayName = GolfUtility.RemovePartStartingWithHash(displayName);
        }
    }

    [SerializeField]
    public class PlayerAvatarData
    {
        public string Id;
        private const string DefaultAvatarId = "AVATAR_DEFAULT_1";
        public PlayerAvatarData()
        {
            Id = DefaultAvatarId;
        }

        public PlayerAvatarData(string id)
        {
            Id = id;
        }
    }

    [Serializable]
    public class Avatar
    {
        private const string SocialAvatarId = "social_avatar";
        private const string DefaultAvatarId = "AVATAR_DEFAULT_1";
        
        public string Id;
        public string DisplayName;
        public Sprite Image => _data.image;
        private AvatarSO _data;
        private bool IsSocialAvatar => Id == SocialAvatarId;

        public Avatar()
        {
            Id = DefaultAvatarId;
            SetAvatar(Id);
        }
        
        public Avatar(string id) : this()
        {
            if (id == null) return;
            
            Id = id;
            if (IsSocialAvatar)
            {
                //TODO: fetch social avatar from cloud
            }
            else
            {
                SetAvatar(id);
            }
        }

        private void SetAvatar(string avatarId)
        {
            if (GlobalSO.InventoryItemSo == null
            || GlobalSO.InventoryItemSo.avatars == null) return;

            var matchingAvatar = GlobalSO.InventoryItemSo.avatars.FirstOrDefault(avatar => avatar.id == avatarId);
            _data = matchingAvatar;
            DisplayName = matchingAvatar != null ? matchingAvatar.avatarName : "";
        }
    }
    
    public class PlayerStats
    {
        public int TotalH2hGames { get; }
        public int TotalH2hGamesWon { get; }
        public float H2hWinRate { get; }
        public int LongestH2hWinStreak { get; }
        public float LongestDrive { get; } //In Meters
        public int HolesInOne { get; }
        public int Albatrosses { get; }
        public int Eagles { get; }
        public int Birdies { get; }
        public int TotalH2hCoinsWon { get; }
        public int TotalH2hElo { get; }
        public Rank HighestH2hRank { get; }

        public PlayerStats(PlayerInfoModel playerInfoModel)
        {
            TotalH2hGames = playerInfoModel.totalH2hGames;
            TotalH2hGamesWon = playerInfoModel.totalH2hGamesWon;
            H2hWinRate = playerInfoModel.totalH2hGames == 0 ? 0 : (float)playerInfoModel.totalH2hGamesWon / playerInfoModel.totalH2hGames;
            LongestH2hWinStreak = playerInfoModel.longestH2hWinStreak;
            LongestDrive = playerInfoModel.longestDrive;
            HolesInOne = playerInfoModel.holesInOne;
            Albatrosses = playerInfoModel.albatrosses;
            Eagles = playerInfoModel.eagles;
            Birdies = playerInfoModel.birdies;
            TotalH2hCoinsWon = playerInfoModel.totalH2hCoinsWon;
            TotalH2hElo = playerInfoModel.totalH2hElo;
            HighestH2hRank = new Rank(playerInfoModel.highestH2hRankId);
        }

        public PlayerStats(PlayerModel playerModel)
        {
            TotalH2hGames = playerModel.TotalH2hGames;
            TotalH2hGamesWon = playerModel.TotalH2hGamesWon;
            H2hWinRate = playerModel.TotalH2hGames == 0 ? 0 : (float)playerModel.TotalH2hGamesWon / playerModel.TotalH2hGames;
            LongestH2hWinStreak = playerModel.LongestH2hWinStreak;
            LongestDrive = playerModel.LongestDrive;
            HolesInOne = playerModel.HolesInOne;
            Albatrosses = playerModel.Albatrosses;
            Eagles = playerModel.Eagles;
            Birdies = playerModel.Birdies;
            TotalH2hCoinsWon = playerModel.TotalH2hCoinsWon;
            TotalH2hElo = playerModel.TotalH2hElo;
            HighestH2hRank = new Rank(playerModel.HighestH2hRankId);
        }

        public PlayerStats(PlayerDTO playerDto)
        {
            TotalH2hGames = playerDto.totalH2hGames;
            TotalH2hGamesWon = playerDto.totalH2hGamesWon;
            H2hWinRate = playerDto.totalH2hGames == 0 ? 0 : (float)playerDto.totalH2hGamesWon / playerDto.totalH2hGames;
            LongestH2hWinStreak = playerDto.longestH2hWinStreak;
            LongestDrive = playerDto.longestDrive;
            HolesInOne = playerDto.holesInOne;
            Albatrosses = playerDto.albatrosses;
            Eagles = playerDto.eagles;
            Birdies = playerDto.birdies;
            TotalH2hCoinsWon = playerDto.totalH2hCoinsWon;
            TotalH2hElo = playerDto.totalH2hElo;
            HighestH2hRank = new Rank(playerDto.highestH2hRankId);
        }
    }
}