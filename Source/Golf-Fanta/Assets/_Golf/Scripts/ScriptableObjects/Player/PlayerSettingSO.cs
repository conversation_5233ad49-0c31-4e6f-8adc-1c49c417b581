using UnityEngine;
namespace _Golf.Scripts.Core
{
    [CreateAssetMenu(menuName = "ScriptableObjects/Player/PlayerSettingSO", fileName = "PlayerSettingSO")]
    public class PlayerSettingSO : ScriptableObject
    {
        [field: SerializeField] public PlayerSessionInfo info { get; private set; }
        public PlayerSettingSO(PlayerSessionInfo info)
        {
            this.info = info;
        }
    }
}