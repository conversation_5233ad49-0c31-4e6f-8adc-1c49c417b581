using System;
using System.Collections.Generic;
using System.Linq;
using _Golf.Scripts.Core;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using UnityEngine;

namespace _Golf.Scripts.ScriptableObjects.Tours
{
    /// <summary>
    /// This class contains course lists that are parsed directly from remote config using JSON.
    /// </summary>
    [CreateAssetMenu(fileName = "TourConfig", menuName = "Tour/TourConfig")]
    public class TourConfig : ScriptableObject
    {
        private List<H2hTourConfig> H2HTourConfigs = new();
        

        public void SetH2HTourConfig(string config)
        {
            H2HTourConfigs = JsonConvert.DeserializeObject<List<H2hTourConfig>>(config);
        }

        public List<H2hTourConfig> GetH2HTourConfigs()
        {
            return H2HTourConfigs.ToList();
        }

        private List<HoleInfo> holeInfos = new();
        private List<CourseInfo> courseInfos = new();

        [SerializeField] private List<H2HTourInfo> h2HTourInfos = new();
        [SerializeField] private List<ClosestToPinTourInfo> closestToPinTourInfos = new();
        [SerializeField] private List<TournamentTourInfo> tournamentTourInfos = new();
        [SerializeField] private List<RaceChallengeTourInfo> raceChallengeTourInfos = new();

        #region Init
        public void SetHoleInfos(string config)
        {
            holeInfos = JsonConvert.DeserializeObject<List<HoleInfo>>(config);
        }

        public void SetCourse(string config)
        {
            CourseList courseListConfig = JsonConvert.DeserializeObject<CourseList>(config);

            courseInfos = new List<CourseInfo>();

            foreach (Course courseConfig in courseListConfig.course_list)
            {
                CourseInfo courseInfo = new CourseInfo();

                courseInfo.Id = courseConfig.course_id;
                courseInfo.Name = courseConfig.course_name;
                courseInfo.Type = courseConfig.courseType;

                // FAKE geography data cause server doesnt have this course data yet
                CourseGeography courseGeography = new CourseGeography { };
                courseConfig.courseGeography = JsonConvert.SerializeObject(courseGeography);

                // PARSE geography 
                try
                {
                    courseInfo.Geography = JsonConvert.DeserializeObject<CourseGeography>(courseConfig.courseGeography);
                }
                catch (JsonException ex)
                {
                    Debug.LogError($"Error parsing geography of course: {ex.Message}");
                    courseInfo.Geography = new CourseGeography { };
                }

                // sort hole list by hole number
                // this step ensures the correct order of hole
                List<Hole> courseConfigHoles = courseConfig.holes.ToList();
                courseConfigHoles.Sort((a, b) =>
                {
                    int aVal = int.TryParse(a.hole_number, out int aNum) ? aNum : -1;
                    int bVal = int.TryParse(b.hole_number, out int bNum) ? bNum : -1;

                    if (aVal == -1 || bVal == -1)
                    {
                        Debug.LogError("Hole number parsed to int failed!");
                    }

                    return aVal.CompareTo(bVal);
                });

                List<HoleInfo> courseInfoHoles = holeInfos.Where(b => courseConfigHoles.Any(a => a.hole_guid == b.Guid)).ToList();
                courseInfo.Holes = courseInfoHoles;

                courseInfos.Add(courseInfo);
            }
        }

        public void SetTour(List<ConfigTour> ConfigTours)
        {
            h2HTourInfos = new();
            closestToPinTourInfos = new();
            tournamentTourInfos = new();
            raceChallengeTourInfos = new();

            foreach (ConfigTour tour in ConfigTours)
            {
                try
                {
                    GameMode gameMode = (GameMode)tour.GameMode;

                    switch (gameMode)
                    {
                        case GameMode.HeadToHead:
                            {
                                h2HTourInfos.Add(new H2HTourInfo(tour));
                                break;
                            }
                        case GameMode.ClosestToPin:
                            {
                                closestToPinTourInfos.Add(new ClosestToPinTourInfo(tour));
                                break;
                            }
                        case GameMode.Tournament:
                            {
                                tournamentTourInfos.Add(new TournamentTourInfo(tour));
                                break;
                            }
                        case GameMode.RaceChallenge:
                            {
                                raceChallengeTourInfos.Add(new RaceChallengeTourInfo(tour));
                                break;
                            }
                        default:
                            {
                                Debug.LogWarning($"Unknown game mode: {tour.GameMode}");
                                break;
                            }
                    }
                }
                catch (Exception ex)
                {
                    Debug.LogError($"Error parsing tour config: {ex.Message}");
                }
            }
            h2HTourInfos.Sort((a, b) => a.GetRequiredElo().CompareTo(b.GetRequiredElo()));

            closestToPinTourInfos.Sort((a, b) => a.GetRequiredElo().CompareTo(b.GetRequiredElo()));
        }
        #endregion

        #region Fetch Infos
        public List<HoleInfo> GetHoleInfos()
        {
            return holeInfos.ToList();
        }
        public List<CourseInfo> GetCourseInfos()
        {
            return courseInfos.ToList();
        }

        public List<H2HTourInfo> GetH2HTourInfos()
        {
            return h2HTourInfos.ToList();
        }

        public List<ClosestToPinTourInfo> GetClosestToPinTourInfos()
        {
            return closestToPinTourInfos.ToList();
        }

        public List<TournamentTourInfo> GetTournamentTourInfos()
        {
            return tournamentTourInfos.ToList();
        }

        public List<RaceChallengeTourInfo> GetRaceChallengeTourInfos() 
        {
            return raceChallengeTourInfos.ToList();
        }
        #endregion
    }

    [Serializable]
    public class H2hTourConfig
    {
        [JsonProperty("tourId")]
        public string Id { get; set; }

        [JsonProperty("courseId")]
        public string CourseId { get; set; }

        [JsonProperty("nameKey")]
        public string NameKey { get; set; }

        [JsonProperty("descriptionKey")]
        public string DescriptionKey { get; set; }

        [JsonProperty("requiredElo")]
        public int RequiredElo { get; set; }

        [JsonProperty("entryFee")]
        public int EntryFee { get; set; }

        [JsonProperty("windSpeedMin")]
        public int WindSpeedMin { get; set; }

        [JsonProperty("windSpeedMax")]
        public int WindSpeedMax { get; set; }
    }

    #region TOUR
    /// <summary>
    /// Class to parse raw tour data from server.
    /// </summary>
    [Serializable]
    public class ConfigTour
    {
        public int Id;
        public string Uuid;
        public string NameKey;
        public string DescriptionKey;
        public List<TourCondition> Conditions;
        public List<TourEntryFee> EntryFees;
        public List<TourDifficulty> Difficulties;
        public int GameMode;
        public string LeaderboardId;
        public string CourseUuid;
        public string BannerAddress;
        public string Status;
        public string Data;
    }

    [Serializable]
    public class TourParameter
    {
        public float WindSpeedMin;
        public float WindSpeedMax;
    }

    [Serializable]
    public class TourCondition
    {
        public string ConditionType;
        private TourConditionType _conditionType = TourConditionType.Unknown;
        public TourConditionType Type => _conditionType == TourConditionType.Unknown ? _conditionType = (TourConditionType)Enum.Parse(typeof(TourConditionType), ConditionType) : _conditionType;
        public object Parameters;
        private object _value;
        public object Value => _value ??= ParseValue();

        public bool EvaluateCondition(object parameters)
        {
            switch (Type)
            {
                case TourConditionType.RequiredElo:
                    var requiredElo = (int)Value;
                    var playerElo = (int)parameters;
                    return playerElo >= requiredElo;
                case TourConditionType.Time:
                    var (startTime, endTime) = (ValueTuple<DateTime, DateTime>)Value;
                    return (DateTime)parameters >= startTime && (DateTime)parameters <= endTime;
                default:
                    return false;
            }
        }
        
        private object ParseValue()
        {
            var dictionary = JsonConvert.DeserializeObject<Dictionary<string, string>>(Parameters.ToString());
            switch (Type)
            {
                case TourConditionType.RequiredElo:
                    var requiredEloString = dictionary["RequiredElo"].ToString();
                    int.TryParse(requiredEloString, out var requiredElo);
                    return requiredElo;
                case TourConditionType.Time:
                    var tourStartTime = dictionary["StartTime"].ToString();
                    var tourEndTime = dictionary["EndTime"].ToString();
                    return (DateTime.Parse(tourStartTime), DateTime.Parse(tourEndTime));
                default:
                    return null;
            }
        }
    }

    /// <summary>
    /// Base class for tours of all game modes.
    /// A tour contains a course and other information that is related to the tour.
    /// </summary>
    [Serializable]
    public abstract class BaseTourInfo<T> where T : CustomTourData
    {
        protected abstract GameMode GameMode { get; }

        // raw data
        protected int Id;
        protected string Uuid;
        protected string NameKey;
        protected string DescriptionKey;
        protected List<TourCondition> Conditions;
        protected List<TourEntryFee> EntryFees;
        protected List<TourDifficulty> Difficulties;
        protected string LeaderboardId;
        protected string CourseUuid;
        protected string BannerAddress;
        protected string Status;

        protected T Data;

        protected BaseTourInfo(ConfigTour config)
        {
            Id = config.Id;
            Uuid = config.Uuid;
            NameKey = config.NameKey;
            DescriptionKey = config.DescriptionKey;
            LeaderboardId = config.LeaderboardId;
            CourseUuid = config.CourseUuid;
            BannerAddress = config.BannerAddress;
            Status = config.Status;
            Difficulties = config.Difficulties;
            EntryFees = config.EntryFees;
            Conditions = config.Conditions;

            try
            {
                Data = JsonConvert.DeserializeObject<T>(config.Data);
            }
            catch (JsonException ex)
            {
                Debug.LogError($"Error parsing tour config: {ex.Message}");

            }
        }

        public string GetUuid()
        {
            return Uuid;
        }

        public string GetNameKey()
        {
            return NameKey;
        }

        public string GetDescriptionKey()
        {
            return DescriptionKey;
        }

        public string GetCourseUuid()
        {
            return CourseUuid;
        }

        public string GetBannerAddress()
        {
            return BannerAddress;
        }

        public string GetLeaderboardId()
        {
            return LeaderboardId;
        }

        public TourCondition GetTourConditionByType(TourConditionType type)
        {
            return Conditions.FirstOrDefault(condition => condition.Type == type);
        }

        public int GetRequiredElo()
        {
            var condition = GetTourConditionByType(TourConditionType.RequiredElo);
            if (condition == null)
            {
                return 0;
            }
            return (int)condition.Value;
        }

        public virtual int GetEntryFee()
        {
            return 0;
        }

        public virtual float GetWindSpeedMin()
        {
            return Difficulties.FirstOrDefault()?.WindSpeedMin ?? 0;
        }

        public virtual float GetWindSpeedMax()
        {
            return Difficulties.FirstOrDefault()?.WindSpeedMax ?? 0;
        }
    }

    /// <summary>
    /// Tour class for Head to Head game mode.
    /// </summary>
    [Serializable]
    public class H2HTourInfo : BaseTourInfo<H2HTourData>
    {
        protected override GameMode GameMode => GameMode.HeadToHead;

        public H2HTourInfo(ConfigTour config) : base(config)
        {

        }

        public int GetH2HBannerIndex()
        {
            var bannerAddress = GetBannerAddress();

            string[] parts = bannerAddress.Split('_');
            if (parts.Length > 1)
            {
                int.TryParse(parts[^1], out int bannerIndex);
                return bannerIndex - 1;
            }
            return 0;
        }

        public override int GetEntryFee()
        {
            return EntryFees.FirstOrDefault()?.Costs.FirstOrDefault()?.Quantity ?? 0;
        }
    }

    /// <summary>
    /// Tour class for Closest to Pin game mode.
    /// </summary>
    [Serializable]
    public class ClosestToPinTourInfo : BaseTourInfo<ClosestToPinTourData>
    {
        protected override GameMode GameMode => GameMode.ClosestToPin;

        public ClosestToPinTourInfo(ConfigTour config) : base(config)
        {

        }

        public ClosestToPinTourData GetClosestToPinTourData()
        {
            return Data;
        }

        public ClosesToPinRankReward GetFragmentDataByRank(Rank rank)
        {
            var majorRank = rank.MajorRank;
            var ctpData = GetClosestToPinTourData();
            return majorRank switch
            {
                GolfGeneralRankEnum.Amateur => ctpData.ClosestToPin.game_modes.Fragment.difficulties.Amateur,
                GolfGeneralRankEnum.Rookie => ctpData.ClosestToPin.game_modes.Fragment.difficulties.Rookie,
                GolfGeneralRankEnum.Veteran => ctpData.ClosestToPin.game_modes.Fragment.difficulties.Veteran,
                GolfGeneralRankEnum.Pro => ctpData.ClosestToPin.game_modes.Fragment.difficulties.Pro,
                GolfGeneralRankEnum.Champion => ctpData.ClosestToPin.game_modes.Fragment.difficulties.Champion,
                _ => ctpData.ClosestToPin.game_modes.Fragment.difficulties.Amateur
            };
        }

        public ClosesToPinRankReward GetCoinDataByRank(Rank rank)
        {
            var majorRank = rank.MajorRank;
            var ctpData = GetClosestToPinTourData();
            return majorRank switch
            {
                GolfGeneralRankEnum.Amateur => ctpData.ClosestToPin.game_modes.Coin.difficulties.Amateur,
                GolfGeneralRankEnum.Rookie => ctpData.ClosestToPin.game_modes.Coin.difficulties.Rookie,
                GolfGeneralRankEnum.Veteran => ctpData.ClosestToPin.game_modes.Coin.difficulties.Veteran,
                GolfGeneralRankEnum.Pro => ctpData.ClosestToPin.game_modes.Coin.difficulties.Pro,
                GolfGeneralRankEnum.Champion => ctpData.ClosestToPin.game_modes.Coin.difficulties.Champion,
                _ => ctpData.ClosestToPin.game_modes.Coin.difficulties.Amateur
            };
        }
    }

    /// <summary>
    /// Tour class for Tournament game mode.
    /// </summary>
    [Serializable]
    public class TournamentTourInfo : BaseTourInfo<TournamentTourData>
    {
        protected override GameMode GameMode => GameMode.Tournament;

        public TournamentTourInfo(ConfigTour config) : base(config)
        {

        }

        public int GetEntryFeeByRank(Rank rank)
        {
            var entryFee = EntryFees.FirstOrDefault(fee => fee.RankId == rank.Id);
            if (entryFee == null)
            {
                return 0;
            }
            return entryFee.Costs.FirstOrDefault()?.Quantity ?? 0;
        }

        public List<TourEntryFee> GetAllEntryFees()
        {
            return EntryFees;
        }

        public List<TourDifficulty> GetAllDifficulties()
        {
            return Difficulties;
        }

        public string GetCurrencyId()
        {
            return EntryFees.FirstOrDefault()?.Costs.FirstOrDefault()?.ItemId ?? "";
        }

        public (DateTime, DateTime) GetTourTime()
        {
            var condition = GetTourConditionByType(TourConditionType.Time);
            if (condition == null)
            {
                return (DateTime.MinValue, DateTime.MaxValue);
            }
            return ((DateTime, DateTime))condition.Value;
        }
    }

    public class H2HTourData : CustomTourData
    {
        
    }
    
    public class ClosestToPinTourData : CustomTourData
    {
        public ClosesToPinConfigModel ClosestToPin;
    }



    public class TournamentTourData : CustomTourData
    {
        
    }

    public class RaceChallengeTourData : CustomTourData
    {
        
    }

    public abstract class CustomTourData
    {

    }

    /// <summary>
    /// Tour class for Race challenge game mode.
    /// </summary>
    [Serializable]
    public class RaceChallengeTourInfo : BaseTourInfo<RaceChallengeTourData>
    {
        protected override GameMode GameMode => GameMode.RaceChallenge;

        public RaceChallengeTourInfo(ConfigTour config) : base(config)
        {

        }
    }

    [Serializable]
    public class TourDifficulty
    {
        public string RankId;
        public float WindSpeedMin;
        public float WindSpeedMax;
    }

    public class TourEntryFee
    {
        public string RankId;
        public List<TourFee> Costs;
    }

    [Serializable]
    public class TourFee
    {
        public string ItemId;
        public int Quantity;
    }

    public enum TourConditionType
    {
        Unknown,
        RequiredElo,
        Time
    }
    #endregion

    #region COURSE
    /// <summary>
    /// Class to parse raw course data from server.
    /// </summary>
    [Serializable]
    public class CourseList
    {
        public List<Course> course_list;
    }

    [Serializable]
    public class Course
    {
        public string course_id;
        public string course_name;
        public string courseType;
        public string courseGeography;
        public List<Hole> holes;
    }

    [Serializable]
    public class CourseGeography
    {

    }

    /// <summary>
    /// Course information class that contains a list of holes and other course-related data
    /// like weather, slopeness and other stuffs.
    /// </summary>
    public class CourseInfo
    {
        public string Id { get; set; }

        public string Name { get; set; }

        public string Type { get; set; }

        public List<HoleInfo> Holes { get; set; }

        public CourseGeography Geography { get; set; }

        public HoleInfo CurrentHole => Holes[GlobalSO.GameplayBus.localLobby.GetCurrentHole()];
    }
    #endregion

    #region HOLE
    /// <summary>
    /// Class to parse raw hole data from server.
    /// </summary>
    [Serializable]
    public class Hole
    {
        public string hole_number;
        public string hole_guid;
    }

    /// <summary>
    /// Hole information class.
    /// </summary>
    [Serializable]
    public class HoleInfo
    {
        [JsonProperty("guid")]
        public string Guid;

        [JsonProperty("name")]
        public string Name;

        [JsonProperty("bucketBadge")]
        public string BucketBadge;

        [JsonProperty("par")]
        public int Par;
    }
    #endregion
}