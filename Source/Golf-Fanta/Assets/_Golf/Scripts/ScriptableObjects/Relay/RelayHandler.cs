using UnityEngine;
using System.Collections.Generic;
using Unity.Services.Relay.Models;
using System.Linq;
using Unity.Services.Relay;
using Unity.Collections;
using Unity.Networking.Transport;
using Region = Unity.Services.Relay.Models.Region;
using Unity.Networking.Transport.Relay;
using System;
using System.Threading.Tasks;
using Unity.Networking.Transport.Error;
using _Golf.Scripts.Common;
using _Golf.Scripts.Core;
using _Golf.Scripts.Networking.Photon.Lobby;
using Unity.Services.Authentication;
using _Golf.Scripts.ScriptableObjects;

namespace _Golf.Scripts.Lobby
{
    public class RelayHandler
    {
        private List<Region> regions;
        private List<string> regionOptions;

        private int regionAutoSelectIndex = 0;

        private Allocation hostAllocation;
        private JoinAllocation playerAllocation;

        private NetworkDriver hostDriver;
        private NetworkDriver playerDriver;
        private NativeList<NetworkConnection> serverConnections;
        private NetworkConnection clientConnection;

        private string joinCode = "N/A";

        private bool isHost = false;
        private bool isPlayer = false;

        public RelayHandler() 
        {
            regions = new List<Region>();
            regionOptions = new List<string>();

            FetchRegions();
        }

        #region Region
        public async void FetchRegions()
        {
            Debug.Log("[RelayHandler][Host] Getting regions.");
            List<Region> allRegions = await RelayService.Instance.ListRegionsAsync();

            regions.Clear();
            regionOptions.Clear();

            foreach (var region in allRegions)
            {
                Debug.Log(region.Id + ": " + region.Description);
                regionOptions.Add(region.Id);
                regions.Add(region);
            }
        }

        private string ChooseRegionFromList(int index = 0)
        {
            if (!regions.Any() || index < 0 || index >= regions.Count)
            {
                return null;
            }
            return regions[index].Id;
        }
        #endregion

        #region Host
        private async Task HostCreateAllocate(int maxConnections, int regionIdx = 0)
        {
            Debug.Log("[RelayHandler][Host] Creating an allocation.");

            // Determine region to use
            string region = ChooseRegionFromList(regionIdx);
            Debug.Log($"[RelayHandler][Host] Choose region: {region}");

            // Important: Once the allocation is created, you have ten seconds to BIND, else the allocation times out.
            try
            {
                hostAllocation = await RelayService.Instance.CreateAllocationAsync(maxConnections, region);
            }
            catch (RelayServiceException ex)
            {
                Debug.Log("[RelayHandler][Host] " + ex.ApiError + " / " + ex.Message);
            }

            Debug.Log($"[RelayHandler][Host] Allocation ID: {hostAllocation.AllocationId}, region: {hostAllocation.Region}");

            // Initialize NetworkConnection list for the server (Host).
            // This list object manages the NetworkConnections which represent connected players.
            serverConnections = new NativeList<NetworkConnection>(maxConnections, Allocator.Persistent);
        }

        private void BindHost()
        {
            Debug.Log("[RelayHandler][Host] Binding to the Relay server using UTP.");

            // Extract the Relay server data from the Allocation response.
            RelayServerData relayServerData = AllocationUtils.ToRelayServerData(hostAllocation, "udp");

            // Create NetworkSettings using the Relay server data.
            NetworkSettings settings = new NetworkSettings();
            settings.WithRelayParameters(ref relayServerData);

            // Create the Host's NetworkDriver from the NetworkSettings.
            hostDriver = NetworkDriver.Create(settings);

            // Bind to the Relay server.
            if (hostDriver.Bind(NetworkEndpoint.AnyIpv4) != 0)
            {
                Debug.LogError("[RelayHandler][Host] Failed to bind");
            }
            else
            {
                if (hostDriver.Listen() != 0)
                {
                    Debug.LogError("[RelayHandler][Host] Failed to listen");
                }
                else
                {
                    Debug.Log("[RelayHandler][Host] Bound to Relay server");
                }
            }
        }

        private async Task HostGetJoinCode()
        {
            Debug.Log("[RelayHandler][Host] Getting a join code.");

            try
            {
                joinCode = await RelayService.Instance.GetJoinCodeAsync(hostAllocation.AllocationId);
                Debug.Log("[RelayHandler][Host] Join code: " + joinCode);
            }
            catch (RelayServiceException ex)
            {
                Debug.LogError(ex.Message + "\n" + ex.StackTrace);
            }
        }

        public async Task<string> HostAllocateAndGetCode(int maxConnections, int regionIdx = 0)
        {
            await HostCreateAllocate(maxConnections, regionIdx);

            BindHost();

            await HostGetJoinCode();

            isHost = true;

            return joinCode;
        }

        public string GetHostAllocationId()
        {
            if (hostAllocation != null)
            {
                return hostAllocation.AllocationId.ToString();
            }
            return string.Empty;
        }

        public string GetJoinCode()
        {
            if (joinCode != null)
            {
                return joinCode;
            }
            return string.Empty;
        }
        #endregion

        #region guest
        private async Task PlayerJoinHost(string joinCode)
        {
            if (String.IsNullOrEmpty(joinCode))
            {
                Debug.LogError("[RelayHandler][Player] Join code is null.");
                return;
            }

            Debug.Log("[RelayHandler][Player] Joining host allocation using join code: " + joinCode);

            try
            {
                playerAllocation = await RelayService.Instance.JoinAllocationAsync(joinCode);
                Debug.Log("[RelayHandler][Player] Allocation ID: " + playerAllocation.AllocationId);
            }
            catch (RelayServiceException ex)
            {
                Debug.LogError(ex.Message + "\n" + ex.StackTrace);
            }
        }

        private void BindPlayer()
        {
            Debug.Log("[RelayHandler][Player] Binding to the Relay server using UTP.");

            // Extract the Relay server data from the Join Allocation response.
            RelayServerData relayServerData = AllocationUtils.ToRelayServerData(playerAllocation, "udp");

            // Create NetworkSettings using the Relay server data.
            NetworkSettings settings = new NetworkSettings();
            settings.WithRelayParameters(ref relayServerData);

            // Create the Player's NetworkDriver from the NetworkSettings object.
            playerDriver = NetworkDriver.Create(settings);

            // Bind to the Relay server.
            if (playerDriver.Bind(NetworkEndpoint.AnyIpv4) != 0)
            {
                Debug.LogError("[RelayHandler][Player] client failed to bind");
            }
            else
            {
                Debug.Log("[RelayHandler][Player] client bound to Relay server");
            }
        }

        private void ConnectPlayerToHost()
        {
            Debug.Log("[RelayHandler][Player] Connecting to Host's client.");

            // Sends a connection request to the Host Player.
            clientConnection = playerDriver.Connect();
        }

        public async Task PlayerJoinHostUsingJoinCode(string joinCode)
        {
            await PlayerJoinHost(joinCode);

            BindPlayer();
            ConnectPlayerToHost();

            isPlayer = true;
        }

        public string GetGuestAllocationId()
        {
            if (playerAllocation != null)
            {
                return playerAllocation.AllocationId.ToString();
            }
            return string.Empty;
        }
        #endregion

        #region Update
        private void UpdateHost()
        {
            // Skip update logic if the Host is not yet bound.
            if (!hostDriver.IsCreated || !hostDriver.Bound)
            {
                return;
            }

            // This keeps the binding to the Relay server alive,
            // preventing it from timing out due to inactivity.
            hostDriver.ScheduleUpdate().Complete();

            // Clean up stale connections.
            for (int i = 0; i < serverConnections.Length; i++)
            {
                if (!serverConnections[i].IsCreated)
                {
                    serverConnections.RemoveAt(i);
                    --i;
                }
            }

            // Accept incoming client connections.
            NetworkConnection incomingConnection;
            while ((incomingConnection = hostDriver.Accept()) != default(NetworkConnection))
            {
                // Adds the requesting Player to the serverConnections list.
                // This also sends a Connect event back the requesting Player,
                // as a means of acknowledging acceptance.
                Debug.Log("[RelayHandler][Host] Host accepted an incoming connection.");
                serverConnections.Add(incomingConnection);
            }

            // Process events from all connections.
            for (int i = 0; i < serverConnections.Length; i++)
            {
                // Resolve event queue.
                NetworkEvent.Type eventType;
                while ((eventType = hostDriver.PopEventForConnection(serverConnections[i], out var stream)) != NetworkEvent.Type.Empty)
                {
                    switch (eventType)
                    {
                        case NetworkEvent.Type.Data:

                            FixedString32Bytes msg = stream.ReadFixedString32();

                            Debug.Log($"[RelayHandler][Host] Received data: {msg}");

                            break;

                        case NetworkEvent.Type.Disconnect:

                            byte firstByteInStream = stream.ReadByte();
                            int disconnectReason = firstByteInStream;

                            DisconnectReason disconnectType = (DisconnectReason)disconnectReason;

                            switch (disconnectType)
                            {
                                case DisconnectReason.Timeout:
                                    {
                                        Debug.Log("[RelayHandler][Host] A client has been disconnected. Reason: client timeout");

                                        string playerId = AuthenticationService.Instance.PlayerId;
                                        LocalLobby localLobby = GlobalSO.GameplayBus.localLobby;
                                        LocalPlayer oppoPlayer = localLobby.LocalPlayers.Find((player) => player.GetId() != playerId);

                                        ActionDispatcher.Dispatch(new RelayInactiveDisconnectAction(oppoPlayer));

                                        break;
                                    }
                                case DisconnectReason.ClosedByRemote:
                                    {
                                        Debug.Log("[RelayHandler][Host] A client has been disconnected. Reason: host timeout");

                                        string playerId = AuthenticationService.Instance.PlayerId;
                                        LocalLobby localLobby = GlobalSO.GameplayBus.localLobby;
                                        LocalPlayer localPlayer = localLobby.LocalPlayers.Find((player) => player.GetId() == playerId);

                                        ActionDispatcher.Dispatch(new RelayInactiveDisconnectAction(localPlayer));

                                        break;
                                    }
                            }

                            serverConnections[i] = default(NetworkConnection);

                            break;
                    }
                }
            }
        }

        private void UpdatePlayer()
        {
            // Skip update logic if the Player is not yet bound.
            if (!playerDriver.IsCreated || !playerDriver.Bound)
            {
                return;
            }

            // This keeps the binding to the Relay server alive,
            // preventing it from timing out due to inactivity.
            playerDriver.ScheduleUpdate().Complete();

            // Resolve event queue.
            NetworkEvent.Type eventType;
            while ((eventType = clientConnection.PopEvent(playerDriver, out var stream)) != NetworkEvent.Type.Empty)
            {
                switch (eventType)
                {
                    case NetworkEvent.Type.Data:

                        FixedString32Bytes msg = stream.ReadFixedString32();

                        Debug.Log($"[RelayHandler][Player] Received data: {msg}");

                        break;

                    case NetworkEvent.Type.Connect:

                        Debug.Log("[RelayHandler][Player] Connected to the host");

                        break;

                    case NetworkEvent.Type.Disconnect:

                        byte firstByteInStream = stream.ReadByte();
                        int disconnectReason = firstByteInStream;

                        DisconnectReason disconnectType = (DisconnectReason)disconnectReason;

                        switch (disconnectType)
                        {
                            case DisconnectReason.Timeout:
                                {
                                    Debug.Log("[RelayHandler][Player] Disconnected from the Host. Reason: host timeout");

                                    string playerId = AuthenticationService.Instance.PlayerId;
                                    LocalLobby localLobby = GlobalSO.GameplayBus.localLobby;
                                    LocalPlayer oppoPlayer = localLobby.LocalPlayers.Find((player) => player.GetId() != playerId);

                                    ActionDispatcher.Dispatch(new RelayInactiveDisconnectAction(oppoPlayer));

                                    break;
                                }
                            case DisconnectReason.ClosedByRemote:
                                {
                                    Debug.Log("[RelayHandler][Player] Disconnected from the Host. Reason: client timeout");

                                    string playerId = AuthenticationService.Instance.PlayerId;
                                    LocalLobby localLobby = GlobalSO.GameplayBus.localLobby;
                                    LocalPlayer localPlayer = localLobby.LocalPlayers.Find((player) => player.GetId() == playerId);

                                    ActionDispatcher.Dispatch(new RelayInactiveDisconnectAction(localPlayer));

                                    break;
                                }
                        }

                        clientConnection = default(NetworkConnection);

                        break;
                }
            }
        }

        public void Update()
        {
            if (isHost)
            {
                UpdateHost(); 
                return;
            }
            if (isPlayer)
            {
                UpdatePlayer(); 
                return;
            }
        }
        #endregion

        #region Send Messages
        public void HostSendMessage(string message)
        {
            if (serverConnections.Length == 0)
            {
                Debug.LogError("[RelayHandler][Host] No players connected to send messages to.");
                return;
            }

            string msg = message;

            for (int i = 0; i < serverConnections.Length; i++)
            {
                if (hostDriver.BeginSend(serverConnections[i], out var writer) == 0)
                {
                    writer.WriteFixedString32(msg);
                    hostDriver.EndSend(writer);
                }
            }
        }

        public void PlayerSendMessage(string message)
        {
            if (!clientConnection.IsCreated)
            {
                Debug.LogError("[RelayHandler][Player] Not connected. No Host client to send message to.");
                return;
            }

            string msg = message;
            if (playerDriver.BeginSend(clientConnection, out var writer) == 0)
            {
                writer.WriteFixedString32(msg);
                playerDriver.EndSend(writer);
            }
        }

        public void SendMessage(string message)
        {
            if (isHost)
            {
                HostSendMessage(message);
                return;
            }

            if (isPlayer)
            {
                PlayerSendMessage(message);
                return;
            }
        }
        #endregion

        #region Disconnect
        private void HostDisconnectAllPlayers()
        {
            if (!isHost)
            {
                return;
            }

            if (serverConnections.Length == 0)
            {
                Debug.LogError("[RelayHandler][Host] No players connected to disconnect.");
                return;
            }

            for (int i = 0; i < serverConnections.Length; i++)
            {
                // This sends a disconnect event to the destination client,
                // letting them know they are disconnected from the Host.
                hostDriver.Disconnect(serverConnections[i]);

                // Here, we set the destination client's NetworkConnection to the default value.
                // It will be recognized in the Host's Update loop as a stale connection, and be removed.
                serverConnections[i] = default(NetworkConnection);
            }
        }

        private void HostStopServer()
        {
            if (!isHost)
            {
                return;
            }

            HostDisconnectAllPlayers();

            if (serverConnections.IsCreated)
            {
                serverConnections.Dispose();
            }

            if (hostDriver.IsCreated)
            {
                hostDriver.Dispose();
                hostDriver = default(NetworkDriver);
            }

            isHost = false;
        }

        private void PlayerDisconnect()
        {
            if (!isPlayer)
            {
                return;
            }

            // This sends a disconnect event to the Host client,
            // letting them know they are disconnecting.
            playerDriver.Disconnect(clientConnection);

            playerDriver.Dispose();
            playerDriver = default(NetworkDriver);

            // Remove the reference to the current connection by overriding it.
            clientConnection = default(NetworkConnection);

            isPlayer = false;
        }

        public void CluelessDisconnect()
        {
            if (isHost)
            {
                Debug.LogError("[RelayHandler][Host] Disconnect.");
                HostStopServer();
                ActionDispatcher.Dispatch(new DisconnectRelayServerAction());
                return;
            }
            if (isPlayer)
            {
                Debug.LogError("[RelayHandler][Player] Disconnect.");
                PlayerDisconnect();
                ActionDispatcher.Dispatch(new DisconnectRelayServerAction());
                return;
            }
        }
        #endregion

        private void OnDestroy()
        {
            // Cleanup objects upon exit
            if (isHost)
            {
                hostDriver.Dispose();
                serverConnections.Dispose();
            }
            else if (isPlayer)
            {
                playerDriver.Dispose();
            }
        }
    }
}
