using System;
using System.Collections;
using System.Collections.Generic;
using Unity.Collections;
using Unity.VisualScripting;
using UnityEngine;
using UnityEngine.UIElements;

namespace Game.UI
{
    public class LoadingBarUXML : Image
    {
        [UnityEngine.Scripting.Preserve]
        public new class UxmlFactory : UxmlFactory<LoadingBarUXML, UxmlTraits> { }

        [UnityEngine.Scripting.Preserve]
        public new class UxmlTraits : VisualElement.UxmlTraits
        {
            UxmlBoolAttributeDescription m_Vertical = new UxmlBoolAttributeDescription
            {
                name = "mode-vertical",
                defaultValue = true
            };

            UxmlBoolAttributeDescription m_Reversed = new UxmlBoolAttributeDescription
            {
                name = "mode-reversed",
                defaultValue = true
            };

            UxmlFloatAttributeDescription _fixedWitdhCornerRatio = new UxmlFloatAttributeDescription
            {
                name = "fixed-width-corner",
                defaultValue = 0.0f
            };

            UxmlFloatAttributeDescription _fixedHeightCornerRatio = new UxmlFloatAttributeDescription
            {
                name = "fixed-height-corner",
                defaultValue = 0.0f
            };

            UxmlFloatAttributeDescription _slashWidthRatio = new UxmlFloatAttributeDescription
            {
                name = "slash-width",
                defaultValue = 0.0f
            };

            UxmlFloatAttributeDescription _slashHeightRatio = new UxmlFloatAttributeDescription
            {
                name = "slash-height",
                defaultValue = 0.0f
            };

            UxmlFloatAttributeDescription _initValue = new UxmlFloatAttributeDescription
            {
                name = "init-value",
                defaultValue = 0.0f
            };


            public override void Init(VisualElement ve, IUxmlAttributes bag, CreationContext cc)
            {
                base.Init(ve, bag, cc);

                LoadingBarUXML item = ve as LoadingBarUXML;

                item.modeVertical = m_Vertical.GetValueFromBag(bag, cc);
                item.modeReversed = m_Reversed.GetValueFromBag(bag, cc);

                item.FixedWidthCorner = _fixedWitdhCornerRatio.GetValueFromBag(bag, cc);
                item.FixedHeightCorner = _fixedHeightCornerRatio.GetValueFromBag(bag, cc);

                item.SlashWidth = _slashWidthRatio.GetValueFromBag(bag, cc);
                item.SlashHeight = _slashHeightRatio.GetValueFromBag(bag, cc);

                item.InitValue= _initValue.GetValueFromBag(bag, cc);

                item.InitUI(item.modeVertical, item.modeReversed);
            }
        }

        public VisualElement totalBGMasking;
        public VisualElement totalBGMasked;
        public VisualElement Masking;
        public VisualElement Masked;

        private VisualElement _rectTotal;
        private VisualElement _rectTotal2;
        private VisualElement _rect;
        private VisualElement _rect2;

        private VisualElement _slash;
        private VisualElement _slashBG;
        private VisualElement _maskSlashBG;

        bool modeVertical { get; set; }
        bool modeReversed { get; set; }

        float FixedWidthCorner { get; set; }
        float FixedHeightCorner { get; set; }

        float SlashWidth { get; set; }
        float SlashHeight { get; set; }

        float InitValue { get; set; }

        private const string stylesResource = "GameUI/Styles/SpinBarUIStyleSheet";
        private const string ussFieldName_Horizontal = "container_Horizontal";
        private const string ussFieldNameTotalBGMasking_Horizontal = "Total_Masking_Horizontal";
        private const string ussFieldNameTotalBGMasked_Horizontal = "Total_Masked_Horizontal";
        private const string ussFieldUpgradableBGMasking_Horizontal = "Upgradable_Masking_Horizontal";
        private const string ussFieldUpgradableBGMasked_Horizontal = "Upgradable_Masked_Horizontal";
        private const string ussFieldNameMasking_Horizontal = "Masking_Horizontal";
        private const string ussFieldNameMasked_Horizontal = "Masked_Horizontal";

        private const string ussFieldName_Horizontal_Reverse = "container_Horizontal_Reverse";
        private const string ussFieldNameTotalBGMasking_Horizontal_Reverse = "Total_Masking_Horizontal_Reverse";
        private const string ussFieldNameTotalBGMasked_Horizontal_Reverse = "Total_Masked_Horizontal_Reverse";
        private const string ussFieldNameMasking_Horizontal_Reverse = "Masking_Horizontal_Reverse";
        private const string ussFieldNameMasked_Horizontal_Reverse = "Masked_Horizontal_Reverse";

        private const string ussFieldNameAnimation = "Loading_Animation";


        private float _currentValue;

        public LoadingBarUXML()
        {
            //InitUI(modeVertical, modeReversed);
        }

        public LoadingBarUXML(bool isVertical, bool isReverse)
        {
            InitUI(isVertical, isReverse);
        }

        private void InitUI(bool isVertical, bool isReverse)
        {
            AddToClassList(ussFieldName_Horizontal);

            totalBGMasking = new VisualElement();
            totalBGMasking.name = "TotalBG Masking";
            totalBGMasking.AddToClassList(ussFieldNameTotalBGMasking_Horizontal);
            Add(totalBGMasking);

            totalBGMasking.style.flexDirection = FlexDirection.Row;
            totalBGMasking.style.position = Position.Absolute;

            _rectTotal = new VisualElement();
            _rectTotal.name = "RectTotal";
            _rectTotal.AddToClassList("TotalBG_Rect");
            totalBGMasking.Add(_rectTotal);

            totalBGMasked = new VisualElement();
            totalBGMasked.name = "TotalBG Masked";
            totalBGMasked.AddToClassList(ussFieldNameTotalBGMasked_Horizontal);
            totalBGMasking.Add(totalBGMasked);

            _rectTotal2 = new VisualElement();
            _rectTotal2.name = "RectTotal2";
            _rectTotal2.AddToClassList("TotalBG_Rect2");
            totalBGMasking.Add(_rectTotal2);

            Masking = new VisualElement();
            Masking.name = "Masking";
            Masking.AddToClassList(ussFieldNameMasking_Horizontal);
            Add(Masking);

            Masking.style.flexDirection = FlexDirection.Row;
            Masking.style.position = Position.Absolute;

            _rect = new VisualElement();
            _rect.name = "Rect";
            _rect.AddToClassList("Masking_Rect");
            Masking.Add(_rect);

            Masked = new VisualElement();
            Masked.name = "Masked";
            Masked.AddToClassList(ussFieldNameMasked_Horizontal);
            Masking.Add(Masked);

            _rect2 = new VisualElement();
            _rect2.name = "Rect2";
            _rect2.AddToClassList("Masking_Rect2");
            Masking.Add(_rect2);

            _maskSlashBG = new VisualElement();
            _maskSlashBG.style.position = Position.Absolute;
            _maskSlashBG.style.height = Length.Percent(100);
            _maskSlashBG.style.width = Length.Percent(100);
            _maskSlashBG.style.overflow = Overflow.Hidden;

            _maskSlashBG.name = "MaskSlashBG";
            _maskSlashBG.AddToClassList("MaskSlashBG");
            _rect2.Add(_maskSlashBG);


            _slashBG = new VisualElement();
            _slashBG.style.position = Position.Absolute;
            _slashBG.style.height = Length.Percent(100);

            _slashBG.name = "SlashBG";
            _slashBG.AddToClassList("SlashBG");
            _maskSlashBG.Add(_slashBG);

            _slash = new VisualElement();
            _slash.style.position = Position.Absolute;
            _slash.style.height = Length.Percent(100);

            _slash.name = "Slash";
            _slash.AddToClassList("Slash");
            _rect2.Add(_slash);


            this.RegisterCallback<GeometryChangedEvent>(ResolveStyle);

            _currentValue = Mathf.Clamp(InitValue, 0f, 1f);

            ResolveStyle(null);
            SetupYoyo(this);
        }

        private void Init(bool isVertical, bool isReverse)
        {
            modeVertical = isVertical;
            modeReversed = isReverse;
        }

        private void ResolveStyle(GeometryChangedEvent evt)
        {
            totalBGMasking.style.width = resolvedStyle.width;
            Masking.style.width = resolvedStyle.width;

            totalBGMasked.style.height = resolvedStyle.height;
            Masked.style.height = resolvedStyle.height;

            var rectW = resolvedStyle.height * FixedWidthCorner / FixedHeightCorner;
            _rectTotal.style.width = rectW;
            _rect.style.width = rectW;
            _rectTotal2.style.width = rectW;
            _rect2.style.width = rectW;

            var realWidth = resolvedStyle.width - 2 * rectW;
            totalBGMasked.style.width = realWidth;
            Masked.style.width = realWidth * _currentValue;

            _slash.style.width = resolvedStyle.height * SlashWidth / SlashHeight;
            _slashBG.style.width = _slash.style.width;
        }

        public void SetValue(float currentValue)
        {
            currentValue = Mathf.Clamp(currentValue, 0f, 1f);

            var isAnimating = _currentValue < currentValue;

            if (isAnimating)
            {
                Masked.AddToClassList(ussFieldNameAnimation);
            }
            else
            {
                Masked.RemoveFromClassList(ussFieldNameAnimation);
            }

            _currentValue = currentValue;

            if (_currentValue <= InitValue)
            {
                Masking.style.display = DisplayStyle.None;
                return;
            }
            else
            {
                Masking.style.display = DisplayStyle.Flex;
            }

            if (Mathf.RoundToInt(_currentValue * 100) == 100)
            {
                _currentValue = 1f;
            }

            if (_currentValue == 1f)
            {
                _slash.style.display = DisplayStyle.None;
                _slashBG.style.display = DisplayStyle.None;
            }
            else
            {
                _slash.style.display = DisplayStyle.Flex;
                _slashBG.style.display = DisplayStyle.Flex;
            }

            this.schedule.Execute(() =>
            {
                var fakeOldRect = Rect.zero;
                var fakeNewRect = this.layout;

                using var evt = GeometryChangedEvent.GetPooled(fakeOldRect, fakeNewRect);
                evt.target = this.contentContainer;
                this.contentContainer.SendEvent(evt);
            });
        }

        private void SetupYoyo(VisualElement root)
        {
            _slash.RegisterCallback<TransitionEndEvent>(evt => _slash.ToggleInClassList("enlarge-scale-yoyo"));
            // Schedule the first transition 100 milliseconds after the root.schedule.Execute method is called.
            root.schedule.Execute(() => _slash.ToggleInClassList("enlarge-scale-yoyo")).StartingIn(100);
        }
    }
}

