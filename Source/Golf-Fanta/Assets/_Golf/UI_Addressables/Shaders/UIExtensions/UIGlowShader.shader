Shader "Universal Render Pipeline/2D/Sprite-Lit-Default-Glow"
{
    Properties
    {
        _MainTex("Diffuse", 2D) = "white" {}
        _MaskTex("Mask", 2D) = "white" {}
        _NormalMap("Normal Map", 2D) = "bump" {}
        _ZWrite("ZWrite", Float) = 0

        // Glow properties
        _GlowColor ("Glow Color", Color) = (0,1,1,1)
        _GlowPower ("Glow Power", Range(0.5, 5)) = 2
        _GlowIntensity ("Glow Intensity", Range(0, 3)) = 1.5
        _GlowRadius ("Glow Radius", Range(1, 20)) = 10
        _GlowAlphaThreshold ("Glow Alpha Threshold", Range(0, 1)) = 0.01
        _GlowSpeed ("Glow Animation Speed", Range(0, 5)) = 1.0
        _GlowMinMax ("Glow Min/Max (X,Y)", Vector) = (0.6, 1.2, 0, 0)

        // Legacy properties. They're here so that materials using this shader can gracefully fallback to the legacy sprite shader.
        [HideInInspector] _Color("Tint", Color) = (1,1,1,1)
        [HideInInspector] _RendererColor("RendererColor", Color) = (1,1,1,1)
        [HideInInspector] _AlphaTex("External Alpha", 2D) = "white" {}
        [HideInInspector] _EnableExternalAlpha("Enable External Alpha", Float) = 0
    }

    SubShader
    {
        Tags {"Queue" = "Transparent" "RenderType" = "Transparent" "RenderPipeline" = "UniversalPipeline" }

        Blend SrcAlpha OneMinusSrcAlpha, One OneMinusSrcAlpha
        Cull Off
        ZWrite [_ZWrite]
        ZTest Off

        Pass
        {
            Tags { "LightMode" = "Universal2D" }

            HLSLPROGRAM
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/Shaders/2D/Include/Core2D.hlsl"

            #pragma vertex CombinedShapeLightVertex
            #pragma fragment CombinedShapeLightFragment

            #include_with_pragmas "Packages/com.unity.render-pipelines.universal/Shaders/2D/Include/ShapeLightShared.hlsl"

            #pragma multi_compile _ DEBUG_DISPLAY SKINNED_SPRITE

            struct Attributes
            {
                float3 positionOS   : POSITION;
                float4 color        : COLOR;
                float2 uv           : TEXCOORD0;
                UNITY_SKINNED_VERTEX_INPUTS
                UNITY_VERTEX_INPUT_INSTANCE_ID
            };

            struct Varyings
            {
                float4  positionCS  : SV_POSITION;
                half4   color       : COLOR;
                float2  uv          : TEXCOORD0;
                half2   lightingUV  : TEXCOORD1;
                #if defined(DEBUG_DISPLAY)
                float3  positionWS  : TEXCOORD2;
                #endif
                UNITY_VERTEX_OUTPUT_STEREO
            };

            #include "Packages/com.unity.render-pipelines.universal/Shaders/2D/Include/LightingUtility.hlsl"
            #include "Packages/com.unity.render-pipelines.core/ShaderLibrary/DebugMipmapStreamingMacros.hlsl"

            TEXTURE2D(_MainTex);
            SAMPLER(sampler_MainTex);
            UNITY_TEXTURE_STREAMING_DEBUG_VARS_FOR_TEX(_MainTex);

            TEXTURE2D(_MaskTex);
            SAMPLER(sampler_MaskTex);

            // NOTE: Do not ifdef the properties here as SRP batcher can not handle different layouts.
            CBUFFER_START(UnityPerMaterial)
                half4 _Color;
                half4 _GlowColor;
                float _GlowPower;
                float _GlowIntensity;
                float _GlowRadius;
                float _GlowAlphaThreshold;
                float _GlowSpeed;
                float4 _GlowMinMax;
                float4 _MainTex_TexelSize;
            CBUFFER_END

            #if USE_SHAPE_LIGHT_TYPE_0
            SHAPE_LIGHT(0)
            #endif

            #if USE_SHAPE_LIGHT_TYPE_1
            SHAPE_LIGHT(1)
            #endif

            #if USE_SHAPE_LIGHT_TYPE_2
            SHAPE_LIGHT(2)
            #endif

            #if USE_SHAPE_LIGHT_TYPE_3
            SHAPE_LIGHT(3)
            #endif

            Varyings CombinedShapeLightVertex(Attributes v)
            {
                Varyings o = (Varyings)0;
                UNITY_SETUP_INSTANCE_ID(v);
                UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(o);
                UNITY_SKINNED_VERTEX_COMPUTE(v);

                v.positionOS = UnityFlipSprite(v.positionOS, unity_SpriteProps.xy);
                o.positionCS = TransformObjectToHClip(v.positionOS);
                #if defined(DEBUG_DISPLAY)
                o.positionWS = TransformObjectToWorld(v.positionOS);
                #endif
                o.uv = v.uv;
                o.lightingUV = half2(ComputeScreenPos(o.positionCS / o.positionCS.w).xy);

                o.color = v.color * _Color * unity_SpriteColor;
                return o;
            }

            #include "Packages/com.unity.render-pipelines.universal/Shaders/2D/Include/CombinedShapeLightShared.hlsl"

            half4 CombinedShapeLightFragment(Varyings i) : SV_Target
            {
                half4 main = i.color * SAMPLE_TEXTURE2D(_MainTex, sampler_MainTex, i.uv);
                const half4 mask = SAMPLE_TEXTURE2D(_MaskTex, sampler_MaskTex, i.uv);
                
                // Calculate time-based glow intensity
                float timeWave = (sin(_Time.y * _GlowSpeed) + 1.0) * 0.5; // 0 to 1
                float animatedIntensity = lerp(_GlowMinMax.x, _GlowMinMax.y, timeWave) * _GlowIntensity;
                
                // Calculate time-based radius - smaller radius at min intensity
                float radiusScale = lerp(0.3, 1.0, timeWave);
                float animatedRadius = _GlowRadius * radiusScale;
                
                // Find edges by sampling in a wider radius
                float maxAlphaDiff = 0;
                float centerAlpha = main.a;
                
                // Sample in a circular pattern
                int samples = min(20, animatedRadius);
                for (int j = 1; j <= samples; j++) {
                    float angle = j * (6.28318 / samples); // 2π / samples
                    float radius = animatedRadius * _MainTex_TexelSize.xy;
                    
                    // Sample at different distances from center
                    for (int d = 1; d <= 3; d++) {
                        float2 offset = float2(cos(angle), sin(angle)) * radius * (d / 3.0);
                        float4 sample = SAMPLE_TEXTURE2D(_MainTex, sampler_MainTex, i.uv + offset);
                        maxAlphaDiff = max(maxAlphaDiff, abs(centerAlpha - sample.a));
                    }
                }
                
                // Apply glow based on alpha difference - but keep the pure glow color
                float edgeFactor = pow(maxAlphaDiff, _GlowPower) * animatedIntensity;
                
                // Store original color
                half4 originalMain = main;
                
                // Only apply glow to the edges (where alpha differences were detected)
                // This ensures the inner parts remain unchanged
                if (edgeFactor > 0.05 && maxAlphaDiff > 0.1) {
                    // Calculate a blend factor that preserves the glow color
                    float colorBlend = min(0.8, edgeFactor * lerp(0.1, 0.6, timeWave));
                    
                    // Apply the glow color directly - no adding which causes white
                    main.rgb = lerp(main.rgb, _GlowColor.rgb, colorBlend);
                    
                    // For stronger edges, enhance the glow color but don't add (which causes white)
                    if (edgeFactor > 0.3) {
                        // Increase the saturation of the glow at edges
                        main.rgb = lerp(main.rgb, _GlowColor.rgb, min(0.9, edgeFactor * 0.7));
                    }
                    
                    // Calculate glow alpha - ensure it's visible even in low alpha areas
                    main.a = max(main.a, edgeFactor * _GlowColor.a * lerp(0.2, 1.0, timeWave));
                }
                
                // Only clip if below threshold
                clip(main.a - _GlowAlphaThreshold);
                
                SurfaceData2D surfaceData;
                InputData2D inputData;

                InitializeSurfaceData(main.rgb, main.a, mask, surfaceData);
                InitializeInputData(i.uv, i.lightingUV, inputData);

                SETUP_DEBUG_TEXTURE_DATA_2D_NO_TS(inputData, i.positionWS, i.positionCS, _MainTex);

                return CombinedShapeLightShared(surfaceData, inputData);
            }
            ENDHLSL
        }

        Pass
        {
            ZWrite Off

            Tags { "LightMode" = "NormalsRendering"}

            HLSLPROGRAM
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/Shaders/2D/Include/Core2D.hlsl"

            #pragma vertex NormalsRenderingVertex
            #pragma fragment NormalsRenderingFragment

            #pragma multi_compile _ SKINNED_SPRITE

            struct Attributes
            {
                float3 positionOS   : POSITION;
                float4 color        : COLOR;
                float2 uv           : TEXCOORD0;
                float4 tangent      : TANGENT;
                UNITY_SKINNED_VERTEX_INPUTS
                UNITY_VERTEX_INPUT_INSTANCE_ID
            };

            struct Varyings
            {
                float4  positionCS      : SV_POSITION;
                half4   color           : COLOR;
                float2  uv              : TEXCOORD0;
                half3   normalWS        : TEXCOORD1;
                half3   tangentWS       : TEXCOORD2;
                half3   bitangentWS     : TEXCOORD3;
                UNITY_VERTEX_OUTPUT_STEREO
            };

            TEXTURE2D(_MainTex);
            SAMPLER(sampler_MainTex);
            TEXTURE2D(_NormalMap);
            SAMPLER(sampler_NormalMap);

            // NOTE: Do not ifdef the properties here as SRP batcher can not handle different layouts.
            CBUFFER_START( UnityPerMaterial )
                half4 _Color;
            CBUFFER_END

            Varyings NormalsRenderingVertex(Attributes attributes)
            {
                Varyings o = (Varyings)0;
                UNITY_SETUP_INSTANCE_ID(attributes);
                UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(o);
                UNITY_SKINNED_VERTEX_COMPUTE(attributes);

                attributes.positionOS = UnityFlipSprite(attributes.positionOS, unity_SpriteProps.xy);
                o.positionCS = TransformObjectToHClip(attributes.positionOS);
                o.uv = attributes.uv;
                o.color = attributes.color;
                o.normalWS = -GetViewForwardDir();
                o.tangentWS = TransformObjectToWorldDir(attributes.tangent.xyz);
                o.bitangentWS = cross(o.normalWS, o.tangentWS) * attributes.tangent.w;
                return o;
            }

            #include "Packages/com.unity.render-pipelines.universal/Shaders/2D/Include/NormalsRenderingShared.hlsl"

            half4 NormalsRenderingFragment(Varyings i) : SV_Target
            {
                const half4 mainTex = i.color * SAMPLE_TEXTURE2D(_MainTex, sampler_MainTex, i.uv);
                const half3 normalTS = UnpackNormal(SAMPLE_TEXTURE2D(_NormalMap, sampler_NormalMap, i.uv));

                return NormalsRenderingShared(mainTex, normalTS, i.tangentWS.xyz, i.bitangentWS.xyz, i.normalWS.xyz);
            }
            ENDHLSL
        }

        Pass
        {
            Tags { "LightMode" = "UniversalForward" "Queue"="Transparent" "RenderType"="Transparent"}

            HLSLPROGRAM
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/Shaders/2D/Include/Core2D.hlsl"
            #include "Packages/com.unity.render-pipelines.core/ShaderLibrary/DebugMipmapStreamingMacros.hlsl"
            #if defined(DEBUG_DISPLAY)
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Debug/Debugging2D.hlsl"
            #endif

            #pragma vertex UnlitVertex
            #pragma fragment UnlitFragment

            #pragma multi_compile _ DEBUG_DISPLAY SKINNED_SPRITE

            struct Attributes
            {
                float3 positionOS   : POSITION;
                float4 color        : COLOR;
                float2 uv           : TEXCOORD0;
                UNITY_SKINNED_VERTEX_INPUTS
                UNITY_VERTEX_INPUT_INSTANCE_ID
            };

            struct Varyings
            {
                float4  positionCS      : SV_POSITION;
                float4  color           : COLOR;
                float2  uv              : TEXCOORD0;
                #if defined(DEBUG_DISPLAY)
                float3  positionWS  : TEXCOORD2;
                #endif
                UNITY_VERTEX_OUTPUT_STEREO
            };

            TEXTURE2D(_MainTex);
            SAMPLER(sampler_MainTex);
            UNITY_TEXTURE_STREAMING_DEBUG_VARS_FOR_TEX(_MainTex);

            // NOTE: Do not ifdef the properties here as SRP batcher can not handle different layouts.
            CBUFFER_START( UnityPerMaterial )
                half4 _Color;
                half4 _GlowColor;
                float _GlowPower;
                float _GlowIntensity;
                float _GlowRadius;
                float _GlowAlphaThreshold;
                float _GlowSpeed;
                float4 _GlowMinMax;
                float4 _MainTex_TexelSize;
            CBUFFER_END

            Varyings UnlitVertex(Attributes attributes)
            {
                Varyings o = (Varyings)0;
                UNITY_SETUP_INSTANCE_ID(attributes);
                UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(o);
                UNITY_SKINNED_VERTEX_COMPUTE(attributes);

                attributes.positionOS = UnityFlipSprite( attributes.positionOS, unity_SpriteProps.xy);
                o.positionCS = TransformObjectToHClip(attributes.positionOS);
                #if defined(DEBUG_DISPLAY)
                o.positionWS = TransformObjectToWorld(attributes.positionOS);
                #endif
                o.uv = attributes.uv;
                o.color = attributes.color * _Color * unity_SpriteColor;
                return o;
            }

            float4 UnlitFragment(Varyings i) : SV_Target
            {
                float4 mainTex = i.color * SAMPLE_TEXTURE2D(_MainTex, sampler_MainTex, i.uv);
                
                // Calculate time-based glow intensity
                float timeWave = (sin(_Time.y * _GlowSpeed) + 1.0) * 0.5; // 0 to 1
                float animatedIntensity = lerp(_GlowMinMax.x, _GlowMinMax.y, timeWave) * _GlowIntensity;
                
                // Calculate time-based radius - smaller radius at min intensity
                float radiusScale = lerp(0.3, 1.0, timeWave);
                float animatedRadius = _GlowRadius * radiusScale;
                
                // Find edges by sampling in a wider radius
                float maxAlphaDiff = 0;
                float centerAlpha = mainTex.a;
                
                // Sample in a circular pattern
                int samples = min(20, animatedRadius);
                for (int j = 1; j <= samples; j++) {
                    float angle = j * (6.28318 / samples); // 2π / samples
                    float radius = animatedRadius * _MainTex_TexelSize.xy;
                    
                    // Sample at different distances from center
                    for (int d = 1; d <= 3; d++) {
                        float2 offset = float2(cos(angle), sin(angle)) * radius * (d / 3.0);
                        float4 sample = SAMPLE_TEXTURE2D(_MainTex, sampler_MainTex, i.uv + offset);
                        maxAlphaDiff = max(maxAlphaDiff, abs(centerAlpha - sample.a));
                    }
                }
                
                // Apply glow based on alpha difference - but keep the pure glow color
                float edgeFactor = pow(maxAlphaDiff, _GlowPower) * animatedIntensity;
                
                // Store original color
                half4 originalMainTex = mainTex;
                
                // Only apply glow to the edges (where alpha differences were detected)
                // This ensures the inner parts remain unchanged
                if (edgeFactor > 0.05 && maxAlphaDiff > 0.1) {
                    // Calculate a blend factor that preserves the glow color
                    float colorBlend = min(0.8, edgeFactor * lerp(0.1, 0.6, timeWave));
                    
                    // Apply the glow color directly - no adding which causes white
                    mainTex.rgb = lerp(mainTex.rgb, _GlowColor.rgb, colorBlend);
                    
                    // For stronger edges, enhance the glow color but don't add (which causes white)
                    if (edgeFactor > 0.3) {
                        // Increase the saturation of the glow at edges
                        mainTex.rgb = lerp(mainTex.rgb, _GlowColor.rgb, min(0.9, edgeFactor * 0.7));
                    }
                    
                    // Calculate glow alpha - ensure it's visible even in low alpha areas
                    mainTex.a = max(mainTex.a, edgeFactor * _GlowColor.a * lerp(0.2, 1.0, timeWave));
                }
                
                // Only clip if below threshold
                clip(mainTex.a - _GlowAlphaThreshold);

                #if defined(DEBUG_DISPLAY)
                SurfaceData2D surfaceData;
                InputData2D inputData;
                half4 debugColor = 0;

                InitializeSurfaceData(mainTex.rgb, mainTex.a, surfaceData);
                InitializeInputData(i.uv, inputData);
                SETUP_DEBUG_TEXTURE_DATA_2D_NO_TS(inputData, i.positionWS, i.positionCS, _MainTex);

                if(CanDebugOverrideOutputColor(surfaceData, inputData, debugColor))
                {
                    return debugColor;
                }
                #endif

                return mainTex;
            }
            ENDHLSL
        }
    }

    Fallback "Sprites/Default"
}
