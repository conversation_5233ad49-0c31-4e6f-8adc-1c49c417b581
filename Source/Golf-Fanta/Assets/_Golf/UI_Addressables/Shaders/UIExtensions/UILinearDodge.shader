// Upgrade NOTE: replaced 'mul(UNITY_MATRIX_MVP,*)' with 'UnityObjectToClipPos(*)'

Shader "AddressablesClones/UIExtensions/UILinearDodge"
{
	Properties
	{
		[PerRendererData] _MainTex ("Sprite Texture", 2D) = "white" {}
		_Color ("Tint", Color) = (1,1,1,1)
		
		_StencilComp ("Stencil Comparison", Float) = 8
		_Stencil ("Stencil ID", Float) = 0
		_StencilOp ("Stencil Operation", Float) = 0
		_StencilWriteMask ("Stencil Write Mask", Float) = 255
		_StencilReadMask ("Stencil Read Mask", Float) = 255

		_ColorMask ("Color Mask", Float) = 15
		
		_GlowColor ("Glow Color", Color) = (0,1,1,1)
		_GlowPower ("Glow Power", Range(0.5, 5)) = 2
		_GlowIntensity ("Glow Intensity", Range(0, 3)) = 1.5
		_GlowRadius ("Glow Radius", Range(1, 20)) = 10
		_GlowAlphaThreshold ("Glow Alpha Threshold", Range(0, 1)) = 0.01
		_GlowSpeed ("Glow Animation Speed", Range(0, 5)) = 1.0
		_GlowMinMax ("Glow Min/Max (X,Y)", Vector) = (0.6, 1.2, 0, 0)
	}

	SubShader
	{
		Tags
		{ 
			"Queue"="Transparent" 
			"IgnoreProjector"="True" 
			"RenderType"="Transparent" 
			"PreviewType"="Plane"
			"CanUseSpriteAtlas"="True"
		}
		
		Stencil
		{
			Ref [_Stencil]
			Comp [_StencilComp]
			Pass [_StencilOp] 
			ReadMask [_StencilReadMask]
			WriteMask [_StencilWriteMask]
		}

		Cull Off
		Lighting Off
		ZWrite Off
		ZTest [unity_GUIZTestMode]
		Fog { Mode Off }
		BlendOp Add
		Blend SrcAlpha One, One Zero
		ColorMask [_ColorMask]

		Pass
		{
		CGPROGRAM
			#pragma vertex vert
			#pragma fragment frag
			#include "UnityCG.cginc"
			
			struct appdata_t
			{
				float4 vertex   : POSITION;
				float4 color    : COLOR;
				float2 texcoord : TEXCOORD0;
			};

			struct v2f
			{
				float4 vertex   : SV_POSITION;
				fixed4 color    : COLOR;
				half2 texcoord  : TEXCOORD0;
			};
			
			fixed4 _Color;
			fixed4 _GlowColor;
			float _GlowPower;
			float _GlowIntensity;
			float _GlowRadius;
			float _GlowAlphaThreshold;
			float _GlowSpeed;
			float4 _GlowMinMax;

			v2f vert(appdata_t IN)
			{
				v2f OUT;
				OUT.vertex = UnityObjectToClipPos(IN.vertex);
				OUT.texcoord = IN.texcoord;
#ifdef UNITY_HALF_TEXEL_OFFSET
				OUT.vertex.xy += (_ScreenParams.zw-1.0)*float2(-1,1);
#endif
				OUT.color = IN.color * _Color;
				return OUT;
			}

			sampler2D _MainTex;
			float4 _MainTex_TexelSize;

			float4 SampleWithAlpha(float2 uv) {
				return tex2D(_MainTex, uv);
			}

			fixed4 frag(v2f IN) : SV_Target
			{
				half4 color = tex2D(_MainTex, IN.texcoord) * IN.color;
				
				// Calculate time-based glow intensity
				float timeWave = (sin(_Time.y * _GlowSpeed) + 1.0) * 0.5; // 0 to 1
				float animatedIntensity = lerp(_GlowMinMax.x, _GlowMinMax.y, timeWave) * _GlowIntensity;
				
				// Calculate time-based radius - smaller radius at min intensity
				float radiusScale = lerp(0.3, 1.0, timeWave);
				float animatedRadius = _GlowRadius * radiusScale;
				
				// Find edges by sampling in a wider radius
				float maxAlphaDiff = 0;
				float centerAlpha = color.a;
				
				// Sample in a circular pattern
				int samples = min(20, animatedRadius);
				for (int i = 1; i <= samples; i++) {
					float angle = i * (6.28318 / samples); // 2π / samples
					float radius = animatedRadius * _MainTex_TexelSize.xy;
					
					// Sample at different distances from center
					for (int d = 1; d <= 3; d++) {
						float2 offset = float2(cos(angle), sin(angle)) * radius * (d / 3.0);
						float4 sample = SampleWithAlpha(IN.texcoord + offset);
						maxAlphaDiff = max(maxAlphaDiff, abs(centerAlpha - sample.a));
					}
				}
				
				// Apply glow based on alpha difference - but keep the pure glow color
				float edgeFactor = pow(maxAlphaDiff, _GlowPower) * animatedIntensity;
				
				// Calculate glow alpha - ensure it's visible even in low alpha areas
				float glowAlpha = max(color.a, edgeFactor * _GlowColor.a * lerp(0.2, 1.0, timeWave));
				
				// Store original color
				half4 originalColor = color;
				
				// Only apply glow to the edges (where alpha differences were detected)
				// This ensures the inner parts remain unchanged
				if (edgeFactor > 0.05) {
					// Calculate a blend factor that preserves the glow color
					float colorBlend = min(0.8, edgeFactor * lerp(0.1, 0.6, timeWave));
					
					// Only apply glow to pixels near edges (where maxAlphaDiff is significant)
					// This keeps the inner parts of the texture unchanged
					if (maxAlphaDiff > 0.1) {
						// Apply the glow color directly - no adding which causes white
						color.rgb = lerp(color.rgb, _GlowColor.rgb, colorBlend);
						
						// For stronger edges, enhance the glow color but don't add (which causes white)
						if (edgeFactor > 0.3) {
							// Increase the saturation of the glow at edges
							color.rgb = lerp(color.rgb, _GlowColor.rgb, min(0.9, edgeFactor * 0.7));
						}
					}
				}
				
				// Apply the new alpha value
				color.a = glowAlpha;
				
				// Apply alpha to RGB for proper blending
				color.rgb *= color.a;
				
				// Only clip if below threshold
				clip(color.a - _GlowAlphaThreshold);
				return color;
			}
		ENDCG
		}
	}
}
